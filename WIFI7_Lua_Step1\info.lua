#!/usr/bin/lua

-- Copyright 2023 xiayan
-- Licensed to the public under the Apache License 2.0.

-- 模块加载：使用require加载所需的Lua模块，如uci, http, sys, 和 jsonc。

-- 模块定义：用local M = {}来定义一个模块表，并将所有的函数和数据添加到这个表中。

-- 返回模块：在脚本的最后，通过return M来导出模块。

-- 函数命名：去掉了模块名前缀，因为这些函数都是模块内部的。

-- 错误处理：保持了原有的错误处理逻辑。

-- 配置文件操作：使用了UCI库来操作配置文件，确保了与OpenWRT系统的兼容性。

-- 网络服务重启：改用了更明确的命令来重启网络服务。

-- 引入模块
local cjson = require("cjson.safe")
local sys = require("luci.sys")
local uci = require("luci.model.uci").cursor()
local http = require("luci.http")
local log_file = "/tmp/info.log" -- 日志文件路径

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end



-- 检查请求格式
if not requestData.version or not requestData.sid or not requestData.module or not requestData.api then
    local error_message = "Invalid request format"
    write_log(error_message)
    io.write(cjson.encode({
        module = "user",
        version = "1.0",
        errcode = 3,
        result = { message = error_message }
    }))
    return
end

-- 路由到具体逻辑
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 获取 POST 数据长度
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""

    -- 读取 POST 数据
    if POSTLength > 0 then
        POST = io.read(POSTLength)
        write_log("Received POST data: " .. (POST or "nil"))
    else
        write_log("No POST data received or CONTENT_LENGTH is 0")
    end

    -- 确保读取成功
    if not POST or POST == "" then
        local error_message = "Failed to retrieve POST data"
        write_log(error_message)
        io.write(cjson.encode({
            module = "user",
            version = "1.0",
            errcode = 1,
            result = { message = error_message }
        }))
        return
    end

    -- 解析 POST 数据为 JSON
    local requestData = cjson.decode(POST)
    if not requestData then
        local error_message = "Invalid JSON input"
        write_log(error_message)
        io.write(cjson.encode({
            module = "user",
            version = "1.0",
            errcode = 2,
            result = { message = error_message }
        }))
        return
    end

    write_log("Parsed request data: " .. cjson.encode(requestData))

    if requestData.api == "info" then
        write_log("Calling handle_info with data: " .. cjson.encode(requestData))
        handle_info(requestData)
    else
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "user",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
    end
end

-- 获取用户信息
function handle_info(data)
    local token = data.param and data.param.token or ""

    -- 这里可以添加验证 token 的逻辑，如果 token 无效或过期，则返回错误信息
    if token == "" then
        write_log("Token is missing for info")
        io.write(cjson.encode({
            module = "user",
            version = "1.0",
            api = "info",
            errcode = 5,
            sid = data.sid,
            result = { message = "Token is required for user info" }
        }))
        return
    end

    -- 获取设备运行模式
        -- 获取设备运行模式
    local mode = uci:get("system", "nhx", "mode") or "ap"

    -- 获取设备型号
    local model = "IAP3500-E11-LV"  -- 默认型号
    local model_file = io.popen("cat /etc/config/system | grep 'option model'")
    local model_line = model_file:read("*l")
    model_file:close()

    if model_line then
        local model_from_config = model_line:match("option model '(.-)'")
        if model_from_config then
            model = model_from_config
        end
    end

    -- 根据模式设置角色
    local roles = {}
    if mode == "router" then
        roles = {"router"}
    elseif mode == "bridge" then
        roles = {"bridge"}
    else
        roles = {"ap"}  -- 默认 AP 模式
    end

    -- 构建用户信息
    local user_info = {
        name = model,
        roles = roles,
        mode = mode
    }

    write_log("User info retrieved successfully: " .. cjson.encode(user_info))
    io.write(cjson.encode({
        module = "user",
        version = "1.0",
        api = "info",
        errcode = 0,
        sid = data.sid,
        result = user_info
    }))
end

-- 显式调用入口函数
local function run()
    write_log("User Info API started")
    route_api()
    write_log("User Info API finished")
end

run()
