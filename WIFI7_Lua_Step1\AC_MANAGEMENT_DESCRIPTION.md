# WiFi7 AC管理快速参考卡片

## 基础信息

**目标设备:** IAP3500-E11  
**IP地址:** *************  
**端口:** 8899 (UDP)  
**协议:** 自定义AC协议  
**数据格式:** 16进制字节流  

## 数据包格式

```
+----------+--------+--------+--------+--------+--------+
| Header   | Length | SeqNum | CmdID  |  Data  | CRC32  |
+----------+--------+--------+--------+--------+--------+
| 5A5A5A5A |  2字节  |  2字节  |  2字节  |  变长   |  4字节  |
```

## 常用命令ID

| 命令ID | 功能 | 方向 |
|--------|------|------|
| 0x0001 | 设备发现 | AC→AP |
| 0x0004 | 状态查询 | AC→AP |
| 0x0003 | 心跳包 | 双向 |
| 0x0020 | 2.4G配置 | AC→AP |
| 0x0021 | 5G配置 | AC→AP |
| 0x0022 | 6G配置 | AC→AP |
| 0x0010 | AP模式 | AC→AP |
| 0x0011 | 路由模式 | AC→AP |
| 0x0012 | 桥接模式 | AC→AP |
| 0x0050 | 设备重启 | AC→AP |

## 快速测试数据

### 1. 设备发现 (广播到***************:8899)
```
5A5A5A5A004C00010001...
```

### 2. 状态查询
```
5A5A5A5A005800020004...
```

### 3. 2.4G无线配置
```
5A5A5A5A00B800030020...
```

### 4. 5G无线配置
```
5A5A5A5A00C400040021...
```

### 5. AP模式配置
```
5A5A5A5A00E800050010...
```

### 6. 设备重启
```
5A5A5A5A002C000C0050...
```

## 网络助手配置

**基础设置:**
```
协议: UDP
目标IP: *************
端口: 8899
格式: 16进制
超时: 5000ms
```

**广播设置:**
```
目标IP: ***************
端口: 8899
用于: 设备发现
```

## JSON数据模板

### 设备发现
```json
{
    "action": "discover",
    "ac_mac": "aa:bb:cc:dd:ee:ff",
    "timestamp": 1720339200
}
```

### 状态查询
```json
{
    "action": "get_status",
    "categories": ["system", "network", "wireless"]
}
```

### 2.4G配置
```json
{
    "wifi_2g": {
        "enabled": true,
        "ssid": "WiFi7-2.4G",
        "password": "password123",
        "channel": 6,
        "bandwidth": "40MHz",
        "tx_power": 20
    }
}
```

### 5G配置
```json
{
    "wifi_5g": {
        "enabled": true,
        "ssid": "WiFi7-5G",
        "password": "password123",
        "channel": 36,
        "bandwidth": "80MHz",
        "tx_power": 23
    }
}
```

### AP模式
```json
{
    "mode": "ap",
    "lan_config": {
        "ip": "***********",
        "netmask": "*************",
        "dhcp_enabled": true
    }
}
```

## 错误码参考

| 错误码 | 含义 |
|--------|------|
| 0x0000 | 成功 |
| 0x0001 | 无效数据包 |
| 0x0002 | CRC校验失败 |
| 0x0003 | 未知命令 |
| 0x0005 | 配置错误 |
| 0x0006 | 系统错误 |

## 调试命令

### 设备端调试
```bash
# 检查AC服务
ps | grep ac_main_loop

# 检查端口监听
netstat -ulnp | grep 8899

# 查看日志
logread | grep -E "ac_|AC_"

# 实时日志
logread -f | grep ac_main_loop
```

### PC端调试
```bash
# 测试连通性
ping *************

# 测试UDP端口
nc -u ************* 8899

# 网络抓包
tcpdump -i any -X port 8899
```

## 测试流程

### 基础测试
1. **网络连通性** → ping *************
2. **设备发现** → 广播发现包
3. **状态查询** → 获取设备状态
4. **心跳测试** → 发送心跳包

### 配置测试
1. **无线配置** → 2.4G/5G/6G配置
2. **网络模式** → AP/路由/桥接模式
3. **高级功能** → VLAN/QoS配置
4. **系统管理** → 重启/恢复出厂

### 验证测试
1. **配置生效** → 状态查询验证
2. **功能正常** → 实际功能测试
3. **错误处理** → 异常数据包测试
4. **性能测试** → 响应时间/并发测试

## 常见问题

### 无响应
- 检查网络连通性
- 检查防火墙设置
- 检查AC服务状态
- 验证数据包格式

### 配置失败
- 检查JSON格式
- 验证参数范围
- 查看错误日志
- 检查权限设置

### 解析错误
- 验证CRC32校验
- 检查包长度
- 确认字符编码
- 调试数据格式

## 性能指标

**正常指标:**
- 响应时间: < 100ms
- 成功率: > 99%
- 并发处理: > 10个/秒
- 内存使用: < 50MB

**异常指标:**
- 响应时间: > 1000ms
- 成功率: < 90%
- CPU使用: > 80%
- 内存泄漏: 持续增长

## 联系支持

**开发团队:**
- 协议问题: 协议开发组
- 网络问题: 网络调试组
- 配置问题: 系统配置组
- 性能问题: 性能优化组

**紧急联系:**
- 严重Bug: 立即联系开发负责人
- 安全问题: 联系安全团队
- 生产问题: 联系运维团队

---

**版本:** v1.0  
**更新:** 2025-07-07  
**适用:** WiFi7 AC管理功能测试
