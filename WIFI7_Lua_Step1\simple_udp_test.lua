#!/usr/bin/lua

-- 简单UDP测试 - 使用不同端口避免冲突

print("=== 简单UDP接收测试 ===")
print("")

local ac_comm = require("ac_comm")

-- 使用端口50010避免与50001冲突
local test_port = 50010

print("1. 初始化UDP监听端口: " .. test_port)
local ok, err = ac_comm.init_udp(test_port)
if not ok then
    print("❌ 初始化失败: " .. tostring(err))
    return
end
print("✅ 初始化成功")
print("")

print("2. 检查端口监听状态:")
os.execute("netstat -ulnp | grep " .. test_port)
print("")

print("3. 等待接收数据 (10秒超时):")
print("请使用网络助手发送数据:")
print("  - 目标IP: 设备IP")
print("  - 目标端口: " .. test_port)
print("  - 数据格式: HEX")
print("  - 发送数据: 41")
print("")
print("开始监听...")

local data, ip, port = ac_comm.recv(10)
if data then
    print("✅ 成功接收数据!")
    print("  来源: " .. tostring(ip) .. ":" .. tostring(port))
    print("  长度: " .. #data .. " 字节")
    print("  原始数据: " .. data)
    print("  十六进制: " .. (data:gsub('.', function(c) return string.format('%02X ', string.byte(c)) end)))
    print("")
    print("🎉 网络通信正常!")
else
    print("❌ 没有接收到数据")
    print("  错误: " .. tostring(ip))
    print("")
    print("可能的原因:")
    print("1. 网络助手没有发送数据")
    print("2. IP地址不正确")
    print("3. 端口号不正确")
    print("4. 防火墙阻止")
    print("5. 网络连接问题")
end

print("")
print("4. 清理资源...")
ac_comm.close_udp()
print("测试完成")
print("")

if data then
    print("=== 结论 ===")
    print("✅ ac_comm.so库工作正常")
    print("✅ 网络连接正常")
    print("问题可能在于:")
    print("1. ac_main_loop.lua的逻辑")
    print("2. 端口50001的冲突")
    print("3. ac_comm_wrap的包装层")
else
    print("=== 结论 ===")
    print("❌ 基础网络通信有问题")
    print("需要检查:")
    print("1. 网络助手的设置")
    print("2. 设备的网络配置")
    print("3. 防火墙设置")
end
