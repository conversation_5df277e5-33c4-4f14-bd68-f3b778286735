#!/usr/bin/lua

-- 网络调试脚本

print("=== 网络调试脚本 ===")
print("")

-- 1. 检查端口监听状态
print("1. 检查端口50001监听状态:")
os.execute("netstat -ulnp | grep 50001")
print("")

-- 2. 检查进程状态
print("2. 检查Lua进程:")
os.execute("ps | grep lua")
print("")

-- 3. 检查ac_comm模块类型
print("3. 检查ac_comm模块:")
local status, ac_comm = pcall(require, "ac_comm")
if status then
    print("✅ ac_comm模块加载成功")
    
    -- 检查模块类型
    if ac_comm.listen_and_handle then
        print("  - 有listen_and_handle函数")
    else
        print("  - 没有listen_and_handle函数")
    end
    
    if ac_comm.init_udp then
        print("  - 有init_udp函数 (C库版本)")
    else
        print("  - 没有init_udp函数 (纯Lua版本)")
    end
    
    if ac_comm.init then
        print("  - 有init函数")
    else
        print("  - 没有init函数")
    end
else
    print("❌ ac_comm模块加载失败: " .. tostring(ac_comm))
end
print("")

-- 4. 测试简单的UDP接收
print("4. 测试简单UDP接收 (5秒超时):")
print("请在5秒内使用网络助手发送数据到端口50002...")

local socket = require("socket")
local udp = socket.udp()
udp:settimeout(5)
udp:setsockname("*", 50002)

local data, ip, port = udp:receivefrom()
if data then
    print("✅ 收到数据:")
    print("  来源: " .. tostring(ip) .. ":" .. tostring(port))
    print("  长度: " .. #data .. " 字节")
    print("  内容: " .. data)
    print("  十六进制: " .. (data:gsub('.', function(c) return string.format('%02X ', string.byte(c)) end)))
else
    print("❌ 没有收到数据 (超时或错误)")
    print("  错误信息: " .. tostring(ip))
end
udp:close()
print("")

-- 5. 检查C库是否可用
print("5. 检查C库ac_comm.so:")
local status, ac_comm_so = pcall(require, "ac_comm")
if status and ac_comm_so.debug_recv then
    print("✅ C库ac_comm.so可用，测试debug_recv...")
    print("请在3秒内使用网络助手发送数据到端口50003...")
    
    local data, ip, port = ac_comm_so.debug_recv(50003, 3)
    if data then
        print("✅ C库收到数据:")
        print("  来源: " .. tostring(ip) .. ":" .. tostring(port))
        print("  长度: " .. #data .. " 字节")
        print("  内容: " .. data)
    else
        print("❌ C库没有收到数据")
        print("  错误: " .. tostring(ip))
    end
else
    print("❌ C库ac_comm.so不可用或没有debug_recv函数")
end
print("")

-- 6. 检查日志文件
print("6. 检查日志文件:")
local log_files = {
    "/tmp/ac_main_loop.log",
    "/tmp/ac_config_manager.log", 
    "/tmp/ac_comm_debug.log"
}

for _, log_file in ipairs(log_files) do
    local f = io.open(log_file, "r")
    if f then
        local size = f:seek("end")
        f:close()
        print("  " .. log_file .. ": " .. size .. " 字节")
    else
        print("  " .. log_file .. ": 不存在")
    end
end
print("")

print("=== 调试建议 ===")
print("1. 如果端口50001没有监听，说明ac_main_loop.lua没有正确启动")
print("2. 如果端口50002能收到数据，说明网络连接正常")
print("3. 如果端口50003能收到数据，说明C库工作正常")
print("4. 检查ac_main_loop.log的最新内容")
print("")
print("测试命令:")
print("网络助手 -> UDP -> 目标IP:设备IP -> 端口:50002或50003 -> 发送:41")
