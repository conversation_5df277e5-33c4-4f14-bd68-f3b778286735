#!/bin/bash

echo "=== 快速修复和测试脚本 ==="
echo ""

echo "1. 停止当前服务..."
killall lua 2>/dev/null

echo "2. 测试upgrade.lua模块加载..."
lua -e "
local status, result = pcall(require, 'upgrade')
if status then
    print('✅ upgrade.lua 加载成功')
else
    print('❌ upgrade.lua 加载失败: ' .. tostring(result))
    os.exit(1)
end
"

if [ $? -ne 0 ]; then
    echo "upgrade.lua 还有问题，请检查"
    exit 1
fi

echo "3. 测试ac_handlers模块加载..."
lua -e "
local status, result = pcall(require, 'ac_handlers')
if status then
    print('✅ ac_handlers 加载成功')
else
    print('❌ ac_handlers 加载失败: ' .. tostring(result))
    os.exit(1)
end
"

if [ $? -ne 0 ]; then
    echo "ac_handlers 还有问题，请检查"
    exit 1
fi

echo "4. 启动AC管理服务..."
lua ac_main_loop.lua &
AC_PID=$!

sleep 2

echo "5. 检查服务状态..."
if ps | grep -q $AC_PID; then
    echo "✅ AC管理服务启动成功 (PID: $AC_PID)"
else
    echo "❌ AC管理服务启动失败"
    exit 1
fi

echo "6. 检查端口监听..."
if netstat -ulnp | grep -q 50001; then
    echo "✅ 端口50001监听正常"
else
    echo "❌ 端口50001没有监听"
    kill $AC_PID 2>/dev/null
    exit 1
fi

echo ""
echo "=== 测试成功！==="
echo "AC管理服务已启动，PID: $AC_PID"
echo "现在可以使用网络助手测试："
echo ""
echo "测试数据："
echo "简单测试: 41 (字母A的十六进制)"
echo "JSON测试: 7B2274657374223A317D ('{\"test\":1}'的十六进制)"
echo ""
echo "查看日志："
echo "tail -f /tmp/ac_main_loop.log"
echo ""
echo "停止服务："
echo "kill $AC_PID"
