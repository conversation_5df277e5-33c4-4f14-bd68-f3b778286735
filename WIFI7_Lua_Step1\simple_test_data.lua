#!/usr/bin/lua

-- 简单测试数据生成器 - 生成最基本的测试包

print("=== 简单AC协议测试数据生成器 ===")
print("")

-- 测试1: 纯文本JSON数据
local test1_json = '{"version":"1.0","sid":"test123","module":"heartbeat","api":"ping"}'
print("测试1: 纯JSON数据")
print("数据: " .. test1_json)
print("长度: " .. #test1_json .. " 字节")

-- 转换为十六进制
local function to_hex(str)
    return (str:gsub('.', function(c)
        return string.format('%02X', string.byte(c))
    end))
end

local test1_hex = to_hex(test1_json)
print("十六进制: " .. test1_hex)
print("")

-- 测试2: 更简单的数据
local test2_simple = "HELLO"
print("测试2: 简单文本")
print("数据: " .. test2_simple)
print("十六进制: " .. to_hex(test2_simple))
print("")

-- 测试3: 单字节测试
local test3_byte = "A"
print("测试3: 单字节")
print("数据: " .. test3_byte)
print("十六进制: " .. to_hex(test3_byte))
print("")

print("=== 网络助手测试步骤 ===")
print("1. 打开网络助手")
print("2. 协议类型: UDP")
print("3. 目标IP: 设备IP地址")
print("4. 目标端口: 50001")
print("5. 数据格式: HEX十六进制")
print("6. 发送数据: 选择上面任一个十六进制数据")
print("")
print("预期结果:")
print("- 设备日志应该显示 '*** PACKET RECEIVED ***'")
print("- 显示接收到的数据长度和内容")
print("- 显示十六进制数据")
print("- 显示处理步骤")
print("")
print("如果没有任何日志输出，说明:")
print("1. 网络连接问题")
print("2. 端口50001没有监听")
print("3. 防火墙阻止")
print("4. IP地址错误")
print("")

-- 生成一些测试用的十六进制数据
print("=== 快速测试数据 ===")
local quick_tests = {
    {name = "测试A", data = "A", desc = "单字节测试"},
    {name = "测试Hello", data = "Hello", desc = "简单文本"},
    {name = "测试JSON", data = '{"test":1}', desc = "简单JSON"},
}

for i, test in ipairs(quick_tests) do
    print(string.format("%d. %s (%s)", i, test.name, test.desc))
    print("   数据: " .. test.data)
    print("   HEX:  " .. to_hex(test.data))
    print("")
end
