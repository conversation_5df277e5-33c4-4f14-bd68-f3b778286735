#!/usr/bin/lua

-- 检查ac_handlers加载状态的脚本

print("=== AC Handlers 加载状态检查 ===")
print("")

-- 检查各个依赖模块
local modules_to_check = {
    "cjson.safe",
    "ac_config_manager", 
    "ac_status",
    "ac_protocol",
    "ac_comm",
    "ac_log",
    "wifi_2_4g",
    "wifi_5g", 
    "scheduled_reboot",
    "ap_mode",
    "route_mode",
    "bridge_mode",
    "vlan",
    "user_setup",
    "user_event",
    "qos_management",
    "current_device",
    "mac_access_control",
    "snmp",
    "network_status",
    "upgrade"
}

local failed_modules = {}
local success_count = 0

for i, module_name in ipairs(modules_to_check) do
    print(string.format("检查模块 %d/%d: %s", i, #modules_to_check, module_name))
    
    local status, result = pcall(require, module_name)
    if status then
        print("  ✅ 加载成功")
        success_count = success_count + 1
    else
        print("  ❌ 加载失败: " .. tostring(result))
        table.insert(failed_modules, {name = module_name, error = tostring(result)})
    end
end

print("")
print("=== 检查结果 ===")
print(string.format("成功: %d/%d", success_count, #modules_to_check))
print(string.format("失败: %d/%d", #failed_modules, #modules_to_check))

if #failed_modules > 0 then
    print("")
    print("失败的模块:")
    for i, module in ipairs(failed_modules) do
        print(string.format("%d. %s", i, module.name))
        print("   错误: " .. module.error)
    end
    print("")
    print("❌ ac_handlers 无法加载，因为有依赖模块失败")
else
    print("")
    print("所有依赖模块加载成功，现在测试 ac_handlers...")
    
    local status, result = pcall(require, "ac_handlers")
    if status then
        print("✅ ac_handlers 加载成功！")
    else
        print("❌ ac_handlers 加载失败: " .. tostring(result))
    end
end

print("")
print("=== 建议 ===")
if #failed_modules > 0 then
    print("1. 修复失败的模块")
    print("2. 确保所有CGI代码都在route_api()函数内")
    print("3. 重新上传修复后的文件")
else
    print("1. 所有模块都正常")
    print("2. 可以进行网络测试")
    print("3. 检查网络连接和防火墙设置")
end
