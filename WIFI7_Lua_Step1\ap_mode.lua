#!/usr/bin/lua

-- Copyright (c) 2024 The Linux Foundation. All rights reserved.
-- Not a Contribution.

-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

-- Lua环境：此脚本在OpenWRT的Lua环境中运行，确保有必要的Lua库，如luci.sys, luci.jsonc和luci.uci

-- UCI：这个脚本使用了uci库来直接操作配置文件。这比直接读写文件更为安全和标准。

-- Base64编码：在获取IP地址、子网掩码等信息时，会进行base64编码以满足API格式的要求。

-- 错误处理：脚本中包含了基本的错误处理，可以根据实际需求进行扩展。

-- 外网设置：
  -- set_wan_settings函数处理外网设置的更新，包括PPPoE、静态IP和动态获取等配置。
  -- get_wan_settings函数用于获取当前的外网设置信息。
  -- 使用uci库来更新或读取外网设置配置。
  -- 使用uci:commit来保存配置文件。
  -- 使用sys.exec来重启相关服务（如network）。

-- API调用格式：
--{
--    "version": "1.0",
--    "sid": "5bdd8aec153a8ff183387bfcea40e3aa",
--    "module": "wan_settings",
--    "api": "set",
--    "param": {
--        "mode": "pppoe/static/dhcp",
--        "auth_type": "PAP/CHAP/PAP_CHAP",
--        "username": "username",
--        "password": "password",
--        "server": "server_name",
--        "mtu": "mtu_value",
--        "ip": "ip_address",
--        "netmask": "netmask",
--        "gateway": "***********",
--        "dns1": "*******",
--        "dns2": "***************"
--    }
--}
  
--{
--    "version": "1.0",
--    "sid": "5bdd8aec153a8ff183387bfcea40e3aa",
--    "module": "wan_settings",
--    "api": "set",
--    "param": {
--        "mode": "pppoe"或"static"或"dhcp",
--        "pppoe": {
--            "username": "base64编码的用户名",
--            "password": "base64编码的密码",
--            "type": "base64编码的类型",
--            "server": "base64编码的服务器名称",
--            "mtu": "base64编码的MTU",
--            "dns1": "base64编码的首选DNS服务器",
--            "dns2": "base64编码的备用DNS服务器"
--        },
--        "static": {
--            "ip": "base64编码的IP地址",
--            "netmask": "base64编码的子网掩码",
--            "gateway": "base64编码的默认网关",
--            "dns1": "base64编码的首选DNS服务器",
--            "dns2": "base64编码的备用DNS服务器"
--        },
--        "dhcp": {
--            "dns1": "base64编码的首选DNS服务器",
--            "dns2": "base64编码的备用DNS服务器"
--        }
--    }
--}

--{
--    "version": "1.0",
--    "sid": "session_id",
--    "module": "wan_settings",
--    "api": "get"
--  }
  
-- 这个脚本使用的是OpenWRT的network配置文件。
-- 在set_wan_settings函数中，脚本根据用户提供的mode（pppoe、static或dhcp）来设置相应的WAN配置。
-- 在get_wan_settings函数中，返回的数据需要进行base64编码，以便与API格式保持一致

-- 重启服务：在更新外网设置后，可能需要重启相关服务来使配置生效。

-- 安全性：确保处理用户输入时，进行了必要的验证和清理，以防止注入攻击
-- 性能：由于脚本每次都需要读取和写入配置文件，对于频繁调用的API，可以考虑优化，例如通过缓存减少IO操作。
-- 权限：确保脚本以必要的权限运行，修改网络配置文件和重启网络服务需要root权限。

--网桥模式配置清理：
--删除 LAN 的桥接类型配置
--删除所有非标准的 def_ifname 配置
--删除网桥模式下的 wwan 接口配置
--防火墙配置清理和恢复：
--删除网桥模式下的特殊防火墙规则
--删除可能存在的旧规则（lan_to_wan, wan_to_lan）
--恢复标准的防火墙配置，包括 WAN 区域的规则和 NAT 设置
--DHCP 服务器配置：
--启用 DHCP 服务器
--设置 DHCP 地址池范围
--设置租约时间
--强制 DHCP 服务器运行
--启用 IPv6 路由通告
--网络配置恢复：
--设置默认 WAN 协议为 PPPoE
--配置 WAN 和 WAN6 接口
--设置 IPv6 协议和接口
--LAN 配置清理和恢复：
--删除可能存在的网关配置
--删除可能存在的 DNS 配置
--设置默认协议为静态
--配置提交：
--在清理和恢复配置后，立即提交所有更改
--包括网络、防火墙、DHCP 和系统配置
--这些改进确保了：
--从网桥模式切换到路由模式时，所有网桥相关的配置都被正确清理
--从 AP 模式切换到路由模式时，DHCP 服务器配置被正确恢复
--路由模式所需的标准配置都被正确设置
--避免了不同模式之间的配置冲突
--提供了更详细的日志记录，方便追踪配置变更
--建议在测试时特别关注以下几点：
--从网桥模式切换到路由模式时，确认所有网桥相关的配置都被正确清理
--从 AP 模式切换到路由模式时，确认 DHCP 服务器配置正确恢复
--验证 WAN 接口是否能正常获取 IP 地址
--检查防火墙规则是否正确应用
--确认 LAN 接口的 DHCP 服务是否正常工作
--验证 IPv6 功能是否正常


-- 引入模块
local cjson = require("cjson.safe")
local sys = require("luci.sys")
local uci = require("luci.model.uci").cursor()
local log_file = "/tmp/ap_mode.log" -- 日志文件路径，改为AP模式日志

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end

-- 获取当前应用模式的辅助函数
local function get_current_apply_mode()
    local mode_file = "/etc/config_apply_mode_status"
    local f = io.open(mode_file, "r")
    if f then
        local mode = f:read("*l") -- Read the first line
        f:close()
        mode = mode and mode:gsub("^%s*(.-)%s*$", "%1") -- Trim whitespace
        if mode == "immediate" or mode == "deferred" then
            write_log("Read apply_mode from " .. mode_file .. ": " .. mode)
            return mode
        else
            write_log("Invalid content in " .. mode_file .. ": '" .. (mode or "nil") .. "'. Defaulting to 'immediate'.")
            return "immediate"
        end
    else
        write_log("Could not open " .. mode_file .. ". Defaulting to 'immediate'.")
        return "immediate"
    end
end



-- 验证 IP 地址是否有效
local function is_valid_ip(ip)
    return ip:match("^%d+%.%d+%.%d+%.%d+$") ~= nil -- 检查IP地址格式是否正确
end

-- 验证子网掩码是否有效
local function is_valid_netmask(netmask)
    return netmask:match("^%d+%.%d+%.%d+%.%d+$") ~= nil -- 检查子网掩码格式是否正确
end

-- AP模式下不需要验证网关和DNS
local function is_valid_gateway(gateway)
    return true
end

local function is_valid_dns(dns)
    return true
end

-- 路由到具体逻辑
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 获取 POST 数据长度
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""

    -- 读取 POST 数据
    if POSTLength > 0 then
        POST = io.read(POSTLength)
        write_log("Received POST data: " .. (POST or "nil"))
    else
        write_log("No POST data received or CONTENT_LENGTH is 0")
    end

    -- 确保读取成功
    if not POST or POST == "" then
        local error_message = "Failed to retrieve POST data"
        write_log(error_message)
        io.write(cjson.encode({
            module = "ap_mode",
            version = "1.0",
            errcode = 1,
            result = { message = error_message }
        }))
        return
    end

    -- 解析 POST 数据为 JSON
    local requestData = cjson.decode(POST)
    if not requestData then
        local error_message = "Invalid JSON input"
        write_log(error_message)
        io.write(cjson.encode({
            module = "ap_mode",
            version = "1.0",
            errcode = 2,
            result = { message = error_message }
        }))
        return
    end

    write_log("Parsed request data: " .. cjson.encode(requestData))

    -- 检查请求格式
    if not requestData.version or not requestData.sid or not requestData.module or not requestData.api then
        local error_message = "Invalid request format"
        write_log(error_message)
        io.write(cjson.encode({
            module = "ap_mode",
            version = "1.0",
            errcode = 3,
            result = { message = error_message }
        }))
        return
    end

    if requestData.api == "set" then
        write_log("Calling set_all_settings with data: " .. cjson.encode(requestData))
        set_all_settings(requestData)
    elseif requestData.api == "get" then
        write_log("Calling get_all_settings with data: " .. cjson.encode(requestData))
        get_all_settings(requestData)
    else
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "ap_mode",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
    end
end

local function table_contains(tbl, value)
    for _, v in ipairs(tbl) do
        if v == value then
            return true
        end
    end
    return false
end

-- 设置所有配置 (LAN, WiFi)
function set_all_settings(data)
    local param = data.param or {}
    local current_mode = uci:get("system", "nhx", "mode") or "router"
    write_log("Current mode: " .. current_mode .. ", setting to AP")

    -- 检查运行时环境
    write_log("Checking runtime environment")
    local mem_info = sys.exec("free -m")
    write_log("Memory info: " .. mem_info)
    local start_time = os.time()

    -- 设置系统模式为 AP
    uci:set("system", "nhx", "mode", "ap")
    write_log("Setting system mode to AP")

    -- 防火墙处理：分为网桥模式切换和其他模式切换两种情况
    if current_mode == "bridge" then
        write_log("Switching from bridge mode, restoring configurations")
        -- 终止 bridge_monitor 进程
        local kill_result = sys.exec("killall bridge_monitor 2>&1")
        if kill_result and kill_result ~= "" then
            write_log("killall bridge_monitor result: " .. kill_result)
        end
        -- 等待 1 秒确保进程终止
        sys.call("sleep 1")
        -- 检查是否仍有 bridge_monitor 进程运行
        local ps_result = sys.exec("ps | grep '[b]ridge_monitor' | grep -v grep")
        if ps_result and ps_result ~= "" then
            write_log("Warning: bridge_monitor process still running after killall: " .. ps_result)
            -- 强制终止
            sys.exec("killall -9 bridge_monitor 2>&1")
            sys.call("sleep 1")
            ps_result = sys.exec("ps | grep '[b]ridge_monitor' | grep -v grep")
            if ps_result and ps_result ~= "" then
                write_log("Error: Failed to terminate bridge_monitor process: " .. ps_result)
            else
                write_log("bridge_monitor process forcefully terminated")
            end
        else
            write_log("bridge_monitor process successfully terminated")
        end
        -- 清理网桥模式残留防火墙配置
        uci:delete("firewall", "wwan_zone")
        uci:delete("firewall", "wwan_to_lan")
        uci:delete("firewall", "masquerade_rule")
        write_log("Removed bridge mode firewall remnants")
        -- 恢复 wan 区域
        local wan_zone_exists = false
        uci:foreach("firewall", "zone", function(s)
            if s.name == "wan" then
                wan_zone_exists = true
                uci:set_list("firewall", s[".name"], "network", {"wan", "wan6", "vpn", "wwan0", "wwan1"})
                uci:set("firewall", s[".name"], "input", "REJECT")
                uci:set("firewall", s[".name"], "output", "ACCEPT")
                uci:set("firewall", s[".name"], "forward", "REJECT")
                uci:set("firewall", s[".name"], "masq", "1")
                uci:set("firewall", s[".name"], "mtu_fix", "1")
            end
        end)
        if not wan_zone_exists then
            uci:section("firewall", "zone", "wan", {
                name = "wan",
                input = "REJECT",
                output = "ACCEPT",
                forward = "REJECT",
                masq = "1",
                mtu_fix = "1",
                network = {"wan", "wan6", "vpn", "wwan0", "wwan1"}
            })
        end
        write_log("Restored wan zone")
        -- 恢复 lan 区域
        uci:set("firewall", "lan", "input", "ACCEPT")
        uci:set("firewall", "lan", "output", "ACCEPT")
        uci:set("firewall", "lan", "forward", "ACCEPT")
        uci:foreach("firewall", "forwarding", function(s)
            if s.src == "lan" and s.dest == "wan" then
                uci:delete("firewall", s[".name"])
            end
        end)
        write_log("Restored lan zone and removed lan_to_wan forwarding")
    else
        -- 非网桥模式切换（AP 到 AP 或路由到 AP）
        write_log("Switching from non-bridge mode, applying AP firewall settings")
        -- 清理可能存在的 WWAN 相关配置
        uci:set("firewall", "wwan_zone", "disabled", "1")
        uci:set("firewall", "wwan_to_lan", "enabled", "0")
        uci:set("firewall", "masquerade_rule", "enabled", "0")
        -- 恢复 wan 区域
        local wan_zone_success, wan_zone_err = pcall(uci.foreach, uci, "firewall", "zone", function(s)
            if s.name == "wan" then
                uci:delete("firewall", s[".name"], "network")
                uci:set_list("firewall", s[".name"], "network", {"wan", "wan6", "vpn", "wwan0", "wwan1"})
                uci:set("firewall", s[".name"], "input", "REJECT")
                uci:set("firewall", s[".name"], "output", "ACCEPT")
                uci:set("firewall", s[".name"], "forward", "REJECT")
                uci:set("firewall", s[".name"], "masq", "1")
                uci:set("firewall", s[".name"], "mtu_fix", "1")
            end
        end)
        if not wan_zone_success then
            write_log("Error in WAN zone foreach: " .. (wan_zone_err or "unknown error"))
        end
        -- 恢复 lan 区域
        uci:set("firewall", "lan", "input", "ACCEPT")
        uci:set("firewall", "lan", "output", "ACCEPT")
        uci:set("firewall", "lan", "forward", "ACCEPT")
        write_log("Restored firewall settings")
        -- 移除 lan -> wan 转发规则（AP 模式不需要）
        local forwarding_success, forwarding_err = pcall(uci.foreach, uci, "firewall", "forwarding", function(s)
            if s.src == "lan" and s.dest == "wan" then
                uci:delete("firewall", s[".name"])
                write_log("Deleted lan -> wan forwarding rule")
            end
        end)
        if not forwarding_success then
            write_log("Error in forwarding foreach: " .. (forwarding_err or "unknown error"))
        end
        write_log("Removed lan_to_wan forwarding rule")
    end

    -- 清理其他模式配置
    write_log("Cleaning up configurations from other modes")
    uci:delete("network", "lan", "stp")
    uci:delete("network", "lan", "gateway")
    uci:delete("network", "lan", "dns")
    uci:delete("network", "lan", "ip6assign")
    uci:delete("network", "lan", "force_link")
    uci:delete("network", "lan", "def_ifname")
    uci:delete("network", "wan", "def_ifname")
    uci:delete("network", "wan6", "def_ifname")

    -- 配置网络：创建 br-lan，桥接 eth1.1, eth1.2 和主要 AP 接口
    local lan_ifnames = {"eth1.1", "eth1.2"} -- AP 模式桥接所有以太网接口
    local valid_ap_ifaces = {"wlan0", "wlan10", "wlan30"} -- 仅 2.4G, 5G, 6G 第一个 VAP
    local foreach_success, foreach_err = pcall(uci.foreach, uci, "wireless", "wifi-iface", function(s)
        if s.ifname and s.disabled ~= "1" and s.mode == "ap" and s.network == "lan" and table_contains(valid_ap_ifaces, s[".name"]) then
            table.insert(lan_ifnames, s.ifname)
        end
    end)
    if not foreach_success then
        write_log("Error in wireless foreach for LAN ifnames: " .. (foreach_err or "unknown error"))
    end
    -- 移除 STA 接口（ath5, ath15, ath35, ath4, ath14, ath34）
    local current_ifnames = uci:get("network", "lan", "ifname") or ""
    local new_ifnames = {}
    local sta_ifnames = {"ath5", "ath15", "ath35", "ath4", "ath14", "ath34"}
    if current_ifnames ~= "" then
        for ifname in current_ifnames:gmatch("%S+") do
            if not table_contains(sta_ifnames, ifname) then
                table.insert(new_ifnames, ifname)
            end
        end
    else
        new_ifnames = lan_ifnames
    end
    -- 合并现有接口和新接口，确保不重复
    for _, ifname in ipairs(lan_ifnames) do
        if not table_contains(new_ifnames, ifname) then
            table.insert(new_ifnames, ifname)
        end
    end
    uci:set("network", "lan", "type", "bridge")
    uci:set("network", "lan", "ifname", table.concat(new_ifnames, " "))
    -- 根据 Web 参数设置 proto
    local lan_param = param.lan or {}
    local ip_allocation = lan_param.ip_allocation or "static" -- 默认静态
    uci:set("network", "lan", "proto", ip_allocation)
    if ip_allocation == "dhcp" then
        -- 动态模式：保留 ipaddr 和 netmask，不设置 gateway
        local current_ip = uci:get("network", "lan", "ipaddr") or "*************"
        local current_netmask = uci:get("network", "lan", "netmask") or "*************"
        uci:set("network", "lan", "ipaddr", current_ip)
        uci:set("network", "lan", "netmask", current_netmask)
        uci:delete("network", "lan", "gateway") -- 动态模式不需要网关
        -- 处理 DNS
        local dns1 = lan_param.dns1 or ""
        local dns2 = lan_param.dns2 or ""
        if dns1 ~= "" and dns2 ~= "" then
            uci:set("network", "lan", "dns", dns1 .. " " .. dns2)
        elseif dns1 ~= "" then
            uci:set("network", "lan", "dns", dns1)
        elseif dns2 ~= "" then
            uci:set("network", "lan", "dns", dns2)
        else
            uci:delete("network", "lan", "dns")
        end
    else
        -- 静态模式：设置 ipaddr、netmask、gateway 和 dns
        local current_ip = uci:get("network", "lan", "ipaddr") or "*************"
        local current_netmask = uci:get("network", "lan", "netmask") or "*************"
        uci:set("network", "lan", "ipaddr", lan_param.ipaddr or current_ip)
        uci:set("network", "lan", "netmask", lan_param.netmask or current_netmask)
        -- 处理网关
        if lan_param.gateway and lan_param.gateway ~= "" then
            uci:set("network", "lan", "gateway", lan_param.gateway)
        else
            uci:delete("network", "lan", "gateway")
        end
        -- 处理 DNS
        local dns1 = lan_param.dns1 or ""
        local dns2 = lan_param.dns2 or ""
        if dns1 ~= "" and dns2 ~= "" then
            uci:set("network", "lan", "dns", dns1 .. " " .. dns2)
        elseif dns1 ~= "" then
            uci:set("network", "lan", "dns", dns1)
        elseif dns2 ~= "" then
            uci:set("network", "lan", "dns", dns2)
        else
            uci:delete("network", "lan", "dns")
        end
    end
    -- 恢复出厂配置的 ip6assign、force_link 和 def_ifname
    uci:set("network", "lan", "ip6assign", "60")
    uci:set("network", "lan", "force_link", "1")
    uci:set("network", "lan", "def_ifname", "eth1.1")
    write_log("Configured LAN ifname: " .. table.concat(new_ifnames, " "))

    -- 禁用 WAN、WAN6、WWAN 接口
    uci:set("network", "wan", "proto", "none")
    uci:set("network", "wan", "disabled", "1")
    uci:set("network", "wan", "def_ifname", "eth1.2") -- 恢复 def_ifname
    uci:set("network", "wan6", "proto", "none")
    uci:set("network", "wan6", "disabled", "1")
    uci:set("network", "wan6", "def_ifname", "eth1.2") -- 恢复 def_ifname
    uci:set("network", "wwan0", "disabled", "1")
    uci:set("network", "wwan1", "disabled", "1")
    uci:set("network", "wwan3", "disabled", "1")
    write_log("Disabled WAN, WAN6, and WWAN interfaces")


    local vlan_success, vlan_err = pcall(function()
        local vlan1_exists = false
        foreach_success, foreach_err = pcall(uci.foreach, uci, "network", "switch_vlan", function(s)
            if s.vlan == "1" and s.ports == "0t 1 2 3 4" then
                vlan1_exists = true
            end
        end)
        if not foreach_success then
            write_log("Error in VLAN foreach: " .. (foreach_err or "unknown error"))
        end

        -- 删除现有的 VLAN 2 和重复的 VLAN 1
        uci:foreach("network", "switch_vlan", function(s)
            if s.vlan == "2" then
                uci:delete("network", s[".name"])
                write_log("Deleted VLAN 2: " .. s[".name"])
            elseif s.vlan == "1" and s.ports ~= "0t 1 2 3 4" then
                uci:delete("network", s[".name"])
                write_log("Deleted inconsistent VLAN 1: " .. s[".name"])
            end
        end)

        if not vlan1_exists then
            uci:set("network", "switch_vlan1", "switch_vlan")
            uci:set("network", "switch_vlan1", "device", "switch1")
            uci:set("network", "switch_vlan1", "vlan", "1")
            uci:set("network", "switch_vlan1", "ports", "0t 1 2 3 4")
            write_log("Added VLAN 1: ports 0t 1 2 3 4")
        else
            write_log("VLAN 1 already exists with correct ports, skipping")
        end
    end)
    if not vlan_success then
        write_log("Error in VLAN configuration: " .. (vlan_err or "unknown error"))
    end
    write_log("VLAN configuration restored: VLAN 1 (ports 0t 1 2 3 4)")


    -- 启用 DHCP（AP 模式禁用 DHCP 服务器）
    uci:set("dhcp", "lan", "ignore", "1")
    uci:set("dhcp", "lan", "start", "100")
    uci:set("dhcp", "lan", "limit", "150")
    uci:set("dhcp", "lan", "leasetime", "12h")
    uci:set("dhcp", "lan", "force", "1")
    uci:set("dhcp", "wan", "ignore", "1")
    write_log("Disabled DHCP server on lan interface")

    -- 禁用 STA 接口并移除 WDS
    foreach_success, foreach_err = pcall(uci.foreach, uci, "wireless", "wifi-iface", function(s)
        if s.mode == "sta" then
            uci:set("wireless", s[".name"], "disabled", "1")
            uci:delete("wireless", s[".name"], "wds")
            write_log("Disabled STA interface: " .. (s.ifname or s[".name"]))
        end
    end)
    if not foreach_success then
        write_log("Error in wireless foreach for disabling STA: " .. (foreach_err or "unknown error"))
    end

    -- 启用主要 AP 接口，禁用其他 AP 接口
    -- local ap_ifaces = {"wlan0", "wlan10", "wlan30"}
    local ap_ifaces = {"wlan0", "wlan10"} -- 暂时不用6G
    for _, iface in ipairs(ap_ifaces) do
        uci:set("wireless", iface, "disabled", "0")
    end

    local other_ap_ifaces = {"wlan1", "wlan2", "wlan3", "wlan4", "wlan11", "wlan12", "wlan13", "wlan14", "wlan31", "wlan32", "wlan33", "wlan34"}
    for _, iface in ipairs(other_ap_ifaces) do
        uci:set("wireless", iface, "disabled", "1")
    end
    -- 禁用 STA 接口
    local sta_ifaces_config = {"sta0", "sta1", "sta3"}
    for _, iface in ipairs(sta_ifaces_config) do
        uci:set("wireless", iface, "disabled", "1")
    end
    -- 启用 son/backhaul 接口
    local son_ifaces = {"son0", "son1", "son2"}
    for _, iface in ipairs(son_ifaces) do
        uci:set("wireless", iface, "disabled", "0")
    end
    write_log("Enabled main AP interfaces and disabled others")

    -- 自动恢复/重建 wwan0/wwan1/wwan3 接口（AP 模式下禁用）
    local sta_ifaces = {
        {name = "wwan0", ifname = "ath5"},
        {name = "wwan1", ifname = "ath15"},
        {name = "wwan3", ifname = "ath35"}
    }
    for _, sta in ipairs(sta_ifaces) do
        local section_success, section_err = pcall(uci.section, uci, "network", "interface", sta.name, {
            ifname = sta.ifname,
            proto = "dhcp",
            disabled = "1"
        })
        if not section_success then
            write_log("Error creating STA interface " .. sta.name .. ": " .. (section_err or "unknown error"))
        end
    end
    write_log("Restored wwan interfaces")

    -- 设置 LAN 配置
    local lan_param = param.lan or {}
    set_lan_settings(lan_param)

    -- 设置 WiFi
    local wifi_param = param.wifi or {}
    local wifi_device_params = {}
    local wifi_iface_params = {}

    for key, value in pairs(wifi_param) do
        if key:match("^wlan") then
            wifi_iface_params[key] = value
        end
        local device_name = "wifi0"
        if key == "wlan0" then device_name = "wifi0"
        elseif key == "wlan10" then device_name = "wifi1"
        elseif key == "wlan30" then device_name = "wifi2" end
        wifi_device_params[device_name] = wifi_device_params[device_name] or {}
        wifi_device_params[device_name].channel = value.channel
        wifi_device_params[device_name].txpower = value.txpower
        wifi_device_params[device_name].country = value.country
        wifi_device_params[device_name].bandwidth = value.bandwidth
    end

    set_wifi(wifi_device_params, wifi_iface_params)
    write_log("WiFi settings applied successfully")

    -- 提交所有配置
    write_log("Committing configurations")
    local commit_success, commit_err = pcall(function()
        uci:commit("network")
        uci:commit("firewall")
        uci:commit("dhcp")
        uci:commit("wireless")
        uci:commit("system")
    end)
    if not commit_success then
        write_log("Error committing configurations: " .. (commit_err or "unknown error"))
    end

    -- 记录执行时间
    local end_time = os.time()
    write_log("Script execution time: " .. (end_time - start_time) .. " seconds")

    -- 立即返回 Web 响应
    write_log("All settings updated successfully, scheduling asynchronous restart")
    local apply_mode = get_current_apply_mode()
    write_log("Current apply mode: " .. apply_mode)

    if apply_mode == "immediate" then
        io.write(cjson.encode({
            module = "ap_mode",
            version = "1.0",
            api = "set",
            errcode = 0,
            sid = data.sid,
            result = { message = "AP模式设置已更新，正在重启服务..." }
        }))
        io.flush()
        -- 异步重启服务
        local restart_cmd = [[
            /bin/sh -c "
                echo 'Starting AP mode restart sequence' >> /tmp/ap_mode_restart.log;
                /etc/init.d/network stop;
                sleep 2;
                sync;
                /etc/init.d/network start;
                sleep 3;
                wifi;
                sleep 2;
                echo 'AP mode restart sequence completed' >> /tmp/ap_mode_restart.log;
            " &
        ]]
        os.execute(restart_cmd)
    else
        write_log("Apply mode is deferred. Services will not be restarted automatically.")
        io.write(cjson.encode({
            module = "ap_mode",
            version = "1.0",
            api = "set",
            errcode = 0,
            sid = data.sid,
            result = { message = "AP模式设置已保存，延迟生效" }
        }))
        io.flush()
    end
end


-- 获取所有配置 (LAN, WiFi)
function get_all_settings(data)
    local result = {
        module = "ap_mode",
        version = "1.0",
        api = "get",
        errcode = 0,
        sid = data.sid,
        result = {
            lan = get_lan_settings(),
            wifi = get_wifi(),
            mode = "ap" -- 固定返回 ap
        }
    }

    write_log("All settings retrieved successfully")
    io.write(cjson.encode(result))
end

-- 设置内网配置
function set_lan_settings(data)
    local param = data or {}
    write_log("Setting LAN settings: " .. cjson.encode(param))

    if param.ip_allocation == "static" then
        if not is_valid_ip(param.ip) or not is_valid_netmask(param.netmask) then
            local error_message = "Invalid IP or netmask for static allocation"
            write_log(error_message)
            io.write(cjson.encode({
                module = "ap_mode",
                version = "1.0",
                api = "set",
                errcode = 5,
                result = { message = error_message }
            }))
            return
        end
        uci:set("network", "lan", "proto", "static")
        uci:set("network", "lan", "ipaddr", param.ip or "*************")
        uci:set("network", "lan", "netmask", param.netmask or "*************") -- 默认 /24
        if param.gateway and param.gateway ~= "" then
            uci:set("network", "lan", "gateway", param.gateway)
        else
            uci:delete("network", "lan", "gateway")
        end
        if param.dns and param.dns ~= "" then
            uci:set("network", "lan", "dns", param.dns)
        else
            uci:delete("network", "lan", "dns")
        end
    else
        local error_message = "LAN should not use DHCP in AP mode"
        write_log(error_message)
        io.write(cjson.encode({
            module = "ap_mode",
            version = "1.0",
            api = "set",
            errcode = 6,
            result = { message = error_message }
        }))
        return
    end
    uci:commit("network")
end

-- 获取内网配置
function get_lan_settings()
    local proto = uci:get("network", "lan", "proto") or "static"
    local ip = uci:get("network", "lan", "ipaddr") or ""
    local netmask = uci:get("network", "lan", "netmask") or ""
    local gateway = uci:get("network", "lan", "gateway") or ""
    local dns = uci:get("network", "lan", "dns") or ""

    -- 如果 proto 为 dhcp或UCI 获取的 IP 或 netmask 为空，优先从 ifconfig 获取真实的 IP 和 netmask
    if proto == "dhcp" or ip == "" or netmask == "" then
        write_log("proto is dhcp, trying to get IP and netmask from ifconfig br-lan")
        local ifconfig_output = io.popen("ifconfig br-lan 2>/dev/null"):read("*a") or ""
        if ifconfig_output ~= "" then
            local ifconfig_ip = ifconfig_output:match("inet addr:([%d%.]+)")
            local ifconfig_mask = ifconfig_output:match("Mask:([%d%.]+)")
            if ifconfig_ip and ifconfig_ip ~= "" then
                ip = ifconfig_ip
                write_log("Got IP from ifconfig: " .. ip)
            else
                write_log("Failed to get IP from ifconfig, falling back to UCI")
            end
            if ifconfig_mask and ifconfig_mask ~= "" then
                netmask = ifconfig_mask
                write_log("Got netmask from ifconfig: " .. netmask)
            else
                write_log("Failed to get netmask from ifconfig, falling back to UCI")
            end
        else
            write_log("ifconfig br-lan output is empty, falling back to UCI")
        end
    end

    -- 如果仍然获取不到 IP 和 netmask，使用 UCI 配置的值或使用默认值
    if ip == "" then
        ip = "*************"
        write_log("Using default IP: " .. ip)
    end
    if netmask == "" then
        netmask = "*************"
        write_log("Using default netmask: " .. netmask)
    end

    return {
        ip_allocation = proto,
        ip = ip,
        netmask = netmask,
        gateway = gateway,
        dns = dns
    }
end

-- WiFi 设置和获取函数 (改进版)
function set_wifi(wifi_device_params, wifi_iface_params)
    -- 2.4G
    local radio_2_4G = "wifi0"
    local iface_2_4G = "wlan0"
    if wifi_device_params[radio_2_4G] then
        if wifi_device_params[radio_2_4G].channel then
            uci:set("wireless", radio_2_4G, "channel", wifi_device_params[radio_2_4G].channel)
        end
        if wifi_device_params[radio_2_4G].txpower then
            uci:set("wireless", radio_2_4G, "txpower", wifi_device_params[radio_2_4G].txpower)
        end
        if wifi_device_params[radio_2_4G].country then
            uci:set("wireless", radio_2_4G, "country", wifi_device_params[radio_2_4G].country)
        end
        if wifi_device_params[radio_2_4G].bandwidth then
            uci:set("wireless", radio_2_4G, "htmode", wifi_device_params[radio_2_4G].bandwidth) -- 设置带宽
        end
    end
    if wifi_iface_params[iface_2_4G] then
        uci:set("wireless", iface_2_4G, "ssid", wifi_iface_params[iface_2_4G].ssid or "IAP3500-E11-2.4G")
        if wifi_iface_params[iface_2_4G].encryption then
            uci:set("wireless", iface_2_4G, "encryption", wifi_iface_params[iface_2_4G].encryption)
        end
        -- 新增：根据加密方式设置 sae 选项
        if wifi_iface_params[iface_2_4G].encryption == "sae" or wifi_iface_params[iface_2_4G].encryption == "sae-mixed" then
            uci:set("wireless", iface_2_4G, "sae", "1")
        else
            uci:set("wireless", iface_2_4G, "sae", "0")
        end
        if wifi_iface_params[iface_2_4G].key then
            uci:set("wireless", iface_2_4G, "key", wifi_iface_params[iface_2_4G].key)
        end
    end

    -- 5G
    local radio_5G = "wifi1"
    local iface_5G = "wlan10"
    if wifi_device_params[radio_5G] then
        if wifi_device_params[radio_5G].channel then
            uci:set("wireless", radio_5G, "channel", wifi_device_params[radio_5G].channel)
        end
        if wifi_device_params[radio_5G].txpower then
            uci:set("wireless", radio_5G, "txpower", wifi_device_params[radio_5G].txpower)
        end
        if wifi_device_params[radio_5G].country then
            uci:set("wireless", radio_5G, "country", wifi_device_params[radio_5G].country)
        end
        if wifi_device_params[radio_5G].bandwidth then
            uci:set("wireless", radio_5G, "htmode", wifi_device_params[radio_5G].bandwidth) -- 设置带宽
        end
    end
    if wifi_iface_params[iface_5G] then
        uci:set("wireless", iface_5G, "ssid", wifi_iface_params[iface_5G].ssid or "IAP3500-E11-5G")
        if wifi_iface_params[iface_5G].encryption then
            uci:set("wireless", iface_5G, "encryption", wifi_iface_params[iface_5G].encryption)
        end
        -- 新增：根据加密方式设置 sae 选项
        if wifi_iface_params[iface_5G].encryption == "sae" or wifi_iface_params[iface_5G].encryption == "sae-mixed" then
            uci:set("wireless", iface_5G, "sae", "1")
        else
            uci:set("wireless", iface_5G, "sae", "0")
        end
        if wifi_iface_params[iface_5G].key then
            uci:set("wireless", iface_5G, "key", wifi_iface_params[iface_5G].key)
        end
    end

    -- 6G
    local radio_6G = "wifi2"
    local iface_6G = "wlan30"
    if wifi_device_params[radio_6G] then
        if wifi_device_params[radio_6G].channel then
            uci:set("wireless", radio_6G, "channel", wifi_device_params[radio_6G].channel)
        end
        if wifi_device_params[radio_6G].txpower then
            uci:set("wireless", radio_6G, "txpower", wifi_device_params[radio_6G].txpower)
        end
        if wifi_device_params[radio_6G].country then
            uci:set("wireless", radio_6G, "country", wifi_device_params[radio_6G].country)
        end
        if wifi_device_params[radio_6G].bandwidth then
            uci:set("wireless", radio_6G, "htmode", wifi_device_params[radio_6G].bandwidth) -- 设置带宽
        end
    end
    if wifi_iface_params[iface_6G] then
        uci:set("wireless", iface_6G, "ssid", wifi_iface_params[iface_6G].ssid or "IAP3500-E11-6G")
        if wifi_iface_params[iface_6G].encryption then
            uci:set("wireless", iface_6G, "encryption", wifi_iface_params[iface_6G].encryption)
        end
        -- 新增：根据加密方式设置 sae 选项
        if wifi_iface_params[iface_6G].encryption == "sae" or wifi_iface_params[iface_6G].encryption == "sae-mixed" then
            uci:set("wireless", iface_6G, "sae", "1")
        else
            uci:set("wireless", iface_6G, "sae", "0")
        end
        if wifi_iface_params[iface_6G].key then
            uci:set("wireless", iface_6G, "key", wifi_iface_params[iface_6G].key)
        end
    end

    uci:save("wireless")
    uci:commit("wireless")
end

function get_wifi()
    local result = {}

    -- 2.4G
    local iface_2_4G = "wlan0"
    result[iface_2_4G] = {
        ssid = uci:get("wireless", iface_2_4G, "ssid") or "",
        encryption = uci:get("wireless", iface_2_4G, "encryption") or "none",
        key = uci:get("wireless", iface_2_4G, "key") or "",
        channel = uci:get("wireless", "wifi0", "channel") or "auto",  -- 从 wifi0 获取
        country = uci:get("wireless", "wifi0", "country") or "CN",   -- 从 wifi0 获取
        txpower = uci:get("wireless", "wifi0", "txpower") or "auto", -- 从 wifi0 获取
        bandwidth = uci:get("wireless", "wifi0", "htmode") or "auto"
    }

    -- 5G
    local iface_5G = "wlan10"
    result[iface_5G] = {
        ssid = uci:get("wireless", iface_5G, "ssid") or "",
        encryption = uci:get("wireless", iface_5G, "encryption") or "none",
        key = uci:get("wireless", iface_5G, "key") or "",
        channel = uci:get("wireless", "wifi1", "channel") or "auto",  -- 从 wifi1 获取
        country = uci:get("wireless", "wifi1", "country") or "CN",   -- 从 wifi1 获取
        txpower = uci:get("wireless", "wifi1", "txpower") or "auto", -- 从 wifi1 获取
        bandwidth = uci:get("wireless", "wifi1", "htmode") or "auto"
    }

    -- 6G
    local iface_6G = "wlan30"
    result[iface_6G] = {
        ssid = uci:get("wireless", iface_6G, "ssid") or "",
        encryption = uci:get("wireless", iface_6G, "encryption") or "none",
        key = uci:get("wireless", iface_6G, "key") or "",
        channel = uci:get("wireless", "wifi2", "channel") or "auto",  -- 从 wifi2 获取
        country = uci:get("wireless", "wifi2", "country") or "CN",   -- 从 wifi2 获取
        txpower = uci:get("wireless", "wifi2", "txpower") or "auto", -- 从 wifi2 获取
        bandwidth = uci:get("wireless", "wifi2", "htmode") or "auto"
    }

    return result
end

-- 获取系统模式
function get_system_mode()
    return uci:get("system", "nhx", "mode") or "ap" -- 从系统配置中获取 mode，默认返回 "ap"
end

-- 检查是否在CGI环境中运行
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- AC 调用接口
local M = {}

function M.set_config_from_ac(payload)
    write_log("[AC] set_config_from_ac called: " .. cjson.encode(payload))

    -- 设置系统模式为AP
    uci:set("system", "nhx", "mode", "ap")

    -- 处理LAN配置
    if payload.lan then
        set_lan_settings(payload.lan)
    end

    -- 处理WiFi配置
    if payload.wifi then
        -- 这里可以调用WiFi配置函数
        write_log("[AC] WiFi configuration in AP mode: " .. cjson.encode(payload.wifi))
    end

    uci:save("system")
    uci:commit("system")
    uci:save("network")
    uci:commit("network")

    write_log("[AC] AP mode configuration applied")
    return true, "AP mode configuration applied by AC"
end

function M.get_config_for_ac()
    local config = {
        mode = "ap",
        system_mode = get_system_mode(),
        lan = get_lan_settings(),
        wifi = {
            wifi_2_4g = get_wifi_2_4g_settings(),
            wifi_5g = get_wifi_5g_settings(),
            wifi_6g = get_wifi_6g_settings()
        }
    }
    write_log("[AC] get_config_for_ac: retrieved AP mode config")
    return config
end

-- 仅在明确作为 CGI 脚本运行时执行
if arg and arg[0] and arg[0]:match("ap_mode%.lua") and is_cgi() then
    local function run()
        write_log("AP Mode Settings API started")
        route_api()
        write_log("AP Mode Settings API finished")
    end
    run()
end

return M