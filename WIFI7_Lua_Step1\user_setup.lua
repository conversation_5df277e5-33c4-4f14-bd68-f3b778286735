#!/usr/bin/lua
-- Copyright (c) 2024 The Linux Foundation. All rights reserved.
-- Not a Contribution.

-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

-- Lua环境：此脚本在OpenWRT的Lua环境中运行，确保有必要的Lua库，如luci.sys, luci.jsonc和luci.uci

-- UCI：这个脚本使用了uci库来直接操作配置文件。这比直接读写文件更为安全和标准。

-- Base64编码：在获取用户名和密码时，用户名和密码会进行base64编码以满足API格式的要求。

-- 错误处理：脚本中包含了基本的错误处理，可以根据实际需求进行扩展。

-- 用户设置：
  -- set_user_credentials函数处理用户设置的更新，包括旧密码验证、用户名和新密码的更新。
  -- 使用sys.user.checkpass来验证旧密码。
  -- 使用uci库来更新用户名和uci:commit来保存配置文件。
  -- 使用sys.user.setpasswd来更新密码。

--system配置文件存在并包含username配置项。
-- 由于修改用户名和密码不需要重启任何服务，所以没有添加服务重启的逻辑。
--使用io.write来输出HTTP响应，包括HTTP头和JSON内容。

-- API调用格式 set：
--{
--    "version": "1.0",
--    "sid": "5bdd8aec153a8ff183387bfcea40e3aa",
--    "module": "user_setup",
--    "api": "set",
--    "param": {
--        "old_password": "旧密码",
--        "new_username": "新用户名",
--        "new_password": "新密码"
--    }
--}


-- set 返回参数格式：
--{
--    "module": "user_setup",
--    "version": "1.0",
--    "api": "set",
--    "errcode": 0,
--    "sid": "5bdd8aec153a8ff183387bfcea40e3aa",
--    "result": {
--        "message": "User credentials updated successfully"
--    }
--}

-- API调用格式 get：
--{
--    "version": "1.0",
--    "sid": "5bdd8aec153a8ff183387bfcea40e3aa",
--    "module": "user_setup",
--    "api": "get",
--    "param": {}
--}

-- get 返回参数格式：
--{
--    "module": "user_setup",
--    "version": "1.0",
--    "api": "get",
--    "errcode": 0,
--    "sid": "5bdd8aec153a8ff183387bfcea40e3aa",
--    "result": {
--        "username": "用户名"
--    }
--}


-- 无需重启服务：因为这个功能只是更新用户名和密码，不涉及到配置文件的修改或服务的重启。

-- 安全性：确保处理用户输入时，进行了必要的验证和清理，以防止注入攻击
-- 性能：由于脚本每次都需要读取和写入配置文件，对于频繁调用的API，可以考虑优化，例如通过缓存减少IO操作。
-- 权限：确保脚本以必要的权限运行，修改网络配置文件和重启网络服务需要root权限。

-- 引入模块
local cjson = require("cjson.safe")
local sys = require("luci.sys")
local uci = require("luci.model.uci").cursor()
local log_file = "/tmp/user_setup.log" -- 日志文件路径

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end



-- 路由到具体逻辑
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 获取 POST 数据长度
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""

    -- 读取 POST 数据
    if POSTLength > 0 then
        POST = io.read(POSTLength)
        write_log("Received POST data: " .. (POST or "nil"))
    else
        write_log("No POST data received or CONTENT_LENGTH is 0")
    end

    -- 确保读取成功
    if not POST or POST == "" then
        local error_message = "Failed to retrieve POST data"
        write_log(error_message)
        io.write(cjson.encode({
            module = "user_setup",
            version = "1.0",
            errcode = 1,
            result = { message = error_message }
        }))
        return
    end

    -- 解析 POST 数据为 JSON
    local requestData = cjson.decode(POST)
    if not requestData then
        local error_message = "Invalid JSON input"
        write_log(error_message)
        io.write(cjson.encode({
            module = "user_setup",
            version = "1.0",
            errcode = 2,
            result = { message = error_message }
        }))
        return
    end

    write_log("Parsed request data: " .. cjson.encode(requestData))

    -- 检查请求格式
    if not requestData.version or not requestData.sid or not requestData.module or not requestData.api then
        local error_message = "Invalid request format"
        write_log(error_message)
        io.write(cjson.encode({
            module = "user_setup",
            version = "1.0",
            errcode = 3,
            result = { message = error_message }
        }))
        return
    end

    if requestData.api == "set" then
        write_log("Calling set_user_credentials with data: " .. cjson.encode(requestData))
        set_user_credentials(requestData)
    elseif requestData.api == "get" then
        write_log("Calling get_user_credentials with data: " .. cjson.encode(requestData))
        get_user_credentials(requestData)
    else
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "user_setup",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
    end
end

-- 设置用户凭据
function set_user_credentials(data)
    local old_password = data.param and data.param.old_password or ""
    local new_username = data.param and data.param.new_username or ""
    local new_password = data.param and data.param.new_password or ""

    -- 从 UCI 获取当前用户名和密码
    local current_username = uci:get("system", "nhx", "username") or "admin"
    local current_password = uci:get("system", "nhx", "password") or "admin"

    write_log("Attempting to verify old password for user: " .. current_username)

    -- 验证旧密码
    local is_valid = false
    if current_password == "admin" and old_password == "admin" then
        is_valid = true
    else
        is_valid = sys.user.checkpasswd("root", old_password)
    end

    if not is_valid then
        write_log("Incorrect old password")
        io.write(cjson.encode({
            module = "user_setup",
            version = "1.0",
            api = "set",
            errcode = 3,  -- Error code for incorrect old password
            sid = data.sid,
            result = { message = "Incorrect old password" }
        }))
        return
    end

    write_log("Old password verified successfully")

    -- 更新用户名
    if new_username ~= "" then
        write_log("Updating username to: " .. new_username)
        uci:set("system", "nhx", "username", new_username)
    end

    -- 更新密码
    if new_password ~= "" then
        write_log("Updating password")
        sys.user.setpasswd("root", new_password)
        uci:set("system", "nhx", "password", new_password)
    end

    uci:commit("system")
    write_log("User credentials updated successfully")
    io.write(cjson.encode({
        module = "user_setup",
        version = "1.0",
        api = "set",
        errcode = 0,
        sid = data.sid,
        result = { message = "User credentials updated successfully" }
    }))
end

-- 获取用户设置
function get_user_credentials(data)
    local username = uci:get("system", "nhx", "username") or "admin"
    local password = uci:get("system", "nhx", "password") or "admin"
    
    write_log("User credentials retrieved successfully")
    io.write(cjson.encode({
        module = "user_setup",
        version = "1.0",
        api = "get",
        errcode = 0,
        sid = data.sid,
        result = {
            username = username,
            password = password
        }
    }))
end

-- 检查是否在CGI环境中运行
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- AC 调用接口
local M = {}

function M.set_config_from_ac(payload)
    write_log("[AC] set_config_from_ac called: " .. cjson.encode(payload))

    -- 调用现有的set_user_credentials函数
    local data = { param = payload }
    set_user_credentials(data)

    write_log("[AC] User setup configuration applied")
    return true, "User setup configuration applied by AC"
end

function M.get_config_for_ac()
    local config = {
        current_username = uci:get("system", "nhx", "username") or "admin",
        -- 不返回密码信息，出于安全考虑
        password_set = (uci:get("system", "nhx", "password") ~= nil)
    }
    write_log("[AC] get_config_for_ac: retrieved user setup config")
    return config
end

-- 仅在明确作为 CGI 脚本运行时执行
if arg and arg[0] and arg[0]:match("user_setup%.lua") and is_cgi() then
    local function run()
        write_log("User Setup API started")
        route_api()
        write_log("User Setup API finished")
    end
    run()
end

return M
