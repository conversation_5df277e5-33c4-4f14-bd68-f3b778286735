#!/usr/bin/lua

-- 测试模块加载的脚本
local modules_to_test = {
    "user_event",
    "info", 
    "status",
    "scheduled_reboot",
    "ap_mode",
    "route_mode", 
    "bridge_mode",
    "vlan",
    "user_setup",
    "qos_management",
    "current_device",
    "mac_access_control"
}

print("Testing module loading...")

local success_count = 0
local total_count = 0

for _, module_name in ipairs(modules_to_test) do
    total_count = total_count + 1
    print("Testing " .. module_name .. "...")
    
    local status, result = pcall(require, module_name)
    if status then
        print("  OK: " .. module_name .. " loaded successfully")
        success_count = success_count + 1
    else
        print("  ERROR: " .. module_name .. " failed to load: " .. tostring(result))
    end
end

print(string.format("\nModule loading test complete: %d/%d modules loaded successfully", success_count, total_count))

if success_count == total_count then
    print("All modules loaded successfully! Now testing ac_handlers...")
    
    local status, result = pcall(require, "ac_handlers")
    if status then
        print("SUCCESS: ac_handlers loaded successfully!")
    else
        print("ERROR: ac_handlers failed to load: " .. tostring(result))
    end
else
    print("Some modules failed to load. Fix them before testing ac_handlers.")
end
