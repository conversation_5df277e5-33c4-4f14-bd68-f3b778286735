#!/usr/bin/lua

-- AC/AP自动发现模块，兼容WiFi6协议
-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed under Apache License 2.0

local cjson = require("cjson.safe")
local socket = require("socket")
local ac_comm = require("ac_comm")
local ac_protocol = require("ac_protocol")
local ac_status = require("ac_status")
local config_manager = require("ac_config_manager")
local log = require("ac_log")
local write_log = log.debug

local M = {}

local DISCOVERY_INTERVAL = 5 -- 秒，自动发现包发送间隔
local DISCOVERY_TYPE = 10000 -- 协议自定义类型，区分为发现包
local AC_ROLE = 1 -- 1=AC，2=AP，和WiFi6保持一致
local AP_ROLE = 2
local BROADCAST_IP = "*************"

M.last_discovery_time = 0


-- 构造自动发现包内容
local function build_discovery_payload()
    local devinfo = ac_status.get_device_info() or {}
    local payload = {
        role = AP_ROLE,
        mac = devinfo.mac or "",
        model = devinfo.model or "", -- 优先hostname
        fw_ver = devinfo.fw_ver or "", -- 优先固件版本号
        ip = devinfo.ip or "",
        time = os.time(),
    }
    return cjson.encode(payload)
end

-- 发送自动发现包
local function send_discovery_packet(ap_port)
    if not ap_port then
        log.error("[ac_discovery] ap_port is nil!")
        return
    end
    local payload = build_discovery_payload()
    local devinfo = ac_status.get_device_info() or {}
    -- MAC 必须去掉冒号，补齐12字节+1字节0
    local mac = (devinfo.mac or ""):gsub(":", "")
    mac = string.sub(mac .. string.rep("0", 12), 1, 12)
    local dev_mac = mac .. "\0" -- 13字节
    
    local device_info = ac_status.get_device_info()
    write_log(string.format("[DEBUG] Device info: %s", require("cjson").encode(device_info)))
    
    -- 构造 header
    local header = {
        data_type = DISCOVERY_TYPE,
        dev_mac = mac, -- 12字节字符串
        reserved_1 = "\0",
        proto_version = 1,
        hashcode = string.rep("0", 16),
        reserved_2 = string.rep("\0", 6),
        dev_type = 0,
        errcode = 0,
        super_flag = 0,
        session = 0,
        sec_type = 0,
        data_sum = 1,
        data_idx = 0
    }
    
    --write_log(string.format("[DEBUG] Prepared header: %s", require("cjson").encode(header)))
    --log.debug("[ac_discovery] Header prepared: " .. cjson.encode(header))
    --log.debug("[ac_discovery] Device info: " .. cjson.encode(devinfo))
    --log.debug("[ac_discovery] MAC address: " .. mac)
    --log.debug("[ac_discovery] Header: " .. cjson.encode(header))
    --log.debug("[ac_discovery] Payload: " .. payload)
    
    local packet = ac_protocol.pack_udp_data(header, payload)
    --log.debug("[ac_discovery] Packet length: " .. #packet)
    
    ac_comm.send_udp_broadcast(packet, BROADCAST_IP, ap_port)
    --log.info("[ac_discovery] Sent discovery packet to " .. BROADCAST_IP .. ":" .. tostring(ap_port))
end

-- 非阻塞定时发送接口
function M.maybe_send_discovery_packet(ap_port)
    local now = socket.gettime()
    if now - (M.last_discovery_time or 0) > DISCOVERY_INTERVAL then
        send_discovery_packet(ap_port)
        M.last_discovery_time = now
    end
end

-- 处理收到的包，若为AC响应则写入配置
function M.handle_packet(data, ip, port)
    log.info(string.format("[ac_discovery] handle_packet called with %d bytes from %s:%s", #data, tostring(ip), tostring(port)))
    print(string.format("[ac_discovery] handle_packet called with %d bytes from %s:%s", #data, tostring(ip), tostring(port)))

    log.info("[ac_discovery] Trying to unpack UDP data...")
    print("[ac_discovery] Trying to unpack UDP data...")

    local header, payload_json = ac_protocol.unpack_udp_data(data)

    if not header or not payload_json then
        log.info("[ac_discovery] Failed to unpack UDP data - not a valid AC protocol packet")
        print("[ac_discovery] Failed to unpack UDP data - not a valid AC protocol packet")
        return false
    end

    log.info(string.format("[ac_discovery] Unpacked header: data_type=%s", tostring(header.data_type)))
    log.info(string.format("[ac_discovery] Payload JSON: %s", tostring(payload_json)))
    print(string.format("[ac_discovery] Unpacked header: data_type=%s", tostring(header.data_type)))
    print(string.format("[ac_discovery] Payload JSON: %s", tostring(payload_json)))

    if header.data_type ~= DISCOVERY_TYPE then
        log.info(string.format("[ac_discovery] Not a discovery packet (data_type=%s, expected=%s)", tostring(header.data_type), tostring(DISCOVERY_TYPE)))
        print(string.format("[ac_discovery] Not a discovery packet (data_type=%s, expected=%s)", tostring(header.data_type), tostring(DISCOVERY_TYPE)))
        return false
    end

    local payload = cjson.decode(payload_json)
    if not payload or payload.role ~= AC_ROLE then
        log.info(string.format("[ac_discovery] Invalid payload or not AC role (role=%s, expected=%s)", tostring(payload and payload.role), tostring(AC_ROLE)))
        print(string.format("[ac_discovery] Invalid payload or not AC role (role=%s, expected=%s)", tostring(payload and payload.role), tostring(AC_ROLE)))
        return false
    end

    -- 收到AC响应，写入配置
    log.info("[ac_discovery] Valid AC discovery packet received, updating config...")
    print("[ac_discovery] Valid AC discovery packet received, updating config...")

    local config = config_manager.read() or {}
    config.ac_ip = ip
    config.ac_port = port
    config.ac_discovery_method = "auto"
    config_manager.write(config)
    log.info("[ac_discovery] Discovered AC at " .. ip .. ":" .. tostring(port) .. ", config updated.")
    print("[ac_discovery] Discovered AC at " .. ip .. ":" .. tostring(port) .. ", config updated.")
    return true
end

-- AC自动发现启动函数
function M.start(config)
    if not config then
        log.warn("[ac_discovery] No config provided for start")
        return false, "No config provided"
    end

    log.info("[ac_discovery] Auto discovery started with config: " .. cjson.encode(config))

    -- 如果配置了自动发现，启动发现逻辑
    if config.ac_discovery_method == "auto" then
        log.info("[ac_discovery] Auto discovery mode enabled")
        -- 立即发送一次发现包
        if config.ap_port then
            send_discovery_packet(config.ap_port)
        end
    end

    return true
end

return M