#!/usr/bin/lua

-- Copyright (c) 2013 The Linux Foundation. All rights reserved.
-- Not a Contribution.

-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

-- Lua环境：此脚本在OpenWRT的Lua环境中运行，确保有必要的Lua库，如luci.jsonc和luci.sys
-- UCI：这个脚本使用了uci库来直接操作配置文件。这比直接读写文件更为安全和标准。

-- UCI 配置管理:

--配置文件 mac_filter: 使用 mac_filter 作为 UCI 配置文件名。
--配置段类型 mac_entry: 使用 mac_entry 作为 UCI 配置段类型，用于存储黑名单、白名单和待定名单中的 MAC 地址条目。
--settings 段: 在 mac_filter 配置文件中添加 settings 段，用于存储全局设置，目前只包含 filter_mode (过滤模式：blacklist, whitelist, pending)。
--list_type 选项: 在每个 mac_entry 配置段中，添加 list_type 选项，用于标识该 MAC 地址所属的列表类型 (黑名单、白名单、待定名单)。
--配置初始化: 在脚本启动时，检查 mac_filter 配置文件是否存在，如果不存在则创建默认配置，并将过滤模式设置为 pending (待定名单)。
--过滤模式管理:

--get_filter_mode() 函数: 获取当前 MAC 地址过滤模式 (从 UCI 配置 settings 段读取 filter_mode 选项)。
--set_filter_mode() 函数: 设置 MAC 地址过滤模式 (更新 UCI 配置 settings 段的 filter_mode 选项)。
--switch_filter_mode() 函数: 切换 MAC 地址过滤模式。
--根据新的 mode 参数，调用 apply_blacklist_rules(), apply_whitelist_rules() 或 clear_mac_filter_rules() 应用或清除 iptables 规则。
--调用 set_filter_mode() 更新 UCI 配置中的 filter_mode。
--MAC 地址列表管理:

--add_mac_to_list() 函数: 添加 MAC 地址到指定的列表 (黑名单/白名单/待定名单)。
--生成唯一的 UCI 配置段名称 (使用 list_type 前缀 + 随机字符串)。
--在 UCI 中创建 mac_entry 配置段，并设置 mac, device_name, list_type 选项。
--返回生成的 UCI 配置段 num (用于后续删除操作)。
--delete_mac_from_list() 函数: 从指定列表删除 MAC 地址 (通过 num 数组)。
--遍历 num 数组，根据 num 查找对应的 UCI 配置段，并删除。
--get_macs_in_list() 函数: 获取指定列表 (黑名单/白名单/待定名单) 的所有 MAC 地址条目。
--遍历 UCI 中所有 mac_entry 配置段，筛选 list_type 匹配的条目，提取 num, mac, device_name, list_type 信息。
--iptables 规则管理:

--apply_blacklist_rules() 函数: 应用黑名单 iptables 规则。
--清空 FORWARD 和 INPUT 链中已有的 MAC 过滤规则 (需要更精确的清理，目前是简单清空整个链)。
--遍历黑名单 MAC 地址列表，为每个 MAC 地址添加 DROP 规则到 FORWARD 和 INPUT 链。
--apply_whitelist_rules() 函数: 应用白名单 iptables 规则。
--清空 FORWARD 和 INPUT 链中已有的 MAC 过滤规则。
--设置 FORWARD 和 INPUT 链的默认策略为 DROP (默认拒绝所有)。
--遍历白名单 MAC 地址列表，为每个 MAC 地址添加 ACCEPT 规则到 FORWARD 和 INPUT 链。
--添加允许已建立和相关连接的规则，以及允许 LAN 设备访问路由器的规则 (重要)。
--clear_mac_filter_rules() 函数: 清除所有 MAC 过滤相关的 iptables 规则，恢复 FORWARD 和 INPUT 链的默认策略为 ACCEPT。
--API 接口:

--api_set_mac_filter() 函数: API - 设置 MAC 过滤 (添加 MAC 到黑名单/白名单/待定名单)。
--接收 mac, list_type, device_name 参数。
--调用 add_mac_to_list() 添加 MAC 地址到指定列表。
--如果当前过滤模式与添加的列表类型相同 (且为黑名单或白名单模式)，则重新应用 iptables 规则。
--返回包含 num 的 JSON 响应。
--api_get_mac_filters() 函数: API - 获取 MAC 过滤列表。
--接收 list_type 参数 (指定要获取的列表类型：blacklist, whitelist, pending)。
--调用 get_macs_in_list() 获取指定列表的 MAC 地址信息。
--返回包含 filter_mode, list_type, mac_list 的 JSON 响应。
--api_delete_mac_filter() 函数: API - 删除 MAC 过滤规则 (通过 num 数组)。
--接收 num 数组参数。
--调用 delete_mac_from_list() 删除指定 num 的 MAC 地址条目。
--删除规则后，如果当前是黑名单或白名单模式，则重新应用 iptables 规则。
--返回成功消息 JSON 响应。
--api_switch_mac_filter_mode() 函数: API - 切换 MAC 过滤模式。
--接收 mode 参数 (目标过滤模式：blacklist, whitelist, pending)。
--调用 switch_filter_mode() 切换过滤模式并应用/清除 iptables 规则。
--返回包含 filter_mode 的 JSON 响应。
--API 路由 route_api() 函数:  根据 requestData.api 字段，路由到不同的 API 处理函数 (api_set_mac_filter, api_get_mac_filters, api_delete_mac_filter, api_switch_mac_filter_mode)。

-- 安全性：确保处理用户输入时，进行了必要的验证和清理，以防止注入攻击
-- 性能：由于脚本每次都需要读取和写入配置文件，对于频繁调用的API，可以考虑优化，例如通过缓存减少IO操作。
-- 权限：确保脚本以必要的权限运行，修改网络配置文件和重启网络服务需要root权限。


-- /www/cgi-bin/mac_access_control.lua

-- Set UTF-8 environment for Lua string handling (important!)
os.setlocale("UTF-8")

-- Standard Libraries
local cjson = require("cjson.safe")
local io = require("io")
local os = require("os")
local string = require("string")
local table = require("table")
local uci = require("luci.model.uci") -- This was missing for UCI operations

-- Configuration
local log_file = "/tmp/mac_access_control.log"
local config_file_path = "/etc/mac_filter_config.txt" -- Simplified config file
local device_name_file_path = "/etc/mac_device_names.txt" -- New file for names

-- *** Target wireless interfaces (adjust if necessary) ***
local TARGET_IFNAMES = {
    "ath0", "ath1", "ath2", "ath3",
    "ath10", "ath11", "ath12", "ath13",
    "ath30", "ath31", "ath32", "ath33"
}

-- *** Mapping from interface name to UCI section name (adjust if necessary) ***
-- This assumes a direct mapping like ath0 -> wlan0, ath10 -> wlan10 etc.
-- Verify this against your actual /etc/config/wireless
local IFNAME_TO_SECTION_MAP = {}
for _, ifname in ipairs(TARGET_IFNAMES) do
    local num = ifname:match("ath(%d+)")
    if num then
        IFNAME_TO_SECTION_MAP[ifname] = "wlan" .. num
    end
end

-- Script State Variables (populated by load_config_from_file)
local current_filter_mode = "pending" -- Default mode
local current_mac_list = {}         -- Default empty list

-- =============================================================================
-- Utility Functions
-- =============================================================================

-- Log writing function
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] ") .. message .. "\n")
        file:close()
    else
        -- Fallback to stderr if log file fails
        io.stderr:write("LOGGING ERROR: Cannot open " .. log_file .. " | " .. message .. "\n")
    end
end

-- MAC Address Validation
local function is_valid_mac(mac)
    return type(mac) == "string" and mac:match("^%x%x:%x%x:%x%x:%x%x:%x%x:%x%x$") ~= nil
end

-- Table to string for logging (simple version)
local function table_to_string_simple(t)
    if type(t) ~= "table" then return tostring(t) end
    local items = {}
    for i, v in ipairs(t) do
        items[i] = tostring(v)
    end
    return "{" .. table.concat(items, ", ") .. "}"
end

-- Execute UCI commands and handle commit
-- Returns true if commit was successful (or not needed), false on error
local function execute_uci_commands(commands, action_description)
    write_log("Executing UCI commands for: " .. action_description)
    local changes_made = false
    local success = true

    if #commands == 0 then
        write_log("  No UCI commands to execute.")
        return true -- No action needed is considered success
    end

    for _, cmd in ipairs(commands) do
        write_log("  Executing: " .. cmd)
        local status = os.execute(cmd .. " > /dev/null 2>&1") -- Redirect output
        if status == 0 then
            write_log("    Success.")
            changes_made = true
        else
            write_log("    ERROR: Command failed (Status: " .. tostring(status) .. ")")
            -- Decide if failure is critical. For most sets/dels, we continue.
            -- success = false -- Uncomment if any failure should stop the whole process
        end
    end

    if changes_made then
        write_log("  Committing UCI changes for wireless...")
        local commit_cmd = "uci commit wireless"
        local commit_status = os.execute(commit_cmd)
        if commit_status == 0 then
            write_log("  Commit successful.")
            return true
        else
            write_log("  ERROR: UCI commit failed! (Status: " .. tostring(commit_status) .. ")")
            return false
        end
    else
        write_log("  No effective UCI changes made, skipping commit.")
        return true -- No commit needed is considered success
    end
end

-- =============================================================================
-- Core Logic Functions (File I/O, UCI Application)
-- =============================================================================

-- Load state from the simplified config file
local function load_config_from_file()
    write_log("Loading MAC filter config from: " .. config_file_path)
    current_filter_mode = "pending" -- Reset to default before loading
    current_mac_list = {}         -- Reset to default

    local config_file, err = io.open(config_file_path, "r")
    if not config_file then
        write_log("  Config file not found or error opening (" .. tostring(err) .. "). Using defaults (mode=pending, empty list).")
        return -- Keep defaults
    end

    local line_num = 0
    local mode_found = false
    for line in config_file:lines() do
        line_num = line_num + 1
        local trimmed_line = line:match("^%s*(.-)%s*$") -- Trim whitespace

        if line_num == 1 then
            local mode = trimmed_line:match("^filter_mode:(%S+)$")
            if mode and (mode == "blacklist" or mode == "whitelist" or mode == "pending") then
                current_filter_mode = mode
                mode_found = true
                write_log("  Set filter_mode from file: " .. current_filter_mode)
            else
                write_log("  WARNING: Invalid or missing filter_mode line in config file. Using default 'pending'. Line: " .. line)
                -- Continue reading for MACs even if mode line is bad
            end
        elseif trimmed_line ~= "" then -- Process subsequent non-empty lines as MACs
            if is_valid_mac(trimmed_line) then
                table.insert(current_mac_list, trimmed_line)
                -- write_log("  Loaded MAC: " .. trimmed_line) -- Optional: can be verbose
            else
                write_log("  WARNING: Skipped invalid MAC address format on line " .. line_num .. ": " .. line)
            end
        end
    end

    config_file:close()
    if not mode_found and line_num > 0 then
         write_log("  WARNING: Config file exists but no valid 'filter_mode:' line found. Using default 'pending'.")
    end
    write_log("MAC filter config loaded. Mode: " .. current_filter_mode .. ", List size: " .. #current_mac_list)
end

-- Save state to the simplified config file
local function save_config_to_file()
    write_log("Saving MAC filter config to: " .. config_file_path)
    local config_file, err = io.open(config_file_path, "w")
    if not config_file then
        write_log("ERROR: Cannot open config file for writing: " .. tostring(err))
        return false -- Indicate failure
    end

    -- Write mode
    config_file:write("filter_mode:" .. current_filter_mode .. "\n")
    write_log("  Saved mode: " .. current_filter_mode)

    -- Write MAC list
    for _, mac in ipairs(current_mac_list) do
        config_file:write(mac .. "\n")
    end
    write_log("  Saved " .. #current_mac_list .. " MAC address(es).")

    config_file:close()
    write_log("MAC filter config saved.")
    return true -- Indicate success
end

-- =============================================================================
-- Device Name File Functions (NEW)
-- =============================================================================

-- Load MAC -> Device Name mappings from the separate file
-- Returns a table map: { [mac_lowercase] = device_name }
local function load_device_names()
    local names_map = {}
    local file, err = io.open(device_name_file_path, "r")
    if file then
        write_log("Loading device names from: " .. device_name_file_path)
        for line in file:lines() do
            -- Match format: aa:bb:cc:dd:ee:ff:Device Name Here
            local mac, name = line:match("^(%x%x:%x%x:%x%x:%x%x:%x%x:%x%x):(.*)$")
            if mac and name then
                local mac_lower = mac:lower()
                names_map[mac_lower] = name
                -- write_log("  Loaded name: " .. mac_lower .. " -> " .. name) -- Optional debug
            else
                write_log("  Skipping invalid line in device name file: " .. line)
            end
        end
        file:close()
    else
        write_log("Device name file not found or error opening (" .. device_name_file_path .. "): " .. tostring(err))
        -- Return empty map, file will be created on first add/save
    end
    return names_map
end

-- Save the MAC -> Device Name mappings map back to the file (overwrites)
-- Returns true on success, false on failure
local function save_device_names(names_map)
    write_log("Saving device names to: " .. device_name_file_path)
    local file, err = io.open(device_name_file_path, "w")
    if not file then
        write_log("ERROR: Cannot open device name file for writing: " .. tostring(err))
        return false
    end

    local count = 0
    for mac_lower, name in pairs(names_map) do
        -- Ensure valid MAC format before writing (safety check)
        if is_valid_mac(mac_lower) then
            file:write(mac_lower .. ":" .. name .. "\n")
            count = count + 1
        else
             write_log("  Skipping save for invalid MAC key in names_map: " .. tostring(mac_lower))
        end
    end

    file:close()
    write_log("Saved " .. count .. " device name mapping(s).")
    return true
end

-- Apply ONLY the macfilter policy to UCI
-- policy: "allow", "deny", "disable"
local function apply_uci_policy_only(policy)
    local uci_commands = {}
    for _, ifname in ipairs(TARGET_IFNAMES) do
        local uci_section = IFNAME_TO_SECTION_MAP[ifname]
        if uci_section then
            table.insert(uci_commands, string.format("uci set wireless.%s.macfilter='%s'", uci_section, policy))
        else
            write_log("  WARNING: No UCI section mapping for ifname '" .. ifname .. "'. Skipping policy set.")
        end
    end
    return execute_uci_commands(uci_commands, "Applying policy '" .. policy .. "'")
end

-- Apply ONLY the maclist to UCI (or delete if list is empty)
local function apply_uci_list_only(mac_list_table)
    local uci_commands = {}
    local macs_string = table.concat(mac_list_table, " ")

    for _, ifname in ipairs(TARGET_IFNAMES) do
        local uci_section = IFNAME_TO_SECTION_MAP[ifname]
        if uci_section then
            if #mac_list_table == 0 then
                table.insert(uci_commands, string.format("uci del wireless.%s.maclist", uci_section))
            else
                table.insert(uci_commands, string.format("uci set wireless.%s.maclist='%s'", uci_section, macs_string))
            end
        else
             write_log("  WARNING: No UCI section mapping for ifname '" .. ifname .. "'. Skipping list set/del.")
        end
    end
    return execute_uci_commands(uci_commands, "Applying MAC list (Count: " .. #mac_list_table .. ")")
end

-- Apply BOTH policy and list to UCI
local function apply_uci_policy_and_list(policy, mac_list_table)
    local uci_commands = {}
    local macs_string = table.concat(mac_list_table, " ")
    local effective_policy = policy

    -- If list is empty, policy MUST be disable, regardless of input policy
    if #mac_list_table == 0 then
        effective_policy = "disable"
    end

    for _, ifname in ipairs(TARGET_IFNAMES) do
        local uci_section = IFNAME_TO_SECTION_MAP[ifname]
        if uci_section then
            -- Set policy first
            table.insert(uci_commands, string.format("uci set wireless.%s.macfilter='%s'", uci_section, effective_policy))
            -- Set or delete list
            if #mac_list_table == 0 then
                 table.insert(uci_commands, string.format("uci del wireless.%s.maclist", uci_section))
            else
                 table.insert(uci_commands, string.format("uci set wireless.%s.maclist='%s'", uci_section, macs_string))
            end
        else
            write_log("  WARNING: No UCI section mapping for ifname '" .. ifname .. "'. Skipping policy/list set.")
        end
    end
    return execute_uci_commands(uci_commands, "Applying policy '" .. effective_policy .. "' and MAC list (Count: " .. #mac_list_table .. ")")
end

-- Asynchronous WiFi Restart Function (copied from original, seems okay)
local function async_restart_wifi()
    write_log("Scheduling asynchronous WiFi restart...")
    local restart_cmd = [[
        /bin/sh -c '
            echo "[$(date)] Starting async WiFi restart sequence" >> /tmp/wifi_restart.log;
            sync;
            echo "[$(date)] Executing wifi command" >> /tmp/wifi_restart.log;
            wifi >> /tmp/wifi_restart.log 2>&1;
            local wifi_exit_code=$?
            echo "[$(date)] wifi command exited with code: $wifi_exit_code" >> /tmp/wifi_restart.log;
            sleep 2;
            # Attempt to check status - adjust command if ubus/hostapd differs
            if ubus list hostapd.* &> /dev/null; then
                local first_hapd=$(ubus list hostapd.* | head -n 1)
                if [ -n "$first_hapd" ]; then
                    echo "[$(date)] Checking clients on $first_hapd" >> /tmp/wifi_restart.log;
                    ubus call $first_hapd get_clients >> /tmp/wifi_restart.log 2>&1;
                    if [ $? -eq 0 ]; then
                        echo "[$(date)] WiFi service check successful (via get_clients)" >> /tmp/wifi_restart.log;
                    else
                        echo "[$(date)] WiFi service check potentially failed (get_clients error)" >> /tmp/wifi_restart.log;
                    fi;
                else
                    echo "[$(date)] No hostapd interfaces found via ubus" >> /tmp/wifi_restart.log;
                fi
            else
                echo "[$(date)] ubus hostapd check failed, cannot confirm WiFi status" >> /tmp/wifi_restart.log;
            fi
            echo "[$(date)] Async WiFi restart sequence nominally completed" >> /tmp/wifi_restart.log;
        ' &
    ]]
    local exec_status = os.execute(restart_cmd)
    write_log("os.execute for async restart command returned: " .. tostring(exec_status))
end

-- =============================================================================
-- API Implementation Functions
-- =============================================================================

-- API: Get current mode and list (Modified to include device names)
local function api_get_mac_list(data)
    write_log("API get: Request received.")
    -- Assumes load_config_from_file() ran at script start giving current_filter_mode and current_mac_list

    -- Load device names
    local device_names_map = load_device_names()

    -- Build the result list with device names
    local result_mac_list = {}
    for _, mac_lower in ipairs(current_mac_list) do
        local device_name = device_names_map[mac_lower] or "-" -- Use default if name not found
        table.insert(result_mac_list, {
            mac = mac_lower, -- Return consistent lowercase MAC
            device_name = device_name
        })
    end

    local response = {
        errcode = 0,
        message = "Successfully retrieved current MAC filter settings.",
        result = {
            filter_mode = current_filter_mode,
            mac_list = result_mac_list -- Return the list of objects
        }
    }
    write_log("API get: Returning Mode: " .. current_filter_mode .. ", List size: " .. #result_mac_list)
    return cjson.encode(response)
end

-- API: Switch filter mode
-- API: Switch filter mode (Corrected for pending <-> blacklist/whitelist transitions)
local function api_switch_filter_mode(data)
    local param = data.param or {}
    local new_mode = param.filter_mode

    write_log("API switch: Request received. Target mode: " .. tostring(new_mode))

    if not new_mode or (new_mode ~= "blacklist" and new_mode ~= "whitelist" and new_mode ~= "pending") then
        write_log("API switch: Invalid or missing filter_mode parameter.")
        return cjson.encode({ errcode = 5, message = "Invalid or missing filter_mode (blacklist, whitelist, or pending)" })
    end

    -- Handling case where mode is already set (Consistency check)
    if new_mode == current_filter_mode then
        write_log("API switch: Mode is already '" .. new_mode .. "'. Re-applying state for consistency.")
        local commit_done = false
        if current_filter_mode == "pending" then
            -- Re-apply 'disable' policy AND ensure list is deleted in UCI
            commit_done = apply_uci_policy_and_list("disable", {}) 
        else
            -- Re-apply 'allow'/'deny' policy AND ensure list is present in UCI
            -- It's safer to re-apply both policy and list here too.
            local policy = (current_filter_mode == "blacklist" and "deny") or "allow"
            commit_done = apply_uci_policy_and_list(policy, current_mac_list) -- Re-apply list as well
        end

         if commit_done then
            async_restart_wifi() 
            return cjson.encode({ errcode = 0, message = "Filter mode already '" .. new_mode .. "'. State re-applied. WiFi restarting.", result = { filter_mode = current_filter_mode } })
        else
             return cjson.encode({ errcode = 0, message = "Filter mode already '" .. new_mode .. "'. State re-application failed or no changes needed.", result = { filter_mode = current_filter_mode } })
        end
    end

    -- *** 开始修改点 (Mode is changing) ***
    write_log("API switch: Changing mode from '" .. current_filter_mode .. "' to '" .. new_mode .. "'.")
    current_filter_mode = new_mode

    -- Save only the mode change to the config file. List remains untouched in the file.
    if not save_config_to_file() then
         return cjson.encode({ errcode = 10, message = "Failed to save configuration file after mode switch."})
    end

    -- Apply changes to UCI based on the NEW mode
    local commit_done = false
    if current_filter_mode == "pending" then
        -- ** Switching TO pending **
        -- Disable filter AND delete UCI list
        write_log("API switch: Applying 'disable' policy and deleting UCI maclist for pending mode.")
        commit_done = apply_uci_policy_and_list("disable", {}) 
    elseif current_filter_mode == "blacklist" then
        -- ** Switching TO blacklist (potentially from pending) **
        -- Enable 'deny' policy AND write current list (from file/memory) back to UCI
        write_log("API switch: Applying 'deny' policy and setting UCI maclist for blacklist mode.")
        commit_done = apply_uci_policy_and_list("deny", current_mac_list)
    elseif current_filter_mode == "whitelist" then
        -- ** Switching TO whitelist (potentially from pending) **
        -- Enable 'allow' policy AND write current list (from file/memory) back to UCI
        write_log("API switch: Applying 'allow' policy and setting UCI maclist for whitelist mode.")
        commit_done = apply_uci_policy_and_list("allow", current_mac_list)
    end
    -- *** 结束修改点 ***

    local response_message = "Filter mode switched to '" .. current_filter_mode .. "'."
    if commit_done then
        async_restart_wifi()
        response_message = response_message .. " WiFi restarting."
    else
        response_message = response_message .. " Failed to apply UCI changes or no changes needed."
    end

    return cjson.encode({
        errcode = 0, 
        message = response_message,
        result = { filter_mode = current_filter_mode }
    })
end

-- API: Delete MAC address(es)
-- API: Delete MAC address(es) (Updated to handle device name file and return names)
local function api_del_mac_filter(data)
    local param = data.param or {}
    local mac_to_delete_input = param.mac -- Expecting string or array of strings

    write_log("API del: Request received. MAC(s) to delete: " .. table_to_string_simple(mac_to_delete_input))

    if not mac_to_delete_input or (type(mac_to_delete_input) ~= "string" and type(mac_to_delete_input) ~= "table") then
        write_log("API del: Missing or invalid 'mac' parameter.")
        return cjson.encode({ errcode = 5, message = "Missing or invalid 'mac' parameter (expecting string or array of strings)" })
    end

    -- Build map of lowercase MACs to remove
    local macs_to_remove_map = {}
    local invalid_mac_found = false
    if type(mac_to_delete_input) == "string" then
        if is_valid_mac(mac_to_delete_input) then
            macs_to_remove_map[mac_to_delete_input:lower()] = true
        else
            invalid_mac_found = true
        end
    else -- It's a table
        for _, mac_str in ipairs(mac_to_delete_input) do
            if type(mac_str) == "string" and is_valid_mac(mac_str) then
                macs_to_remove_map[mac_str:lower()] = true
            else
                write_log("API del: Skipping invalid MAC format in input array: " .. tostring(mac_str))
                invalid_mac_found = true
            end
        end
    end

    if invalid_mac_found and next(macs_to_remove_map) == nil then
         write_log("API del: No valid MAC addresses provided for deletion.")
         return cjson.encode({ errcode = 8, message = "No valid MAC addresses provided for deletion." })
    end
     if next(macs_to_remove_map) == nil then
          write_log("API del: Internal error - macs_to_remove_map is empty after processing input.")
          -- This case implies valid input yielded no MACs to process, maybe return success "not found"?
          return cjson.encode({ errcode = 0, message = "No valid MAC addresses specified for deletion.", result = { filter_mode = current_filter_mode, mac_list = {} } }) -- Return empty list if input was bad
     end

    -- Load current state is assumed done at script start (current_filter_mode, current_mac_list)

    -- Filter the current MAC list
    local original_count = #current_mac_list
    local updated_mac_list = {}
    local actual_removed_macs_lower = {} -- Keep track of MACs confirmed removed from list
    local removed_count = 0
    for _, mac_lower in ipairs(current_mac_list) do
        if not macs_to_remove_map[mac_lower] then
            table.insert(updated_mac_list, mac_lower)
        else
            removed_count = removed_count + 1
            actual_removed_macs_lower[mac_lower] = true -- Mark this MAC as actually removed
            write_log("API del: Marking MAC for removal: " .. mac_lower)
        end
    end

    if removed_count == 0 then
        write_log("API del: MAC address(es) specified were not found in the current list.")
        -- Still need to load names to return the current list correctly formatted
        local device_names_map_for_return = load_device_names()
        local result_list_notfound = {}
         for _, mac_lower in ipairs(current_mac_list) do
            local name = device_names_map_for_return[mac_lower] or "-"
            table.insert(result_list_notfound, {mac = mac_lower, device_name = name})
        end
        return cjson.encode({ 
            errcode = 0, 
            message = "Specified MAC address(es) not found in the current list.", 
            result = { filter_mode = current_filter_mode, mac_list = result_list_notfound } 
        })
    end

    write_log("API del: Removed " .. removed_count .. " MAC(s) from internal list. New list size: " .. #updated_mac_list)
    current_mac_list = updated_mac_list

    -- Save the updated main config file first
    if not save_config_to_file() then
        return cjson.encode({ errcode = 10, message = "Failed to save main configuration file after deletion."})
    end

    -- *** 开始修改点: Update Device Name File ***
    local device_names_map = load_device_names()
    local names_changed = false
    for mac_lower_removed, _ in pairs(actual_removed_macs_lower) do
        if device_names_map[mac_lower_removed] then
            device_names_map[mac_lower_removed] = nil -- Remove the entry
            names_changed = true
            write_log("API del: Removing device name mapping for " .. mac_lower_removed)
        end
    end

    if names_changed then
        if not save_device_names(device_names_map) then
            -- Log error, but potentially continue UCI update? Or return error? Let's return error for safety.
            return cjson.encode({ errcode = 12, message = "Failed to save device name file after deletion."})
        end
    else
         write_log("API del: No corresponding device names found or removed from name file.")
    end
    -- *** 结束修改点 ***


    -- Update UCI list (this part remains the same logic)
    local commit_done = apply_uci_list_only(current_mac_list)
    local uci_policy_updated = false
    local mode_changed_to_pending = false

    -- Handle empty list case (remains the same logic)
    if #current_mac_list == 0 and commit_done then
        write_log("API del: List is now empty. Ensuring UCI policy is disabled.")
        if apply_uci_policy_only("disable") then
             uci_policy_updated = true
             if current_filter_mode ~= "pending" then
                 write_log("API del: List empty, changing internal mode to 'pending'.")
                 current_filter_mode = "pending"
                 mode_changed_to_pending = true
                 save_config_to_file() -- Save the mode change as well
             end
        else
             write_log("API del: WARNING: Failed to set UCI policy to disable after list became empty.")
        end
    end

    -- Determine if WiFi restart is needed
    local wifi_restarted = false
    if commit_done or uci_policy_updated then -- Restart if list or policy changed in UCI
        async_restart_wifi()
        wifi_restarted = true
    end

    -- Prepare response message
    local response_message = removed_count .. " MAC address(es) removed successfully."
    if mode_changed_to_pending then response_message = response_message .. " Mode set to pending as list is now empty." end
    if wifi_restarted then response_message = response_message .. " WiFi restarting."
    elseif commit_done == false and uci_policy_updated == false and #current_mac_list > 0 then response_message = response_message .. " Failed to update UCI or no changes needed." end


    -- *** 开始修改点: Prepare result list with names ***
    -- Re-load names map in case it was just updated or if mode changed to pending
    local final_device_names_map = load_device_names() 
    local result_mac_list_with_names = {}
    for _, mac_lower in ipairs(current_mac_list) do
        local device_name = final_device_names_map[mac_lower] or "-"
        table.insert(result_mac_list_with_names, {
            mac = mac_lower,
            device_name = device_name
        })
    end
    -- *** 结束修改点 ***

    return cjson.encode({
        errcode = 0,
        message = response_message,
        result = {
            filter_mode = current_filter_mode, -- Return the potentially updated mode
            mac_list = result_mac_list_with_names -- Return remaining list with names
        }
    })
end

-- =============================================================================
-- API Implementation Functions
-- =============================================================================

-- API: Add MAC address(es) with device names (NEW)
local function api_add_mac_filter(data)
    local param = data.param or {}
    local request_mode = param.filter_mode -- Mode this add applies to
    local mac_list_input = param.mac_list -- Array of {mac="...", device_name="..."}

    write_log("API add: Request received. Target Mode: " .. tostring(request_mode) .. ", Input Count: " .. #tostring(mac_list_input))

    -- Validate request mode
    if not request_mode or (request_mode ~= "blacklist" and request_mode ~= "whitelist" and request_mode ~= "pending") then
        write_log("API add: Invalid or missing filter_mode parameter.")
        return cjson.encode({ errcode = 6, message = "Invalid or missing filter_mode (blacklist, whitelist, or pending)" })
    end

    -- Validate input list format
    if not mac_list_input or type(mac_list_input) ~= "table" or #mac_list_input == 0 then
         write_log("API add: Missing or empty mac_list array (expecting array of {mac, device_name} objects).")
         return cjson.encode({ errcode = 5, message = "Missing or empty mac_list array" })
    end

    -- Load current state from main config file
    -- load_config_from_file() -- Assumed to be called at script start

    -- Check if the request mode matches the actual current mode
    if request_mode ~= current_filter_mode then
         write_log("API add: Request mode '" .. request_mode .. "' does not match current system mode '" .. current_filter_mode .. "'. Add operation rejected.")
         return cjson.encode({ errcode = 11, message = "Cannot add MAC to a list that is not the currently active mode. Switch mode first."})
    end

    -- Load existing device names
    local device_names_map = load_device_names()

    local added_macs = {} -- Keep track of MACs actually added in this run
    local skipped_invalid = 0
    local skipped_duplicate = 0
    local macs_to_add_to_uci = {} -- Only needed if mode is blacklist/whitelist

    -- Process input list for additions
    for _, item in ipairs(mac_list_input) do
        if type(item) == "table" and item.mac and type(item.mac) == "string" then
            local mac_input = item.mac
            local device_name = item.device_name or "-" -- Use default if name is missing

            if is_valid_mac(mac_input) then
                local mac_lower = mac_input:lower()
                local already_exists = false
                -- Check if MAC already in current_mac_list
                for _, existing_mac in ipairs(current_mac_list) do
                    if existing_mac:lower() == mac_lower then
                        already_exists = true
                        break
                    end
                end

                if not already_exists then
                    write_log("API add: Adding MAC: " .. mac_lower .. " with Name: " .. device_name)
                    table.insert(current_mac_list, mac_lower)
                    table.insert(added_macs, mac_lower) -- Track added MAC
                    if current_filter_mode ~= "pending" then -- Prepare for UCI update only if not pending
                       table.insert(macs_to_add_to_uci, mac_lower) 
                    end
                    -- Update/Add device name mapping
                    device_names_map[mac_lower] = device_name
                else
                    skipped_duplicate = skipped_duplicate + 1
                    write_log("API add: Skipping duplicate MAC: " .. mac_lower)
                    -- Optional: Update device name even if MAC exists? Current logic skips.
                    -- device_names_map[mac_lower] = device_name -- Uncomment to allow name update on duplicates
                end
            else
                skipped_invalid = skipped_invalid + 1
                write_log("API add: Skipping invalid MAC format: " .. tostring(mac_input))
            end
        else
             write_log("API add: Skipping invalid item format in mac_list: " .. tostring(item))
             skipped_invalid = skipped_invalid + 1
        end
    end

    if #added_macs == 0 then
         write_log("API add: No new valid MAC addresses were added.")
         local msg = "No new MAC addresses added."
         if skipped_duplicate > 0 then msg = msg .. " Found " .. skipped_duplicate .. " duplicate(s)." end
         if skipped_invalid > 0 then msg = msg .. " Skipped " .. skipped_invalid .. " invalid entry(ies)." end
         return cjson.encode({errcode = 0, message = msg, result = { filter_mode = current_filter_mode }})
    end

    -- Save updated main config file (with added MACs)
    if not save_config_to_file() then
        return cjson.encode({ errcode = 10, message = "Failed to save main configuration file after adding MACs."})
    end

    -- Save updated device name mappings
    if not save_device_names(device_names_map) then
         -- Log error but maybe continue? Or return error? Let's return error.
        return cjson.encode({ errcode = 12, message = "Failed to save device name file after adding MACs."})
    end

    -- Update UCI only if needed (mode is blacklist or whitelist)
    local commit_done = false
    local wifi_restarted = false
    if current_filter_mode == "blacklist" or current_filter_mode == "whitelist" then
         write_log("API add: Updating UCI maclist for mode: " .. current_filter_mode)
         -- We need to apply the *full* updated list to UCI
         commit_done = apply_uci_list_only(current_mac_list)
         if commit_done then
             async_restart_wifi()
             wifi_restarted = true
         end
    else
         write_log("API add: Mode is pending, skipping UCI update.")
         commit_done = true -- Indicate success as no UCI needed
    end

    local response_message = #added_macs .. " MAC address(es) added successfully to " .. current_filter_mode .. " list."
    if skipped_duplicate > 0 then response_message = response_message .. " Skipped " .. skipped_duplicate .. " duplicate(s)." end
    if skipped_invalid > 0 then response_message = response_message .. " Skipped " .. skipped_invalid .. " invalid entry(ies)." end
    if wifi_restarted then response_message = response_message .. " WiFi restarting."
    elseif commit_done == false then response_message = response_message .. " Failed to update UCI or no changes needed." end


    return cjson.encode({
        errcode = 0,
        message = response_message,
        result = {
            filter_mode = current_filter_mode,
            added_count = #added_macs
            -- Maybe return the full updated list here? Let's keep it simple for now.
        }
    })
end

-- API: Set (overwrite) mode and list (Corrected UCI handling for pending mode)
local function api_set_mac_filter(data)
    local param = data.param or {}
    local new_mode = param.filter_mode
    local new_mac_list_input = param.mac_list -- Expecting array of strings

    write_log("API set: Request received. Mode: " .. tostring(new_mode) .. ", Input List size: " .. #tostring(new_mac_list_input)) -- Log input size

    -- Validate mode
    if not new_mode or (new_mode ~= "blacklist" and new_mode ~= "whitelist" and new_mode ~= "pending") then
        write_log("API set: Invalid or missing filter_mode.")
        return cjson.encode({ errcode = 6, message = "Invalid or missing filter_mode (blacklist, whitelist, or pending)" })
    end

    -- Validate MAC list input type (Treat missing/null as empty list for 'set')
    if new_mac_list_input == nil or type(new_mac_list_input) ~= "table" then
         write_log("API set: Missing or invalid mac_list (assuming empty list).")
         new_mac_list_input = {}
    end

    -- Validate individual MACs in the list
    local validated_mac_list = {}
    local invalid_count = 0
    for _, mac in ipairs(new_mac_list_input) do
        -- Ensure MACs are stored consistently, e.g., lowercase
        if type(mac) == "string" and is_valid_mac(mac) then
             -- Optional: Add check for duplicates within the input list if desired
             table.insert(validated_mac_list, mac:lower()) -- Store lowercase
        else
            write_log("API set: Skipping invalid MAC format in input list: " .. tostring(mac))
            invalid_count = invalid_count + 1
        end
    end
    write_log("API set: Validated list size: " .. #validated_mac_list .. ". Skipped " .. invalid_count .. " invalid entries.")

    -- Update internal state (OVERWRITE based on validated input)
    current_filter_mode = new_mode
    current_mac_list = validated_mac_list

    -- Save state to config file first
    if not save_config_to_file() then
         return cjson.encode({ errcode = 10, message = "Failed to save configuration file."})
    end

    -- *** 开始修改点: UCI Update Logic ***
    local commit_done = false
    if current_filter_mode == "pending" then
        -- ** Mode is Pending **
        -- Regardless of whether current_mac_list has MACs,
        -- UCI must have 'disable' policy and NO maclist.
        write_log("API set: Mode is pending. Applying 'disable' policy and deleting UCI maclist.")
        commit_done = apply_uci_policy_and_list("disable", {}) -- Force disable and empty list for UCI

    else
        -- ** Mode is Blacklist or Whitelist **
        -- Apply the correct policy AND the list to UCI.
        local policy = (current_filter_mode == "blacklist" and "deny") or "allow"
        write_log("API set: Mode is " .. current_filter_mode .. ". Applying policy '" .. policy .. "' and list to UCI.")
        commit_done = apply_uci_policy_and_list(policy, current_mac_list)
    end
    -- *** 结束修改点 ***

    local response_message = "MAC filter settings applied successfully. Mode: " .. current_filter_mode .. ", List size: " .. #current_mac_list .. "."
     if invalid_count > 0 then
         response_message = response_message .. " Skipped " .. invalid_count .. " invalid MAC entries."
     end

    if commit_done then
        async_restart_wifi()
        response_message = response_message .. " WiFi restarting."
    else
        response_message = response_message .. " No effective UCI changes made or commit failed."
    end

    return cjson.encode({
        errcode = 0,
        message = response_message,
        result = {
            -- Return the state as it is saved in the config file
            filter_mode = current_filter_mode,
            mac_list = current_mac_list -- Return the list that was just set
        }
    })
end

-- 获取当前应用模式的辅助函数
local function get_current_apply_mode()
    local mode_file = "/etc/config_apply_mode_status"
    local f = io.open(mode_file, "r")
    if f then
        local mode = f:read("*l") -- Read the first line
        f:close()
        mode = mode and mode:gsub("^%s*(.-)%s*$", "%1") -- Trim whitespace
        if mode == "immediate" or mode == "deferred" then
            -- write_log("Read apply_mode from " .. mode_file .. ": " .. mode)
            return mode
        else
            -- write_log("Invalid content in " .. mode_file .. ": '" .. (mode or "nil") .. "'. Defaulting to 'immediate'.")
            return "immediate"
        end
    else
        -- write_log("Could not open " .. mode_file .. ". Defaulting to 'immediate'.")
        return "immediate"
    end
end

-- 清除所有 MAC 过滤相关的 iptables 规则
local function clear_mac_filter_rules()
    write_log("Clearing all MAC filter iptables rules...")
    -- Restore default policy to ACCEPT for FORWARD and INPUT chains
    os.execute("iptables -P FORWARD ACCEPT")
    os.execute("iptables -P INPUT ACCEPT")
    write_log("  Set FORWARD and INPUT default policies to ACCEPT.")

    -- Flush and delete the custom chains
    os.execute("iptables -F MAC_FILTER_FORWARD 2>/dev/null")
    os.execute("iptables -X MAC_FILTER_FORWARD 2>/dev/null")
    os.execute("iptables -F MAC_FILTER_INPUT 2>/dev/null")
    os.execute("iptables -X MAC_FILTER_INPUT 2>/dev/null")
    write_log("  Flushed and deleted custom MAC filter chains.")

    -- Remove jump rules from FORWARD and INPUT chains
    os.execute("iptables -D FORWARD -j MAC_FILTER_FORWARD 2>/dev/null")
    os.execute("iptables -D INPUT -j MAC_FILTER_INPUT 2>/dev/null")
    write_log("  Removed jump rules from FORWARD and INPUT.")

    -- Restart firewall if immediate apply mode is set
    local apply_mode = get_current_apply_mode()
    if apply_mode == "immediate" then
        write_log("Restarting firewall after clearing MAC filter rules (immediate mode)...")
        os.execute("/etc/init.d/firewall restart >/dev/null 2>&1")
    else
        write_log("Firewall restart deferred after clearing MAC filter rules.")
    end
    write_log("All MAC filter iptables rules cleared.")
end

-- 根据新的过滤模式，应用或清除 iptables 规则
local function switch_filter_mode(mode)
    write_log("Switching filter mode to: " .. mode)
    current_filter_mode = mode

    if mode == "blacklist" then
        apply_blacklist_rules()
    elseif mode == "whitelist" then
        apply_whitelist_rules()
    else -- pending mode or any other invalid mode
        clear_mac_filter_rules()
    end

    save_config_to_file() -- Save the new mode to file

    local apply_mode = get_current_apply_mode()
    if apply_mode == "immediate" then
        write_log("Firewall restart triggered by switch_filter_mode (immediate mode)...")
        os.execute("/etc/init.d/firewall restart >/dev/null 2>&1")
    else
        write_log("Firewall restart deferred by switch_filter_mode.")
    end
end

-- API: 设置 MAC 过滤
local function api_set_mac_filter(request_data)
    write_log("api_set_mac_filter called.")
    local param = request_data.param or {}
    local mac = param.mac
    local list_type = param.list_type -- blacklist, whitelist, pending
    local device_name = param.device_name or ""

    if not is_valid_mac(mac) then
        write_log("Invalid MAC address received: " .. (mac or "nil"))
        send_error_response(request_data.sid, 5, "Invalid MAC address.")
        return
    end
    if not list_type or (list_type ~= "blacklist" and list_type ~= "whitelist" and list_type ~= "pending") then
        write_log("Invalid list_type received: " .. (list_type or "nil"))
        send_error_response(request_data.sid, 6, "Invalid list type. Must be 'blacklist', 'whitelist', or 'pending'.")
        return
    end

    -- If adding to a new list type, remove from others first to prevent inconsistency
    for _, existing_mac_entry in ipairs(current_mac_list) do
        if existing_mac_entry.mac == mac then
            -- If it's already in the target list, just update device_name if different
            if existing_mac_entry.list_type == list_type then
                if existing_mac_entry.device_name ~= device_name then
                    existing_mac_entry.device_name = device_name
                    write_log("Updated device name for existing MAC " .. mac .. " in " .. list_type .. ".")
                    save_config_to_file()
                    send_success_response(request_data.sid, "MAC address device name updated.")
                    return
                else
                    write_log("MAC " .. mac .. " already exists in " .. list_type .. " with same device name. No action needed.")
                    send_success_response(request_data.sid, "MAC address already exists.")
                    return
                end
            else
                -- If it's in a different list, remove it from there
                delete_mac_from_list({existing_mac_entry.num}, true) -- Don't trigger firewall restart yet
                write_log("Removed MAC " .. mac .. " from old list type " .. existing_mac_entry.list_type .. ".")
                break -- Only one entry per MAC is expected
            end
        end
    end

    local new_mac_entry = {
        num = generate_unique_num(),
        mac = mac,
        device_name = device_name,
        list_type = list_type
    }
    table.insert(current_mac_list, new_mac_entry)
    write_log("Added MAC " .. mac .. " to " .. list_type .. " list (num: " .. new_mac_entry.num .. ").")
    save_config_to_file()

    -- If the current filter mode is the same as the list being modified (and it's not pending),
    -- re-apply rules immediately.
    local apply_mode = get_current_apply_mode()
    if apply_mode == "immediate" then
        if list_type == current_filter_mode and (list_type == "blacklist" or list_type == "whitelist") then
            write_log("Re-applying MAC filter rules due to immediate mode and matching list type.")
            if current_filter_mode == "blacklist" then
                apply_blacklist_rules()
            elseif current_filter_mode == "whitelist" then
                apply_whitelist_rules()
            end
        else
            write_log("No immediate rule re-application needed. Current mode: " .. current_filter_mode .. ", Modified list: " .. list_type)
        end
    else
        write_log("Apply mode is deferred. Rules will not be re-applied immediately.")
    end
    
    send_success_response(request_data.sid, "MAC address added successfully.", {num = new_mac_entry.num})
end

-- API: 删除 MAC 过滤规则
local function api_delete_mac_filter(request_data)
    write_log("api_delete_mac_filter called.")
    local param = request_data.param or {}
    local nums_to_delete = param.num -- Can be a single num or a table of nums

    if not nums_to_delete then
        write_log("Missing 'num' parameter for delete.")
        send_error_response(request_data.sid, 7, "Missing 'num' parameter.")
        return
    end

    local num_list = {}
    if type(nums_to_delete) == "table" then
        num_list = nums_to_delete
    else
        table.insert(num_list, nums_to_delete)
    end

    local deleted_count = delete_mac_from_list(num_list)

    if deleted_count > 0 then
        save_config_to_file() -- Save changes after deletion

        -- If current filter mode is blacklist or whitelist, re-apply rules
        local apply_mode = get_current_apply_mode()
        if apply_mode == "immediate" then
            if current_filter_mode == "blacklist" then
                write_log("Re-applying blacklist rules after deletion (immediate mode)...")
                apply_blacklist_rules()
            elseif current_filter_mode == "whitelist" then
                write_log("Re-applying whitelist rules after deletion (immediate mode)...")
                apply_whitelist_rules()
            else
                write_log("No immediate rule re-application needed after deletion. Current mode: " .. current_filter_mode)
            end
        else
            write_log("Apply mode is deferred. Rules will not be re-applied immediately after deletion.")
        end
        send_success_response(request_data.sid, "MAC filter rule(s) deleted successfully.")
    else
        write_log("No MAC filter rules found for deletion with provided nums.")
        send_success_response(request_data.sid, "No matching MAC filter rules found for deletion.")
    end
end

-- API: 获取 MAC 过滤列表
local function api_get_mac_filters(request_data)
    write_log("api_get_mac_filters called.")
    local param = request_data.param or {}
    local list_type = param.list_type or "all" -- 'all', 'blacklist', 'whitelist', 'pending'

    local mac_list_filtered = {}
    if list_type == "all" then
        mac_list_filtered = current_mac_list
    else
        for _, mac_entry in ipairs(current_mac_list) do
            if mac_entry.list_type == list_type then
                table.insert(mac_list_filtered, mac_entry)
            end
        end
    end

    -- Sort the list for consistent output (e.g., by num)
    table.sort(mac_list_filtered, function(a, b)
        return tonumber(a.num) < tonumber(b.num)
    end)

    write_log("Returning MAC filter list for type: " .. list_type .. ", count: " .. #mac_list_filtered)
    send_success_response(request_data.sid, "MAC filter list retrieved.", {
        filter_mode = current_filter_mode,
        list_type = list_type,
        mac_list = mac_list_filtered
    })
end

-- API: 切换 MAC 过滤模式
local function api_switch_mac_filter_mode(request_data)
    write_log("api_switch_mac_filter_mode called.")
    local param = request_data.param or {}
    local mode = param.mode

    if not mode or (mode ~= "blacklist" and mode ~= "whitelist" and mode ~= "pending") then
        write_log("Invalid mode received for switch_mac_filter_mode: " .. (mode or "nil"))
        send_error_response(request_data.sid, 8, "Invalid mode. Must be 'blacklist', 'whitelist', or 'pending'.")
        return
    end

    if current_filter_mode == mode then
        write_log("Filter mode is already '" .. mode .. "'. No change needed.")
        send_success_response(request_data.sid, "Filter mode already set.", {filter_mode = current_filter_mode})
        return
    end

    switch_filter_mode(mode)
    send_success_response(request_data.sid, "MAC filter mode switched successfully.", {filter_mode = current_filter_mode})
end

-- Response Helpers
local function send_success_response(sid, message, result_data)
    local response = {
        module = "mac_access_control",
        version = "1.0",
        errcode = 0,
        result = { message = message },
        sid = sid
    }
    if result_data then
        for k, v in pairs(result_data) do
            response.result[k] = v
        end
    end
    http.write_json(response)
end

local function send_error_response(sid, errcode, message)
    http.write_json({
        module = "mac_access_control",
        version = "1.0",
        errcode = errcode,
        result = { message = message },
        sid = sid
    })
end

-- API Router
local function route_api()
    load_config_from_file() -- Always load current state before handling API
    load_device_names() -- Load device names at the start

    local api = request_data.api
    if api == "set" then
        api_set_mac_filter(request_data)
    elseif api == "get" then
        api_get_mac_filters(request_data)
    elseif api == "delete" then
        api_delete_mac_filter(request_data)
    elseif api == "switch_mode" then
        api_switch_mac_filter_mode(request_data)
    else
        send_error_response(request_data.sid, 4, "Unknown API: " .. (api or "nil"))
    end
end

-- 检查是否在CGI环境中运行
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- 生成唯一的数字ID
local function generate_unique_num()
    math.randomseed(os.time() + os.clock() * 1000000)
    return math.random(10000, 99999)
end

-- 内部函数：添加MAC到指定列表（简化版本，适配当前数据结构）
local function add_mac_to_list(mac, list_type, device_name)
    write_log("add_mac_to_list called: mac=" .. tostring(mac) .. ", list_type=" .. tostring(list_type) .. ", device_name=" .. tostring(device_name))

    if not is_valid_mac(mac) then
        write_log("Invalid MAC address: " .. tostring(mac))
        return nil
    end

    if not list_type or (list_type ~= "blacklist" and list_type ~= "whitelist" and list_type ~= "pending") then
        write_log("Invalid list_type: " .. tostring(list_type))
        return nil
    end

    device_name = device_name or "AC_Device"

    -- 检查MAC是否已存在
    for i, existing_mac in ipairs(current_mac_list) do
        if existing_mac == mac then
            write_log("MAC " .. mac .. " already exists in list")
            return generate_unique_num() -- 返回一个ID表示成功
        end
    end

    -- 添加新的MAC地址
    table.insert(current_mac_list, mac)
    write_log("Added MAC " .. mac .. " to " .. list_type .. " list")

    -- 保存设备名称到设备名称文件
    local device_file, err = io.open(device_name_file_path, "a")
    if device_file then
        device_file:write(mac .. ":" .. device_name .. "\n")
        device_file:close()
        write_log("Saved device name: " .. mac .. " -> " .. device_name)
    else
        write_log("Warning: Could not save device name: " .. tostring(err))
    end

    -- 保存配置
    save_config_to_file()

    -- 如果当前过滤模式与添加的列表类型相同，重新应用规则
    local apply_mode = get_current_apply_mode()
    if apply_mode == "immediate" then
        if list_type == current_filter_mode and (list_type == "blacklist" or list_type == "whitelist") then
            write_log("Re-applying MAC filter rules due to immediate mode and matching list type")
            if current_filter_mode == "blacklist" then
                apply_blacklist_rules()
            elseif current_filter_mode == "whitelist" then
                apply_whitelist_rules()
            end
        end
    end

    return generate_unique_num()
end

-- AC 调用接口
local M = {}

function M.set_config_from_ac(payload)
    write_log("[AC] set_config_from_ac called: " .. cjson.encode(payload))

    if payload.action == "add" then
        -- 添加MAC到指定列表
        local mac = payload.mac
        local list_type = payload.list_type or "whitelist"  -- whitelist, blacklist, pending
        local device_name = payload.device_name or "AC_Device"

        if mac then
            local num = add_mac_to_list(mac, list_type, device_name)
            if num then
                write_log("[AC] Added MAC " .. mac .. " to " .. list_type)
                return true, "MAC added to " .. list_type .. " successfully"
            else
                return false, "Failed to add MAC to " .. list_type
            end
        else
            return false, "MAC address is required"
        end
    elseif payload.action == "delete" then
        -- 删除MAC
        if payload.nums and #payload.nums > 0 then
            delete_mac_from_list(payload.nums)
            write_log("[AC] Deleted MAC entries: " .. cjson.encode(payload.nums))
            return true, "MAC entries deleted successfully"
        else
            return false, "No MAC entries specified for deletion"
        end
    elseif payload.action == "switch_mode" then
        -- 切换过滤模式
        local mode = payload.mode or "pending"  -- blacklist, whitelist, pending
        switch_filter_mode(mode)
        write_log("[AC] Switched filter mode to: " .. mode)
        return true, "Filter mode switched to " .. mode
    else
        return false, "Unknown action: " .. tostring(payload.action)
    end
end

function M.get_config_for_ac()
    local config = {
        filter_mode = get_filter_mode(),
        blacklist = get_macs_in_list("blacklist"),
        whitelist = get_macs_in_list("whitelist"),
        pending = get_macs_in_list("pending")
    }
    write_log("[AC] get_config_for_ac: retrieved MAC access control config")
    return config
end

-- 仅在明确作为 CGI 脚本运行时执行
if arg and arg[0] and arg[0]:match("mac_access_control%.lua") and is_cgi() then
    local function run()
        read_request_data()
        if request_data then
            route_api()
        else
            send_error_response("N/A", 1, "No request data received.")
        end
    end
    run()
end

return M