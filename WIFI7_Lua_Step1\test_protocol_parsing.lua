#!/usr/bin/env lua

-- 测试协议解析
local ac_protocol = require("ac_protocol")

-- 网络助手发送的查询数据
local hex_data = "01 00 00 00 A5 00 41 1F 00 00 00 01 00 01 00 00 31 39 00 00 00 00 00 00 31 32 33 34 35 36 37 38 39 30 31 32 33 34 35 36 11 22 33 44 55 66 00 00 00 00 00 00 00 00 00 00 7B 22 71 75 65 72 79 5F 74 79 70 65 22 3A 22 6D 61 63 5F 77 68 69 74 65 6C 69 73 74 22 7D"

-- 转换16进制字符串为二进制数据
local function hex_to_binary(hex_str)
    local result = ""
    for hex_byte in hex_str:gmatch("%x%x") do
        result = result .. string.char(tonumber(hex_byte, 16))
    end
    return result
end

-- 手动解析4字节整数（小端序）
local function read_uint32_le(data, pos)
    local b1, b2, b3, b4 = data:byte(pos, pos + 3)
    return b1 + (b2 * 256) + (b3 * 65536) + (b4 * 16777216)
end

-- 手动解析2字节整数（小端序）
local function read_uint16_le(data, pos)
    local b1, b2 = data:byte(pos, pos + 1)
    return b1 + (b2 * 256)
end

-- 手动解析协议头（按照正确的字节序）
local function manual_parse_header(data)
    if #data < 60 then
        return nil, "Data too short"
    end

    local pos = 1

    -- TLV头 (8字节)
    local tlv_type = read_uint32_le(data, pos); pos = pos + 4
    local tlv_length = read_uint32_le(data, pos); pos = pos + 4

    -- 数据类型 (4字节)
    local data_type = read_uint32_le(data, pos); pos = pos + 4

    -- 设备MAC (13字节)
    local dev_mac = data:sub(pos, pos + 12); pos = pos + 13

    -- 保留字段1 (1字节)
    local reserved_1 = data:sub(pos, pos); pos = pos + 1

    -- 协议版本 (2字节)
    local proto_version = read_uint16_le(data, pos); pos = pos + 2

    -- 哈希码 (17字节)
    local hashcode = data:sub(pos, pos + 16); pos = pos + 17

    -- 保留字段2 (6字节)
    local reserved_2 = data:sub(pos, pos + 5); pos = pos + 6

    -- 其他字段 (每个4字节)
    local dev_type = read_uint32_le(data, pos); pos = pos + 4
    local errcode = read_uint32_le(data, pos); pos = pos + 4
    local super_flag = read_uint32_le(data, pos); pos = pos + 4
    local session = read_uint32_le(data, pos); pos = pos + 4
    local sec_type = read_uint32_le(data, pos); pos = pos + 4
    local data_sum = read_uint32_le(data, pos); pos = pos + 4
    local data_idx = read_uint32_le(data, pos); pos = pos + 4

    -- 载荷数据
    local payload = data:sub(pos)

    return {
        tlv_type = tlv_type,
        tlv_length = tlv_length,
        data_type = data_type,
        dev_mac = dev_mac,
        reserved_1 = reserved_1,
        proto_version = proto_version,
        hashcode = hashcode,
        reserved_2 = reserved_2,
        dev_type = dev_type,
        errcode = errcode,
        super_flag = super_flag,
        session = session,
        sec_type = sec_type,
        data_sum = data_sum,
        data_idx = data_idx
    }, payload
end

-- 生成正确的查询数据
local function generate_correct_query_data()
    local payload = '{"query_type":"mac_whitelist"}'
    local total_length = 60 + #payload  -- 头部60字节 + 载荷

    local data = ""
    -- TLV头
    data = data .. string.char(0x01, 0x00, 0x00, 0x00)  -- tlv_type = 1
    data = data .. string.char(total_length & 0xFF, (total_length >> 8) & 0xFF, (total_length >> 16) & 0xFF, (total_length >> 24) & 0xFF)  -- tlv_length

    -- 数据类型 8002 (查询)
    data = data .. string.char(0x42, 0x1F, 0x00, 0x00)  -- data_type = 8002

    -- 设备MAC (13字节)
    data = data .. "112233445566\0"

    -- 保留字段1 (1字节)
    data = data .. "\0"

    -- 协议版本 (2字节)
    data = data .. string.char(0x01, 0x00)  -- version = 1

    -- 哈希码 (17字节)
    data = data .. "1234567890123456\0"

    -- 保留字段2 (6字节)
    data = data .. "000000"

    -- 其他字段 (每个4字节，共28字节)
    data = data .. string.char(0x00, 0x00, 0x00, 0x00)  -- dev_type
    data = data .. string.char(0x00, 0x00, 0x00, 0x00)  -- errcode
    data = data .. string.char(0x00, 0x00, 0x00, 0x00)  -- super_flag
    data = data .. string.char(0x39, 0x30, 0x00, 0x00)  -- session = 12345
    data = data .. string.char(0x00, 0x00, 0x00, 0x00)  -- sec_type
    data = data .. string.char(0x01, 0x00, 0x00, 0x00)  -- data_sum = 1
    data = data .. string.char(0x00, 0x00, 0x00, 0x00)  -- data_idx = 0

    -- 载荷
    data = data .. payload

    return data
end

print("=== 协议解析测试 ===")

-- 转换数据
local binary_data = hex_to_binary(hex_data:gsub("%s+", ""))
print("Binary data length:", #binary_data)

-- 生成正确的查询数据
local correct_query_data = generate_correct_query_data()
print("Correct query data length:", #correct_query_data)

-- 使用C库解析
print("\n=== C库解析结果 ===")
local header_c, payload_c = ac_protocol.unpack_udp_data(binary_data)
if header_c then
    print("C库 data_type:", header_c.data_type)
    print("C库 payload:", payload_c)
    print("C库 payload length:", #payload_c)
else
    print("C库解析失败:", payload_c)
end

-- 使用手动解析
print("\n=== 手动解析结果 ===")
local header_manual, payload_manual = manual_parse_header(binary_data)
if header_manual then
    print("手动 data_type:", header_manual.data_type)
    print("手动 payload:", payload_manual)
    print("手动 payload length:", #payload_manual)
    
    -- 详细字段对比
    print("\n=== 详细字段对比 ===")
    print("tlv_type: C=" .. (header_c.tlv_type or "nil") .. ", Manual=" .. header_manual.tlv_type)
    print("data_type: C=" .. (header_c.data_type or "nil") .. ", Manual=" .. header_manual.data_type)
    print("proto_version: C=" .. (header_c.proto_version or "nil") .. ", Manual=" .. header_manual.proto_version)
else
    print("手动解析失败:", payload_manual)
end

-- 测试正确的查询数据
print("\n=== 正确查询数据测试 ===")
local header_correct, payload_correct = ac_protocol.unpack_udp_data(correct_query_data)
if header_correct then
    print("正确数据 data_type:", header_correct.data_type)
    print("正确数据 payload:", payload_correct)
    print("正确数据 payload length:", #payload_correct)
else
    print("正确数据解析失败:", payload_correct)
end

-- 输出正确查询数据的16进制格式
print("\n=== 正确查询数据16进制格式 ===")
local hex_output = ""
for i = 1, #correct_query_data do
    hex_output = hex_output .. string.format("%02X ", string.byte(correct_query_data, i))
    if i % 16 == 0 then
        hex_output = hex_output .. "\n"
    end
end
print(hex_output)
