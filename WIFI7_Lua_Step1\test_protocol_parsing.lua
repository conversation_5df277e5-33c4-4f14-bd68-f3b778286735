#!/usr/bin/env lua

-- 测试协议解析
local ac_protocol = require("ac_protocol")

-- 网络助手发送的查询数据
local hex_data = "01 00 00 00 A5 00 41 1F 00 00 00 01 00 01 00 00 31 39 00 00 00 00 00 00 31 32 33 34 35 36 37 38 39 30 31 32 33 34 35 36 11 22 33 44 55 66 00 00 00 00 00 00 00 00 00 00 7B 22 71 75 65 72 79 5F 74 79 70 65 22 3A 22 6D 61 63 5F 77 68 69 74 65 6C 69 73 74 22 7D"

-- 转换16进制字符串为二进制数据
local function hex_to_binary(hex_str)
    local result = ""
    for hex_byte in hex_str:gmatch("%x%x") do
        result = result .. string.char(tonumber(hex_byte, 16))
    end
    return result
end

-- 手动解析协议头（按照正确的字节序）
local function manual_parse_header(data)
    if #data < 60 then
        return nil, "Data too short"
    end
    
    local pos = 1
    
    -- TLV头 (8字节)
    local tlv_type = string.unpack("<I4", data, pos); pos = pos + 4
    local tlv_length = string.unpack("<I4", data, pos); pos = pos + 4
    
    -- 数据类型 (4字节)
    local data_type = string.unpack("<I4", data, pos); pos = pos + 4
    
    -- 设备MAC (13字节)
    local dev_mac = data:sub(pos, pos + 12); pos = pos + 13
    
    -- 保留字段1 (1字节)
    local reserved_1 = data:sub(pos, pos); pos = pos + 1
    
    -- 协议版本 (2字节)
    local proto_version = string.unpack("<I2", data, pos); pos = pos + 2
    
    -- 哈希码 (17字节)
    local hashcode = data:sub(pos, pos + 16); pos = pos + 17
    
    -- 保留字段2 (6字节)
    local reserved_2 = data:sub(pos, pos + 5); pos = pos + 6
    
    -- 其他字段 (每个4字节)
    local dev_type = string.unpack("<I4", data, pos); pos = pos + 4
    local errcode = string.unpack("<I4", data, pos); pos = pos + 4
    local super_flag = string.unpack("<I4", data, pos); pos = pos + 4
    local session = string.unpack("<I4", data, pos); pos = pos + 4
    local sec_type = string.unpack("<I4", data, pos); pos = pos + 4
    local data_sum = string.unpack("<I4", data, pos); pos = pos + 4
    local data_idx = string.unpack("<I4", data, pos); pos = pos + 4
    
    -- 载荷数据
    local payload = data:sub(pos)
    
    return {
        tlv_type = tlv_type,
        tlv_length = tlv_length,
        data_type = data_type,
        dev_mac = dev_mac,
        reserved_1 = reserved_1,
        proto_version = proto_version,
        hashcode = hashcode,
        reserved_2 = reserved_2,
        dev_type = dev_type,
        errcode = errcode,
        super_flag = super_flag,
        session = session,
        sec_type = sec_type,
        data_sum = data_sum,
        data_idx = data_idx
    }, payload
end

print("=== 协议解析测试 ===")

-- 转换数据
local binary_data = hex_to_binary(hex_data:gsub("%s+", ""))
print("Binary data length:", #binary_data)

-- 使用C库解析
print("\n=== C库解析结果 ===")
local header_c, payload_c = ac_protocol.unpack_udp_data(binary_data)
if header_c then
    print("C库 data_type:", header_c.data_type)
    print("C库 payload:", payload_c)
    print("C库 payload length:", #payload_c)
else
    print("C库解析失败:", payload_c)
end

-- 使用手动解析
print("\n=== 手动解析结果 ===")
local header_manual, payload_manual = manual_parse_header(binary_data)
if header_manual then
    print("手动 data_type:", header_manual.data_type)
    print("手动 payload:", payload_manual)
    print("手动 payload length:", #payload_manual)
    
    -- 详细字段对比
    print("\n=== 详细字段对比 ===")
    print("tlv_type: C=" .. (header_c.tlv_type or "nil") .. ", Manual=" .. header_manual.tlv_type)
    print("data_type: C=" .. (header_c.data_type or "nil") .. ", Manual=" .. header_manual.data_type)
    print("proto_version: C=" .. (header_c.proto_version or "nil") .. ", Manual=" .. header_manual.proto_version)
else
    print("手动解析失败:", payload_manual)
end
