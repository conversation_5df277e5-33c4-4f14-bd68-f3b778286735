#!/usr/bin/lua

-- AC命令处理器 - 修复版本
-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

local ac_log = require("ac_log")
local ac_protocol = require("ac_protocol")
local ac_comm_wrap = require("ac_comm_wrap")
local cjson = require("cjson.safe")

-- 安全加载模块
local function safe_require(module_name)
    local ok, module = pcall(require, module_name)
    if ok then
        ac_log.info("Successfully loaded module: " .. module_name)
        return module
    else
        ac_log.warn("Failed to load module " .. module_name .. ": " .. tostring(module))
        return nil
    end
end

-- 加载功能模块（使用修复版本）
local ap_mode = safe_require("ap_mode")
local route_mode = safe_require("route_mode") 
local bridge_mode = safe_require("bridge_mode_fixed")  -- 使用修复版本
local vlan = safe_require("vlan")
local user_setup = safe_require("user_setup")
local snmp = safe_require("snmp")
local qos = safe_require("qos")
local scheduled_reboot = safe_require("scheduled_reboot")
local current_device = safe_require("current_device")
local mac_access_control = safe_require("mac_access_control")

-- AC命令注册表
local command_registry = {
    -- 模式管理
    ["ap_mode_config"] = ap_mode,
    ["route_mode_config"] = route_mode,
    ["bridge_mode_config"] = bridge_mode,
    
    -- 网络功能
    ["vlan_config"] = vlan,
    ["user_setup"] = user_setup,
    ["snmp_config"] = snmp,
    ["qos_config"] = qos,
    ["scheduled_reboot"] = scheduled_reboot,
    
    -- 无线过滤
    ["wireless_filter_whitelist"] = current_device,
    ["wireless_filter_blacklist"] = mac_access_control,
}

-- 处理AC协议包
local function handle_packet(data, ip, port)
    ac_log.info("AC handlers processing packet from " .. tostring(ip) .. ":" .. tostring(port))
    
    -- 解析协议包
    local header, payload = ac_protocol.unpack_udp_data(data)
    if not header then
        ac_log.error("Failed to parse AC protocol packet")
        return false
    end
    
    ac_log.info("Parsed AC command: " .. tostring(header.data_type))
    
    -- 查找对应的处理模块
    local handler_module = command_registry[header.data_type]
    if not handler_module then
        ac_log.warn("No handler found for command: " .. tostring(header.data_type))
        
        -- 发送错误响应
        local error_response = {
            errcode = 404,
            message = "Command not supported: " .. tostring(header.data_type)
        }
        local response_data = ac_protocol.pack_udp_data(header, cjson.encode(error_response))
        if response_data then
            ac_comm_wrap.send(response_data, ip, port)
        end
        return false
    end
    
    -- 调用模块的AC接口
    if handler_module.set_config_from_ac and payload then
        local payload_obj = cjson.decode(payload)
        if payload_obj then
            local ok, result = pcall(handler_module.set_config_from_ac, payload_obj)
            if ok then
                ac_log.info("AC command processed successfully: " .. tostring(header.data_type))
                
                -- 发送成功响应
                local success_response = {
                    errcode = 0,
                    message = result or "Command processed successfully"
                }
                local response_data = ac_protocol.pack_udp_data(header, cjson.encode(success_response))
                if response_data then
                    ac_comm_wrap.send(response_data, ip, port)
                end
                return true
            else
                ac_log.error("Error processing AC command " .. tostring(header.data_type) .. ": " .. tostring(result))
                
                -- 发送错误响应
                local error_response = {
                    errcode = 500,
                    message = "Internal error: " .. tostring(result)
                }
                local response_data = ac_protocol.pack_udp_data(header, cjson.encode(error_response))
                if response_data then
                    ac_comm_wrap.send(response_data, ip, port)
                end
                return false
            end
        else
            ac_log.error("Failed to parse JSON payload for command: " .. tostring(header.data_type))
            return false
        end
    else
        ac_log.warn("Module " .. tostring(header.data_type) .. " does not support AC interface")
        return false
    end
end

-- 导出接口
local M = {}
M.handle_packet = handle_packet
M.command_registry = command_registry

return M
