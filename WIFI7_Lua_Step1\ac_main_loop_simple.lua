#!/usr/bin/lua

-- AC主循环 - 简化版本
-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

package.path = package.path .. ";./?.lua"

local ac_log = require("ac_log")
local ac_config_manager = require("ac_config_manager")
local ac_comm_wrap = require("ac_comm_wrap")
local ac_discovery = require("ac_discovery")

-- 暂时跳过ac_handlers加载，避免CGI模块问题
local ac_handlers = nil
print("Skipping ac_handlers loading to avoid CGI module issues")

ac_log.info("AC main loop starting...")

-- 读取配置
local config = ac_config_manager.read()
if not config or not config.enable or config.enable ~= 1 then
    ac_log.info("AC management is disabled, exiting")
    print("AC management is disabled")
    os.exit(0)
end

ac_log.info("AC management enabled, config: " .. require("cjson").encode(config))
print("AC management enabled")

-- 初始化通信
local ok, err = ac_comm_wrap.init(config)
if not ok then
    ac_log.error("Failed to initialize communication: " .. tostring(err))
    print("Failed to initialize communication:", err)
    os.exit(1)
end

local listen_port = config.local_port or config.ap_port
ac_log.info("AC communication initialized on port " .. listen_port)
print("AC communication initialized on port " .. listen_port)

-- 启动自动发现
local discovery_ok, discovery_err = ac_discovery.start(config)
if discovery_ok then
    ac_log.info("AC discovery started successfully")
    print("AC discovery started")
else
    ac_log.warn("AC discovery start failed: " .. tostring(discovery_err))
    print("AC discovery start failed:", discovery_err)
end

-- 主循环
ac_log.info("AC main loop started, waiting for packets...")
print("AC main loop started, waiting for packets on port " .. listen_port)
print("Press Ctrl+C to stop")

local packet_count = 0

while true do
    local data, ip, port = ac_comm_wrap.receive(1)
    if data then
        packet_count = packet_count + 1
        ac_log.info("Received packet #" .. packet_count .. ": " .. #data .. " bytes from " .. tostring(ip) .. ":" .. tostring(port))
        print("Received packet #" .. packet_count .. " from " .. tostring(ip) .. ":" .. tostring(port) .. " (" .. #data .. " bytes)")
        
        -- 首先检查是否是自动发现包
        local discovery_handled = ac_discovery.handle_packet(data, ip, port)
        if discovery_handled then
            ac_log.info("Packet handled by discovery module")
            print("  -> Handled by discovery module")
        else
            -- 简单处理其他AC管理命令（暂时跳过ac_handlers）
            ac_log.info("Packet not handled by discovery, would need ac_handlers")
            print("  -> Packet not handled by discovery (ac_handlers skipped)")

            -- 简单回复确认包
            local reply = "ACK"
            ac_comm_wrap.send(reply, ip, port)
            print("  -> Sent ACK reply")
        end
    else
        -- 定期发送自动发现包
        if config.ac_discovery_method == "auto" and config.ap_port then
            ac_discovery.maybe_send_discovery_packet(config.ap_port)
        end
    end
end
