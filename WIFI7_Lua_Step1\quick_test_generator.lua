#!/usr/bin/lua

-- 快速测试数据生成器 - 生成简单的UDP测试包

local cjson = require("cjson.safe")

-- 简单的测试数据
local test_data = {
    version = "1.0",
    sid = "test123",
    module = "heartbeat", 
    api = "ping",
    timestamp = os.time(),
    ap_mac = "AA:BB:CC:DD:EE:FF"
}

local json_str = cjson.encode(test_data)
print("JSON数据:")
print(json_str)
print("")

-- 转换为十六进制
local function to_hex(str)
    return (str:gsub('.', function(c)
        return string.format('%02X', string.byte(c))
    end))
end

local hex_data = to_hex(json_str)
print("十六进制数据:")
print(hex_data)
print("")

print("网络助手测试步骤:")
print("1. 打开网络助手")
print("2. 选择UDP协议")
print("3. 目标IP: 设备IP地址")
print("4. 目标端口: 50001")
print("5. 发送数据: " .. hex_data)
print("")

-- 生成多个测试用例
local test_cases = {
    {
        name = "心跳测试",
        data = {
            version = "1.0",
            sid = "heartbeat_test",
            module = "heartbeat",
            api = "ping"
        }
    },
    {
        name = "配置获取测试", 
        data = {
            version = "1.0",
            sid = "config_test",
            module = "config",
            api = "get",
            target_mac = "AA:BB:CC:DD:EE:FF"
        }
    },
    {
        name = "状态查询测试",
        data = {
            version = "1.0", 
            sid = "status_test",
            module = "status",
            api = "get"
        }
    }
}

print("=== 多个测试用例 ===")
for i, case in ipairs(test_cases) do
    local json = cjson.encode(case.data)
    local hex = to_hex(json)
    print(string.format("[%d] %s", i, case.name))
    print("JSON: " .. json)
    print("HEX:  " .. hex)
    print("")
end
