#!/usr/bin/lua


--脚本命名:  脚本文件命名为 network_status.lua。

--get_network_status() 函数:

--读取 UCI 配置: 使用 uci:get_all("network") 获取 /etc/config/network 配置文件中所有 network 配置段的信息。
--遍历接口: 遍历获取到的 network 接口配置段，排除 loopback 接口。
--提取网络信息: 对于每个接口，提取以下信息：
--所属网络 (network_name): 根据 UCI 配置中的接口名称 (.name)，将 lan 接口命名为 "LAN"，wan, wan6, wwan0, wwan1 接口统一命名为 "WAN"，其他接口名称直接使用 UCI 中的名称。 根据实际情况调整这个映射关系。
--获取方式 (connection_type): 根据 UCI 配置中的 proto 选项，判断获取方式。 dhcp 和 dhcpv6 协议类型被识别为 "动态获取"， static 协议类型被识别为 "静态IP"，其他协议类型直接显示 UCI 中的 proto 值。
--MAC 地址 (mac_address): 从 UCI 配置中读取 macaddr 选项。
--IP 地址 (ip_address): 从 UCI 配置中读取 ipaddr 选项。
--子网掩码 (netmask): 从 UCI 配置中读取 netmask 选项。
--默认网关 (gateway): 从 UCI 配置中读取 gateway 选项。
--首选 DNS 服务器 (dns_primary) 和 备用 DNS 服务器 (dns_secondary): 从 UCI 配置中读取 dns 选项。 脚本会处理 dns 选项为字符串（逗号分隔的 DNS 地址）或数组的情况，提取首选和备用 DNS 服务器地址。 如果 UCI 中 dns 选项没有配置，则 DNS 服务器信息默认为 "unknown"。
--构建结果: 将每个接口的网络信息存储在一个 table (network_info) 中，然后将这些 table 添加到一个数组 result.result 中。
--route_api() 函数:  路由 API 请求，目前只实现了 "get" API，用于调用 get_network_status() 函数并返回网络状态信息。

--错误处理和日志:  添加了基本的错误处理和日志记录，例如，当无法从 UCI 获取网络接口信息时，会记录错误日志并返回错误 JSON 响应。



local cjson = require("cjson.safe")
local sys = require("luci.sys")
local uci = require("luci.model.uci").cursor()
local io = require("io")
local os = require("os")

local log_file = "/tmp/network_status.log" -- 日志文件路径

-- 日志写入函数 (与之前的版本相同)
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end



-- 读取 HTTP POST 请求数据 (DEBUG v35)
-- 读取 HTTP POST 请求数据 (DEBUG v36 - 移除行首多余符号)
local function read_request_data()
    local request_method = sys.getenv("REQUEST_METHOD")
    local request_length_str = sys.getenv("CONTENT_LENGTH")
    local request_length = tonumber(request_length_str)

    write_log("read_request_data - REQUEST_METHOD: " .. (request_method or "nil"))
    write_log("read_request_data - CONTENT_LENGTH: " .. (request_length_str or "nil"))

    if request_method == "POST" then
        if request_length and request_length > 0 then
            local request_body = io.read("*n", request_length)
            if request_body then
                write_log("read_request_data - Request Body (*n format): " .. request_body)
                local decoded_json = cjson.decode(request_body)
                if decoded_json then
                    requestData = decoded_json
                    write_log("read_request_data - Decoded JSON Data: " .. cjson.encode(requestData))
                else
                    write_log("read_request_data - Failed to decode JSON from request body.")
                end
            else
                write_log("read_request_data - Failed to read request body (*n format).")

                -- Fallback: Try reading with "*all" to see if anything is read at all
                local request_body_fallback = io.read("*all")
                if request_body_fallback then
                    write_log("read_request_data - Fallback Request Body (*all format): " .. request_body_fallback)
                    local decoded_json_fallback = cjson.decode(request_body_fallback)
                    if decoded_json_fallback then
                        requestData = decoded_json_fallback
                        write_log("read_request_data - Decoded JSON Data (fallback): " .. cjson.encode(requestData))
                    else
                        write_log("read_request_data - Failed to decode JSON from fallback request body.")
                    end
                else
                    write_log("read_request_data - Fallback Failed to read request body (*all format) ALSO.")
                end
            end
        else
            write_log("read_request_data - No request body or empty request (CONTENT_LENGTH is not positive).")
        end
    else
        write_log("read_request_data - Request method is not POST, skipping body reading.")
    end
end

-- 获取网络状态信息
local function get_network_status()
    local result = {
        module = "network_status",
        version = "1.0",
        api = "get",
        errcode = 0,
        result = {}
    }

    local interfaces = uci:get_all("network")
    if not interfaces then
        write_log("Error: Failed to get network interfaces from UCI.")
        result.errcode = 5
        result.result = { message = "Failed to get network interfaces" }
        return result
    end

    local device_mode = uci:get("system", "nhx", "mode") or "ap"

    for _, interface in pairs(interfaces) do
        local is_wan_interface = interface[".name"] == "wan"
        local is_lan_interface = interface[".name"] == "lan"
        -- AP模式下只显示lan接口
        if device_mode == "ap" and not is_lan_interface then
            write_log("AP mode: skipping non-lan interface: " .. interface[".name"])
        -- 桥接模式下跳过wan
        elseif device_mode == "bridge" and is_wan_interface then
            write_log("Skipping WAN interface in bridge mode.")
        elseif interface[".type"] == "interface" and (is_lan_interface or is_wan_interface) then
            local ifname = is_lan_interface and "br-lan" or (interface.ifname or interface[".name"])
            local proto = interface.proto or "none"
            write_log("Interface: " .. interface[".name"] .. ", proto: " .. proto)

            local network_info = {
                interface_name = interface[".name"],
                ifname = ifname,
                proto_type = proto,
                mac_address = "-",
                ip_address = "-",
                netmask = "-",
                gateway = "-",
                dns_primary = "-",
                dns_secondary = "-",
                connection_type = proto
            }

            -- 获取 MAC 地址
            if interface.macaddr then
                network_info.mac_address = string.upper(interface.macaddr)
                write_log("MAC address from UCI for " .. interface[".name"] .. ": " .. network_info.mac_address)
            else
                -- 优先使用 ip link 获取 MAC
                local cmd_mac = "ip link show dev " .. ifname .. " 2>/dev/null | grep ether | awk '{print $2}'"
                local handle_mac = io.popen(cmd_mac)
                local mac = handle_mac and handle_mac:read("*a"):gsub("%s+", "") or ""
                if handle_mac then handle_mac:close() end
                if mac and mac:match("^%x+:%x+:%x+:%x+:%x+:%x+$") then
                    network_info.mac_address = string.upper(mac)
                    write_log("MAC address from ip link for " .. ifname .. ": " .. network_info.mac_address)
                else
                    -- 回退到 ifconfig
                    cmd_mac = "ifconfig " .. ifname .. " 2>/dev/null | grep HWaddr | awk '{print $5}'"
                    handle_mac = io.popen(cmd_mac)
                    mac = handle_mac and handle_mac:read("*a"):gsub("%s+", "") or ""
                    if handle_mac then handle_mac:close() end
                    if mac and mac:match("^%x+:%x+:%x+:%x+:%x+:%x+$") then
                        network_info.mac_address = string.upper(mac)
                        write_log("MAC address from ifconfig for " .. ifname .. ": " .. network_info.mac_address)
                    else
                        write_log("Failed to get MAC address for " .. ifname)
                    end
                end
            end

            -- 处理DNS
            if interface.dns then
                local dns_servers = interface.dns
                if type(dns_servers) == "string" then
                    local dns_list = {}
                    for dns in dns_servers:gmatch("([^%s]+)") do
                        table.insert(dns_list, dns)
                    end
                    network_info.dns_primary = dns_list[1] or "-"
                    network_info.dns_secondary = dns_list[2] or "-"
                elseif type(dns_servers) == "table" then
                    network_info.dns_primary = dns_servers[1] or "-"
                    network_info.dns_secondary = dns_servers[2] or "-"
                end
                write_log("DNS for " .. interface[".name"] .. ": primary=" .. network_info.dns_primary .. ", secondary=" .. network_info.dns_secondary)
            elseif interface[".name"] == "wan" then
                -- WAN口未配置时，读取系统实际生效的DNS
                local cmd_dns = "cat /tmp/resolv.conf.d/resolv.conf.auto 2>/dev/null | grep nameserver | awk '{print $2}' | head -n 2"
                local handle_dns = io.popen(cmd_dns)
                local dns_list = {}
                if handle_dns then
                    local dns_lines = handle_dns:read("*a")
                    handle_dns:close()
                    for dns in dns_lines:gmatch("([^\n]+)") do
                        table.insert(dns_list, dns)
                    end
                end
                network_info.dns_primary = dns_list[1] or "-"
                network_info.dns_secondary = dns_list[2] or "-"
                write_log("DNS for wan (Primary DNS): primary=" .. network_info.dns_primary .. ", secondary=" .. network_info.dns_secondary)
            elseif interface[".name"] == "lan" and not is_ap_mode then
                -- 优先读取系统实际生效的DNS
                local cmd_dns = "cat /tmp/resolv.conf.d/resolv.conf.auto 2>/dev/null | grep nameserver | awk '{print $2}' | head -n 2"
                local handle_dns = io.popen(cmd_dns)
                local dns_list = {}
                if handle_dns then
                    local dns_lines = handle_dns:read("*a")
                    handle_dns:close()
                    for dns in dns_lines:gmatch("([^\n]+)") do
                        table.insert(dns_list, dns)
                    end
                end
                if #dns_list == 0 then
                    -- 如果系统DNS没有，再尝试用WAN口UCI配置的DNS
                    local wan_dns = interfaces.wan and interfaces.wan.dns
                    if wan_dns then
                        if type(wan_dns) == "string" then
                            for dns in wan_dns:gmatch("([^%s]+)") do
                                table.insert(dns_list, dns)
                            end
                        elseif type(wan_dns) == "table" then
                            dns_list[1] = wan_dns[1]
                            dns_list[2] = wan_dns[2]
                        end
                    end
                end
                network_info.dns_primary = dns_list[1] or "-"
                network_info.dns_secondary = dns_list[2] or "-"
                write_log("DNS for lan (优先系统DNS): primary=" .. network_info.dns_primary .. ", secondary=" .. network_info.dns_secondary)
            end

            -- 获取IP地址、掩码和网关
            if proto == "static" then
                network_info.ip_address = interface.ipaddr or "-"
                network_info.netmask = interface.netmask or "-"
                network_info.gateway = interface.gateway or "-"
            elseif proto == "pppoe" then
                -- 优先查 pppoe-wan
                local cmd_ipv4 = "ip addr show dev pppoe-wan 2>/dev/null | grep 'inet ' | awk '{print $2}'"
                local handle_ipv4 = io.popen(cmd_ipv4)
                local ip_mask = handle_ipv4:read("*a"):gsub("%s+", "")
                handle_ipv4:close()
                if ip_mask == "" then
                    -- 查 ppp0
                    cmd_ipv4 = "ip addr show dev ppp0 2>/dev/null | grep 'inet ' | awk '{print $2}'"
                    handle_ipv4 = io.popen(cmd_ipv4)
                    ip_mask = handle_ipv4:read("*a"):gsub("%s+", "")
                    handle_ipv4:close()
                end
                if ip_mask == "" then
                    -- 兜底查物理口
                    cmd_ipv4 = "ip addr show dev " .. ifname .. " | grep 'inet ' | awk '{print $2}'"
                    handle_ipv4 = io.popen(cmd_ipv4)
                    ip_mask = handle_ipv4:read("*a"):gsub("%s+", "")
                    handle_ipv4:close()
                end
                write_log("IPv4 command output for " .. ifname .. " (pppoe): " .. (ip_mask or "nil"))
                network_info.ip_address = ip_mask:match("(%d+%.%d+%.%d+%.%d+)") or "-"
                local cidr = ip_mask and ip_mask:match("/(%d+)")
                if cidr then
                    local mask = cidr_to_netmask(tonumber(cidr))
                    network_info.netmask = mask or "***************"
                else
                    network_info.netmask = "***************"
                end
                -- 默认网关
                local cmd_gateway = "ip route | grep '^default' | grep pppoe-wan | awk '{print $3}'"
                local handle_gateway = io.popen(cmd_gateway)
                local gateway = handle_gateway:read("*a"):gsub("%s+", "")
                handle_gateway:close()
                if gateway == "" then
                    cmd_gateway = "ip route | grep '^default' | grep ppp0 | awk '{print $3}'"
                    handle_gateway = io.popen(cmd_gateway)
                    gateway = handle_gateway:read("*a"):gsub("%s+", "")
                    handle_gateway:close()
                end
                if gateway == "" then
                    cmd_gateway = "ip route | grep '^default' | grep " .. ifname .. " | awk '{print $3}'"
                    handle_gateway = io.popen(cmd_gateway)
                    gateway = handle_gateway:read("*a"):gsub("%s+", "")
                    handle_gateway:close()
                end
                write_log("Gateway command output for " .. ifname .. " (pppoe): " .. (gateway or "nil"))
                network_info.gateway = gateway:match("(%d+%.%d+%.%d+%.%d+)") or "-"
                -- DNS
                local dns1, dns2 = "-", "-"
                local dns_file = io.open("/tmp/resolv.conf.d/resolv.conf.auto", "r")
                if dns_file then
                    local dns_lines = {}
                    for line in dns_file:lines() do
                        local dns = line:match("nameserver%s+([%d%.]+)")
                        if dns then table.insert(dns_lines, dns) end
                    end
                    dns_file:close()
                    dns1 = dns_lines[1] or "-"
                    dns2 = dns_lines[2] or "-"
                end
                network_info.dns_primary = dns1
                network_info.dns_secondary = dns2
            elseif proto == "dhcp" or proto == "pppoe" then
                local cmd_ipv4 = "ip addr show dev " .. ifname .. " | grep 'inet ' | awk '{print $2}'"
                local handle_ipv4 = io.popen(cmd_ipv4)
                if handle_ipv4 then
                    local ip_mask = handle_ipv4:read("*a"):gsub("%s+", "")
                    handle_ipv4:close()
                    write_log("IPv4 command output for " .. ifname .. ": " .. (ip_mask or "nil"))
                    network_info.ip_address = ip_mask:match("(%d+%.%d+%.%d+%.%d+)") or "-"
                    local cidr = ip_mask:match("/(%d+)")
                    if cidr then
                        local mask = cidr_to_netmask(tonumber(cidr))
                        network_info.netmask = mask or "-"
                    end
                end
                local cmd_gateway = "ip route | grep 'default via' | grep " .. ifname .. " | awk '{print $3}'"
                local handle_gateway = io.popen(cmd_gateway)
                if handle_gateway then
                    local gateway = handle_gateway:read("*a"):gsub("%s+", "")
                    handle_gateway:close()
                    write_log("Gateway command output for " .. ifname .. ": " .. (gateway or "nil"))
                    network_info.gateway = gateway:match("(%d+%.%d+%.%d+%.%d+)") or "-"
                end
            elseif interface[".name"] == "lan" and interface.type == "bridge" then
                local br_ifname = "br-lan"
                local cmd_ipv4 = "ip addr show dev " .. br_ifname .. " | grep 'inet ' | awk '{print $2}'"
                local handle_ipv4 = io.popen(cmd_ipv4)
                if handle_ipv4 then
                    local ip_mask = handle_ipv4:read("*a"):gsub("%s+", "")
                    handle_ipv4:close()
                    write_log("IPv4 command output for " .. br_ifname .. ": " .. (ip_mask or "nil"))
                    network_info.ip_address = ip_mask:match("(%d+%.%d+%.%d+%.%d+)") or interface.ipaddr or "-"
                    local cidr = ip_mask:match("/(%d+)")
                    if cidr then
                        local mask = cidr_to_netmask(tonumber(cidr))
                        network_info.netmask = mask or "-"
                    end
                end
                -- 为LAN尝试获取默认网关（路由模式下）
                if not is_ap_mode then
                    local cmd_gateway = "ip route | grep 'default via' | awk '{print $3}' | head -n 1"
                    local handle_gateway = io.popen(cmd_gateway)
                    if handle_gateway then
                        local gateway = handle_gateway:read("*a"):gsub("%s+", "")
                        handle_gateway:close()
                        write_log("Gateway command output for lan: " .. (gateway or "nil"))
                        network_info.gateway = gateway:match("(%d+%.%d+%.%d+%.%d+)") or "-"
                    end
                end
            end

            -- 设置connection_type
            if proto == "dhcp" or proto == "dhcpv6" then
                network_info.connection_type = "dhcp"
            elseif proto == "static" then
                network_info.connection_type = "static"
            else
                network_info.connection_type = proto
            end
            write_log("Connection type set to: " .. network_info.connection_type)

            table.insert(result.result, network_info)
            write_log("Network info added for interface: " .. interface[".name"] .. ", ifname: " .. ifname)
        else
            write_log("Skipping interface: " .. interface[".name"] .. " as it is not 'lan' or 'wan'.")
        end
    end

    if #result.result == 0 then
        write_log("Warning: No network interface information found (for 'lan' or 'wan').")
        result.errcode = 6
        result.result = { message = "No network interface information found for 'lan' or 'wan'" }
    end

    return result
end

-- 辅助函数：将CIDR转换为子网掩码
function cidr_to_netmask(cidr)
    if not cidr or cidr < 0 or cidr > 32 then return nil end
    local mask = 0
    for i = 1, cidr do
        mask = mask + (1 * 2^(32 - i))
    end
    local a = math.floor(mask / 2^24)
    local b = math.floor((mask % 2^24) / 2^16)
    local c = math.floor((mask % 2^16) / 2^8)
    local d = math.floor(mask % 2^8)
    return string.format("%d.%d.%d.%d", a, b, c, d)
end


-- 路由 API 请求 (目前只有 get)
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 读取POST数据
    local function read_request_data()
        local request_method = sys.getenv("REQUEST_METHOD")
        write_log("read_request_data - REQUEST_METHOD: " .. (request_method or "nil"))
        if request_method == "POST" then
            local request_body = io.read("*all")
            if request_body and request_body ~= "" then
                write_log("read_request_data - Request Body: " .. request_body)
                local decoded_json = cjson.decode(request_body)
                if decoded_json then
                    requestData = decoded_json
                    write_log("read_request_data - Decoded JSON Data: " .. cjson.encode(requestData))
                else
                    write_log("read_request_data - Failed to decode JSON")
                end
            else
                write_log("read_request_data - No request body or empty")
            end
        else
            write_log("read_request_data - Not a POST request")
        end
    end

    read_request_data()
    --write_log("requestData.api" .. requestData.api)
    if requestData.api == "get" then
        write_log("API request: get network status")
        local network_status = get_network_status()
        io.write(cjson.encode(network_status))
    else
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "network_status",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
    end
end

-- 主程序入口 (DEBUG v37 -  添加 route_api() 调用前后 Debug 日志)
-- 检查是否在CGI环境中运行
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- AC 调用接口
local M = {}

function M.set_config_from_ac(payload)
    write_log("[AC] set_config_from_ac called: " .. cjson.encode(payload))
    -- 网络状态是只读的，不支持设置
    write_log("[AC] Network status is read-only")
    return false, "Network status is read-only"
end

function M.get_config_for_ac()
    local config = get_network_status()
    write_log("[AC] get_config_for_ac: retrieved network status")
    return config
end

-- 仅在明确作为 CGI 脚本运行时执行
if arg and arg[0] and arg[0]:match("network_status%.lua") and is_cgi() then
    local function run()
        write_log("Network Status API started")
        read_request_data()  -- 添加 read_request_data() 函数调用

        --write_log("DEBUG: Before calling route_api()") -- NEW DEBUG LOG BEFORE route_api() call

        route_api()          -- 调用路由 API 的函数

        --write_log("DEBUG: After calling route_api()")  -- NEW DEBUG LOG AFTER route_api() call

        write_log("Network Status API finished")
    end
    run()
end

return M