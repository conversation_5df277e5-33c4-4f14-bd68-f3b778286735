# Client Mode API 调用指南

## 概述

client_mode.lua 提供客户端模式配置，支持STA连接、扫描、状态查询等功能。本文档详细说明所有API的JSON格式参数。

## API端点

**设置客户端模式配置:**
```
POST /cgi-bin/client_mode.lua
Content-Type: application/json
```

**获取客户端模式配置:**
```
GET /cgi-bin/client_mode.lua?action=get
```

**扫描SSID:**
```
POST /cgi-bin/client_mode.lua
Content-Type: application/json
```

**获取STA状态:**
```
GET /cgi-bin/client_mode.lua?action=get_status
```

## 1. 设置配置 (POST) JSON格式

### 1.1 基础客户端模式配置

```json
{
    "module": "client_mode",
    "api": "set",
    "sid": "session_id_here",
    "param": {
        "mode": "client",
        "connection": {
            "band": "5G",
            "ssid": "UpstreamAP_SSID",
            "password": "wifi_password",
            "encryption": "psk2"
        },
        "wan": {
            "mode": "dhcp",
            "dns": "******* *******"
        },
        "lan": {
            "ip": "***********",
            "netmask": "*************"
        },
        "wifi": {
            "2g": {
                "enabled": true,
                "ssid": "MyWiFi_2G",
                "password": "my_password",
                "encryption": "psk2",
                "channel": "auto",
                "bandwidth": "40MHz",
                "power": 20,
                "hidden": false
            },
            "5g": {
                "enabled": true,
                "ssid": "MyWiFi_5G",
                "password": "my_password",
                "encryption": "psk2",
                "channel": "auto",
                "bandwidth": "80MHz",
                "power": 23,
                "hidden": false
            }
        }
    }
}
```

### 1.2 静态IP外网配置

```json
{
    "module": "client_mode",
    "api": "set",
    "sid": "session_id_here",
    "param": {
        "mode": "client",
        "connection": {
            "band": "2.4G",
            "ssid": "UpstreamAP_SSID",
            "password": "wifi_password",
            "encryption": "psk2"
        },
        "wan": {
            "mode": "static",
            "ip": "***********00",
            "netmask": "*************",
            "gateway": "***********",
            "dns": "******* *******"
        },
        "lan": {
            "ip": "***********",
            "netmask": "*************"
        },
        "wifi": {
            "2g": {
                "enabled": true,
                "ssid": "MyWiFi_2G",
                "password": "my_password"
            }
        }
    }
}
```

### 1.3 PPPoE外网配置

```json
{
    "module": "client_mode",
    "api": "set",
    "sid": "session_id_here",
    "param": {
        "mode": "client",
        "connection": {
            "band": "5G",
            "ssid": "UpstreamAP_SSID",
            "password": "wifi_password",
            "encryption": "psk2"
        },
        "wan": {
            "mode": "pppoe",
            "username": "pppoe_username",
            "password": "pppoe_password",
            "dns": "******* *******"
        },
        "lan": {
            "ip": "***********",
            "netmask": "*************"
        },
        "wifi": {
            "2g": {
                "enabled": true,
                "ssid": "MyWiFi_2G",
                "password": "my_password"
            },
            "5g": {
                "enabled": true,
                "ssid": "MyWiFi_5G",
                "password": "my_password"
            }
        }
    }
}
```

## 2. 扫描SSID (POST) JSON格式

### 2.1 扫描2.4G频段

```json
{
    "module": "client_mode",
    "api": "scan_ssid",
    "sid": "session_id_here",
    "param": {
        "band": "2.4G"
    }
}
```

### 2.2 扫描5G频段

```json
{
    "module": "client_mode",
    "api": "scan_ssid",
    "sid": "session_id_here",
    "param": {
        "band": "5G"
    }
}
```

### 2.3 扫描6G频段

```json
{
    "module": "client_mode",
    "api": "scan_ssid",
    "sid": "session_id_here",
    "param": {
        "band": "6G"
    }
}
```

### 2.4 指定接口扫描

```json
{
    "module": "client_mode",
    "api": "scan_ssid",
    "sid": "session_id_here",
    "param": {
        "band": "5G",
        "ifname": "ath10"
    }
}
```

## 3. 获取配置 (GET) 响应格式

```json
{
    "module": "client_mode",
    "version": "1.0",
    "api": "get",
    "errcode": 0,
    "sid": "session_id_here",
    "result": {
        "connection": {
            "band": "5G",
            "ssid": "UpstreamAP_SSID",
            "password": "wifi_password",
            "encryption": "psk2"
        },
        "wan": {
            "mode": "dhcp",
            "dns": "******* *******"
        },
        "lan": {
            "ip": "***********",
            "netmask": "*************"
        },
        "wifi": {
            "2g": {
                "enabled": true,
                "ssid": "MyWiFi_2G",
                "password": "my_password",
                "encryption": "psk2",
                "channel": "6",
                "bandwidth": "40MHz",
                "power": 20,
                "hidden": false
            },
            "5g": {
                "enabled": true,
                "ssid": "MyWiFi_5G",
                "password": "my_password",
                "encryption": "psk2",
                "channel": "36",
                "bandwidth": "80MHz",
                "power": 23,
                "hidden": false
            }
        },
        "mode": "client"
    }
}
```

## 4. 获取STA状态 (GET) 响应格式

```json
{
    "module": "client_mode",
    "version": "1.0",
    "api": "get_status",
    "errcode": 0,
    "sid": "session_id_here",
    "result": {
        "connection_status": "connected",
        "connected_ssid": "UpstreamAP_SSID",
        "signal_strength": "-45 dBm",
        "connection_time": "02:30:15",
        "ip_address": "***********50",
        "gateway": "***********",
        "dns_servers": "******* *******",
        "tx_rate": "866 Mbps",
        "rx_rate": "866 Mbps",
        "interface": "ath15"
    }
}
```

## 5. 扫描结果响应格式

```json
{
    "module": "client_mode",
    "version": "1.0",
    "api": "scan_ssid",
    "errcode": 0,
    "sid": "session_id_here",
    "result": {
        "band": "5G",
        "scan_ifname": "ath10",
        "ssids": [
            {
                "address": "aa:bb:cc:dd:ee:ff",
                "ssid": "WiFi_Network_1",
                "signal": "-45",
                "encryption": "WPA2 PSK (CCMP)",
                "channel": "36"
            },
            {
                "address": "11:22:33:44:55:66",
                "ssid": "WiFi_Network_2",
                "signal": "-60",
                "encryption": "WPA3 SAE",
                "channel": "40"
            },
            {
                "address": "77:88:99:aa:bb:cc",
                "ssid": "Open_Network",
                "signal": "-70",
                "encryption": "none",
                "channel": "44"
            }
        ]
    }
}
```

## 6. 参数详细说明

### 6.1 connection (连接设置)
- **band**: "2.4G" | "5G" | "6G" - 连接频段
- **ssid**: 上级AP的SSID
- **password**: 上级AP的密码
- **encryption**: "none" | "wep" | "psk" | "psk2" | "sae"

### 6.2 wan (外网设置)
- **mode**: "dhcp" | "static" | "pppoe"
- **ip**: 静态IP地址 (static模式)
- **netmask**: 子网掩码 (static模式)
- **gateway**: 网关地址 (static模式)
- **username**: PPPoE用户名 (pppoe模式)
- **password**: PPPoE密码 (pppoe模式)
- **dns**: DNS服务器，空格分隔

### 6.3 lan (内网设置)
- **ip**: 内网IP地址
- **netmask**: 内网子网掩码

### 6.4 wifi (无线设置)
每个频段支持以下参数：
- **enabled**: true | false
- **ssid**: 无线网络名称
- **password**: 无线密码
- **encryption**: "none" | "wep" | "psk" | "psk2" | "sae"
- **channel**: "auto" | 具体信道号
- **bandwidth**: "20MHz" | "40MHz" | "80MHz" | "160MHz"
- **power**: 发射功率 (0-30)
- **hidden**: true | false - 是否隐藏SSID

### 6.5 scan_ssid参数
- **band**: "2.4G" | "5G" | "6G" - 扫描频段
- **ifname**: 可选，指定扫描接口

## 7. 错误响应格式

```json
{
    "module": "client_mode",
    "version": "1.0",
    "api": "set",
    "errcode": 5,
    "sid": "session_id_here",
    "result": {
        "message": "Invalid band: 7G"
    }
}
```

## 8. 常见错误码
- **0**: 成功
- **5**: 无效的频段参数
- **12**: 未找到无线接口
- **13**: 扫描结果为空

## 9. 使用示例

### 9.1 设置客户端模式
```bash
curl -X POST http://***********/cgi-bin/client_mode.lua \
  -H "Content-Type: application/json" \
  -d '{
    "module": "client_mode",
    "api": "set",
    "param": {
      "connection": {
        "band": "5G",
        "ssid": "MainRouter",
        "password": "password123"
      }
    }
  }'
```

### 9.2 扫描5G频段SSID
```bash
curl -X POST http://***********/cgi-bin/client_mode.lua \
  -H "Content-Type: application/json" \
  -d '{
    "module": "client_mode",
    "api": "scan_ssid",
    "param": {
      "band": "5G"
    }
  }'
```

### 9.3 获取当前配置
```bash
curl "http://***********/cgi-bin/client_mode.lua?action=get"
```

### 9.4 获取STA状态
```bash
curl "http://***********/cgi-bin/client_mode.lua?action=get_status"
```

## 10. 特殊功能说明

### 10.1 自动频段切换扫描
系统会自动临时启用需要扫描的频段接口：
- 扫描2.4G时，临时启用radio0和sta0
- 扫描5G时，临时启用radio1和sta1  
- 扫描6G时，临时启用radio2和sta3
- 扫描完成后恢复原始状态

### 10.2 接口映射关系
- **2.4G**: radio0 -> sta0 -> ath0/ath5
- **5G**: radio1 -> sta1 -> ath10/ath15
- **6G**: radio2 -> sta3 -> ath30/ath35
