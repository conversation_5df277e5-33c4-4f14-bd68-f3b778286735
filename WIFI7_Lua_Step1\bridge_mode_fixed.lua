#!/usr/bin/lua

-- Bridge Mode Settings - Fixed CGI version
-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

local uci = require("luci.model.uci").cursor()
local sys = require("luci.sys")
local cjson = require("cjson.safe")

-- 日志函数
local function write_log(message)
    local log_file = "/tmp/bridge_mode.log"
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] ") .. message .. "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end

-- 检查是否在CGI环境中运行
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- 路由 API 请求
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 获取 POST 数据长度
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""

    -- 读取 POST 数据
    if POSTLength > 0 then
        POST = io.read(POSTLength)
        write_log("Received POST data: " .. (POST or "nil"))
    else
        write_log("No POST data received or CONTENT_LENGTH is 0")
    end

    -- 确保读取成功
    if not POST or POST == "" then
        local error_message = "Failed to retrieve POST data"
        write_log(error_message)
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            errcode = 1,
            result = { message = error_message }
        }))
        return
    end

    -- 解析 POST 数据为 JSON
    local requestData = cjson.decode(POST)
    if not requestData then
        local error_message = "Invalid JSON input"
        write_log(error_message)
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            errcode = 2,
            result = { message = error_message }
        }))
        return
    end

    write_log("Parsed request data: " .. cjson.encode(requestData))

    -- 检查请求格式
    if not requestData.version or not requestData.sid or not requestData.module or not requestData.api then
        local error_message = "Invalid request format"
        write_log(error_message)
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            errcode = 3,
            result = { message = error_message }
        }))
        return
    end

    if requestData.api == "set" then
        write_log("Bridge mode set API called")
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            api = "set",
            errcode = 0,
            sid = requestData.sid,
            result = { message = "Bridge mode configuration updated (simplified)" }
        }))
    elseif requestData.api == "get" then
        write_log("Bridge mode get API called")
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            api = "get",
            errcode = 0,
            sid = requestData.sid,
            result = {
                mode = "bridge",
                connection_type = "wds",
                connection = { band = "2.4G", ssid = "", encryption = "none" },
                lan = { ip_allocation = "dhcp", ip = "*************", netmask = "*************" },
                wifi = {}
            }
        }))
    else
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
    end
end

-- AC 调用接口
local M = {}

function M.set_config_from_ac(payload)
    write_log("[AC] set_config_from_ac called: " .. cjson.encode(payload))
    -- 简化的AC配置处理
    return true, "Bridge mode configuration applied by AC (simplified)"
end

function M.get_config_for_ac()
    local config = {
        mode = "bridge",
        connection = { band = "2.4G", ssid = "", encryption = "none" },
        lan = { ip_allocation = "dhcp", ip = "*************", netmask = "*************" },
        wifi = {}
    }
    write_log("[AC] get_config_for_ac: retrieved Bridge mode config")
    return config
end

-- 仅在明确作为 CGI 脚本运行时执行
if arg and arg[0] and arg[0]:match("bridge_mode") and is_cgi() then
    local function run()
        write_log("Bridge Mode Settings API started")
        route_api()
        write_log("Bridge Mode Settings API finished")
    end
    run()
end

return M
