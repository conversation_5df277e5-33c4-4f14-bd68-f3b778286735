# Bridge Mode API 调用指南

## 概述

bridge_mode.lua 支持三种连接模式：WDS桥接、万能桥接和无线NAT。本文档详细说明所有API的JSON格式参数。

## API端点

**设置桥接模式配置:**
```
POST /cgi-bin/bridge_mode.lua
Content-Type: application/json
```

**获取桥接模式配置:**
```
GET /cgi-bin/bridge_mode.lua?action=get
```

## 1. 设置配置 (POST) JSON格式

### 1.1 WDS桥接模式

```json
{
    "module": "bridge_mode",
    "api": "set",
    "sid": "session_id_here",
    "param": {
        "mode": "bridge",
        "connection_type": "wds",
        "connection": {
            "band": "2.4G",
            "ssid": "UpstreamAP_SSID",
            "password": "wifi_password",
            "encryption": "psk2",
            "mode": "auto",
            "peer_mac": "aa:bb:cc:dd:ee:ff",
            "rssi_threshold": "-70"
        },
        "lan": {
            "ip_allocation": "static",
            "ip": "*************",
            "netmask": "*************",
            "gateway": "***********",
            "dns": "******* *******"
        },
        "wifi": {
            "2g": {
                "enabled": true,
                "ssid": "MyWiFi_2G",
                "password": "my_password",
                "encryption": "psk2",
                "channel": "auto",
                "bandwidth": "40MHz",
                "power": 20,
                "hidden": false
            },
            "5g": {
                "enabled": true,
                "ssid": "MyWiFi_5G",
                "password": "my_password",
                "encryption": "psk2",
                "channel": "auto",
                "bandwidth": "80MHz",
                "power": 23,
                "hidden": false
            },
            "6g": {
                "enabled": false,
                "ssid": "MyWiFi_6G",
                "password": "my_password",
                "encryption": "sae",
                "channel": "auto",
                "bandwidth": "160MHz",
                "power": 23,
                "hidden": false
            }
        }
    }
}
```

### 1.2 万能桥接模式

```json
{
    "module": "bridge_mode",
    "api": "set",
    "sid": "session_id_here",
    "param": {
        "mode": "bridge",
        "connection_type": "universal",
        "connection": {
            "band": "5G",
            "ssid": "UpstreamAP_SSID",
            "password": "wifi_password",
            "encryption": "psk2",
            "mode": "auto",
            "rssi_threshold": "-65"
        },
        "lan": {
            "ip_allocation": "dhcp"
        },
        "wifi": {
            "2g": {
                "enabled": true,
                "ssid": "MyWiFi_2G",
                "password": "my_password",
                "encryption": "psk2",
                "channel": "auto",
                "bandwidth": "40MHz",
                "power": 20,
                "hidden": false
            },
            "5g": {
                "enabled": true,
                "ssid": "MyWiFi_5G",
                "password": "my_password",
                "encryption": "psk2",
                "channel": "auto",
                "bandwidth": "80MHz",
                "power": 23,
                "hidden": false
            }
        }
    }
}
```

### 1.3 无线NAT模式

```json
{
    "module": "bridge_mode",
    "api": "set",
    "sid": "session_id_here",
    "param": {
        "mode": "bridge",
        "connection_type": "nat",
        "connection": {
            "band": "5G",
            "ssid": "UpstreamAP_SSID",
            "password": "wifi_password",
            "encryption": "psk2",
            "mode": "auto",
            "rssi_threshold": "-60"
        },
        "wan": {
            "mode": "dhcp",
            "dns": "******* *******"
        },
        "lan": {
            "ip_allocation": "static",
            "ip": "***********",
            "netmask": "*************"
        },
        "wifi": {
            "2g": {
                "enabled": true,
                "ssid": "MyWiFi_2G",
                "password": "my_password",
                "encryption": "psk2",
                "channel": "auto",
                "bandwidth": "40MHz",
                "power": 20,
                "hidden": false
            },
            "5g": {
                "enabled": true,
                "ssid": "MyWiFi_5G",
                "password": "my_password",
                "encryption": "psk2",
                "channel": "auto",
                "bandwidth": "80MHz",
                "power": 23,
                "hidden": false
            }
        }
    }
}
```

### 1.4 无线NAT模式 - 静态IP外网

```json
{
    "module": "bridge_mode",
    "api": "set",
    "sid": "session_id_here",
    "param": {
        "mode": "bridge",
        "connection_type": "nat",
        "connection": {
            "band": "2.4G",
            "ssid": "UpstreamAP_SSID",
            "password": "wifi_password",
            "encryption": "psk2"
        },
        "wan": {
            "mode": "static",
            "ip": "*************",
            "netmask": "*************",
            "gateway": "***********",
            "dns": "******* *******"
        },
        "lan": {
            "ip_allocation": "static",
            "ip": "***********",
            "netmask": "*************"
        },
        "wifi": {
            "2g": {
                "enabled": true,
                "ssid": "MyWiFi_2G",
                "password": "my_password",
                "encryption": "psk2"
            }
        }
    }
}
```

### 1.5 无线NAT模式 - PPPoE外网

```json
{
    "module": "bridge_mode",
    "api": "set",
    "sid": "session_id_here",
    "param": {
        "mode": "bridge",
        "connection_type": "nat",
        "connection": {
            "band": "5G",
            "ssid": "UpstreamAP_SSID",
            "password": "wifi_password",
            "encryption": "psk2"
        },
        "wan": {
            "mode": "pppoe",
            "username": "pppoe_username",
            "password": "pppoe_password",
            "dns": "******* *******"
        },
        "lan": {
            "ip_allocation": "static",
            "ip": "***********",
            "netmask": "*************"
        },
        "wifi": {
            "2g": {
                "enabled": true,
                "ssid": "MyWiFi_2G",
                "password": "my_password"
            },
            "5g": {
                "enabled": true,
                "ssid": "MyWiFi_5G",
                "password": "my_password"
            }
        }
    }
}
```

## 2. 获取配置 (GET) 响应格式

```json
{
    "module": "bridge_mode",
    "version": "1.0",
    "api": "get",
    "errcode": 0,
    "sid": "session_id_here",
    "result": {
        "connection_type": "nat",
        "connection": {
            "band": "5G",
            "ssid": "UpstreamAP_SSID",
            "password": "wifi_password",
            "encryption": "psk2",
            "mode": "auto",
            "rssi_threshold": "-60"
        },
        "wan": {
            "mode": "dhcp",
            "dns": "******* *******"
        },
        "lan": {
            "ip_allocation": "static",
            "ip": "***********",
            "netmask": "*************"
        },
        "wifi": {
            "2g": {
                "enabled": true,
                "ssid": "MyWiFi_2G",
                "password": "my_password",
                "encryption": "psk2",
                "channel": "6",
                "bandwidth": "40MHz",
                "power": 20,
                "hidden": false
            },
            "5g": {
                "enabled": true,
                "ssid": "MyWiFi_5G",
                "password": "my_password",
                "encryption": "psk2",
                "channel": "36",
                "bandwidth": "80MHz",
                "power": 23,
                "hidden": false
            },
            "6g": {
                "enabled": false
            }
        },
        "mode": "bridge"
    }
}
```

## 3. 参数详细说明

### 3.1 connection_type (必需)
- **wds**: WDS桥接模式，支持点对点连接
- **universal**: 万能桥接模式，使用extap技术
- **nat**: 无线NAT模式，STA接口独立于LAN

### 3.2 connection (连接设置)
- **band**: "2.4G" | "5G" | "6G" - 连接频段
- **ssid**: 上级AP的SSID
- **password**: 上级AP的密码
- **encryption**: "none" | "wep" | "psk" | "psk2" | "sae"
- **mode**: "auto" | "p2p" - 连接模式
- **peer_mac**: P2P模式下的对端MAC地址
- **rssi_threshold**: RSSI阈值，如"-70"

### 3.3 wan (外网设置，仅NAT模式)
- **mode**: "dhcp" | "static" | "pppoe"
- **ip**: 静态IP地址 (static模式)
- **netmask**: 子网掩码 (static模式)
- **gateway**: 网关地址 (static模式)
- **username**: PPPoE用户名 (pppoe模式)
- **password**: PPPoE密码 (pppoe模式)
- **dns**: DNS服务器，空格分隔

### 3.4 lan (内网设置)
- **ip_allocation**: "static" | "dhcp"
- **ip**: 内网IP地址
- **netmask**: 内网子网掩码
- **gateway**: 内网网关 (桥接模式)
- **dns**: DNS服务器 (桥接模式)

### 3.5 wifi (无线设置)
每个频段支持以下参数：
- **enabled**: true | false
- **ssid**: 无线网络名称
- **password**: 无线密码
- **encryption**: "none" | "wep" | "psk" | "psk2" | "sae"
- **channel**: "auto" | 具体信道号
- **bandwidth**: "20MHz" | "40MHz" | "80MHz" | "160MHz"
- **power**: 发射功率 (0-30)
- **hidden**: true | false - 是否隐藏SSID

## 4. 错误响应格式

```json
{
    "module": "bridge_mode",
    "version": "1.0",
    "api": "set",
    "errcode": 5,
    "sid": "session_id_here",
    "result": {
        "message": "Invalid band: 7G"
    }
}
```

## 5. 常见错误码
- **0**: 成功
- **5**: 无效的频段参数
- **6**: 无效的连接类型
- **7**: 无效的IP配置
- **8**: 无效的无线配置

## 6. 使用示例

### 6.1 设置WDS桥接模式
```bash
curl -X POST http://***********/cgi-bin/bridge_mode.lua \
  -H "Content-Type: application/json" \
  -d '{
    "module": "bridge_mode",
    "api": "set",
    "param": {
      "connection_type": "wds",
      "connection": {
        "band": "5G",
        "ssid": "MainRouter",
        "password": "password123"
      }
    }
  }'
```

### 6.2 获取当前配置
```bash
curl "http://***********/cgi-bin/bridge_mode.lua?action=get"
```
