#!/usr/bin/lua

-- AC主循环 - 修复版本
-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

package.path = package.path .. ";./?.lua"

local ac_log = require("ac_log")
local ac_config_manager = require("ac_config_manager")
local ac_comm_wrap = require("ac_comm_wrap")
local ac_discovery = require("ac_discovery")

-- 安全加载 ac_handlers_fixed
local ac_handlers = nil
local handlers_ok, handlers_err = pcall(function()
    ac_handlers = require("ac_handlers_fixed")
end)

if not handlers_ok then
    ac_log.error("Failed to load ac_handlers_fixed: " .. tostring(handlers_err))
    print("Error loading ac_handlers_fixed:", handlers_err)
    print("Continuing without ac_handlers...")
    ac_handlers = nil
end

ac_log.info("AC main loop starting...")

-- 读取配置
local config = ac_config_manager.read()
if not config or not config.enable or config.enable ~= 1 then
    ac_log.info("AC management is disabled, exiting")
    print("AC management is disabled")
    os.exit(0)
end

ac_log.info("AC management enabled, config: " .. require("cjson").encode(config))
print("AC management enabled")

-- 初始化通信
local ok, err = ac_comm_wrap.init(config)
if not ok then
    ac_log.error("Failed to initialize communication: " .. tostring(err))
    print("Failed to initialize communication:", err)
    os.exit(1)
end

local listen_port = config.local_port or config.ap_port
ac_log.info("AC communication initialized on port " .. listen_port)
print("AC communication initialized on port " .. listen_port)

-- 启动自动发现
local discovery_ok, discovery_err = ac_discovery.start(config)
if discovery_ok then
    ac_log.info("AC discovery started successfully")
    print("AC discovery started")
else
    ac_log.warn("AC discovery start failed: " .. tostring(discovery_err))
    print("AC discovery start failed:", discovery_err)
end

-- 主循环
ac_log.info("AC main loop started, waiting for packets...")
print("AC main loop started, waiting for packets on port " .. listen_port)
print("Press Ctrl+C to stop")

local packet_count = 0

while true do
    local data, ip, port = ac_comm_wrap.receive(1)
    if data then
        packet_count = packet_count + 1
        ac_log.info("Received packet #" .. packet_count .. ": " .. #data .. " bytes from " .. tostring(ip) .. ":" .. tostring(port))
        print("Received packet #" .. packet_count .. " from " .. tostring(ip) .. ":" .. tostring(port) .. " (" .. #data .. " bytes)")
        
        -- 首先检查是否是自动发现包
        local discovery_handled = ac_discovery.handle_packet(data, ip, port)
        if discovery_handled then
            ac_log.info("Packet handled by discovery module")
            print("  -> Handled by discovery module")
        else
            -- 处理其他AC管理命令
            if ac_handlers then
                local ok, result = pcall(ac_handlers.handle_packet, data, ip, port)
                if not ok then
                    ac_log.error("Error handling packet: " .. tostring(result))
                    print("  -> Error handling packet:", result)
                else
                    ac_log.info("Packet handled successfully by AC handlers")
                    print("  -> Handled successfully by AC handlers")
                end
            else
                ac_log.info("AC handlers not available, sending simple ACK")
                print("  -> AC handlers not available, sending ACK")
                
                -- 简单回复确认包
                local reply = "ACK"
                ac_comm_wrap.send(reply, ip, port)
                print("  -> Sent ACK reply")
            end
        end
    else
        -- 定期发送自动发现包
        if config.ac_discovery_method == "auto" and config.ap_port then
            ac_discovery.maybe_send_discovery_packet(config.ap_port)
        end
    end
end
