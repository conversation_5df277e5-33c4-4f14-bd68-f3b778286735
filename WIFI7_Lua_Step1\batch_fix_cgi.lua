#!/usr/bin/lua

-- 批量修复CGI问题的脚本
local files_to_fix = {
    "status.lua",
    "route_table.lua", 
    "ping_diagnostics.lua",
    "upgrade.lua",
    "wireless_probe.lua",
    "system_management.lua",
    "port_redirection.lua"
}

local function fix_file(filename)
    print("Processing " .. filename .. "...")
    
    local file = io.open(filename, "r")
    if not file then
        print("  ERROR: Cannot open " .. filename)
        return false
    end
    
    local content = file:read("*all")
    file:close()
    
    -- 检查是否有CGI代码在顶层
    if not content:match('io%.write%("Content%-type:') then
        print("  OK: " .. filename .. " has no CGI issues")
        return true
    end
    
    -- 检查是否已经有route_api函数
    if not content:match('function route_api') then
        print("  WARNING: " .. filename .. " has CGI code but no route_api function")
        return false
    end
    
    print("  FIXING: " .. filename)
    
    -- 移除顶层CGI代码 (从Content-type到第一个function之前)
    local fixed_content = content:gsub(
        'io%.write%("Content%-type:[^"]*"[^%c]*%c%c[^f]*',
        ''
    )
    
    -- 如果route_api是local function，改为global function
    fixed_content = fixed_content:gsub('local function route_api%(', 'function route_api(')
    
    -- 写回文件
    local outfile = io.open(filename, "w")
    if not outfile then
        print("  ERROR: Cannot write to " .. filename)
        return false
    end
    
    outfile:write(fixed_content)
    outfile:close()
    
    print("  FIXED: " .. filename)
    return true
end

print("Starting batch CGI fix...")
local success_count = 0
local total_count = 0

for _, filename in ipairs(files_to_fix) do
    total_count = total_count + 1
    if fix_file(filename) then
        success_count = success_count + 1
    end
end

print(string.format("Batch fix complete: %d/%d files processed successfully", success_count, total_count))
