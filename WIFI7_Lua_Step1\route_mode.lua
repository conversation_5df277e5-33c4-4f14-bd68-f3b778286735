#!/usr/bin/lua

-- Copyright (c) 2024 The Linux Foundation. All rights reserved.
-- Not a Contribution.

-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

-- Lua环境：此脚本在OpenWRT的Lua环境中运行，确保有必要的Lua库，如luci.sys, luci.jsonc和luci.uci

-- UCI：这个脚本使用了uci库来直接操作配置文件。这比直接读写文件更为安全和标准。

-- Base64编码：在获取IP地址、子网掩码等信息时，会进行base64编码以满足API格式的要求。

-- 错误处理：脚本中包含了基本的错误处理，可以根据实际需求进行扩展。

-- 外网设置：
  -- set_wan_settings函数处理外网设置的更新，包括PPPoE、静态IP和动态获取等配置。
  -- get_wan_settings函数用于获取当前的外网设置信息。
  -- 使用uci库来更新或读取外网设置配置。
  -- 使用uci:commit来保存配置文件。
  -- 使用sys.exec来重启相关服务（如network）。

-- API调用格式：
--{
--    "version": "1.0",
--    "sid": "5bdd8aec153a8ff183387bfcea40e3aa",
--    "module": "wan_settings",
--    "api": "set",
--    "param": {
--        "mode": "pppoe/static/dhcp",
--        "auth_type": "PAP/CHAP/PAP_CHAP",
--        "username": "username",
--        "password": "password",
--        "server": "server_name",
--        "mtu": "mtu_value",
--        "ip": "ip_address",
--        "netmask": "netmask",
--        "gateway": "***********",
--        "dns1": "*******",
--        "dns2": "***************"
--    }
--}
  
--{
--    "version": "1.0",
--    "sid": "5bdd8aec153a8ff183387bfcea40e3aa",
--    "module": "wan_settings",
--    "api": "set",
--    "param": {
--        "mode": "pppoe"或"static"或"dhcp",
--        "pppoe": {
--            "username": "base64编码的用户名",
--            "password": "base64编码的密码",
--            "type": "base64编码的类型",
--            "server": "base64编码的服务器名称",
--            "mtu": "base64编码的MTU",
--            "dns1": "base64编码的首选DNS服务器",
--            "dns2": "base64编码的备用DNS服务器"
--        },
--        "static": {
--            "ip": "base64编码的IP地址",
--            "netmask": "base64编码的子网掩码",
--            "gateway": "base64编码的默认网关",
--            "dns1": "base64编码的首选DNS服务器",
--            "dns2": "base64编码的备用DNS服务器"
--        },
--        "dhcp": {
--            "dns1": "base64编码的首选DNS服务器",
--            "dns2": "base64编码的备用DNS服务器"
--        }
--    }
--}

--{
--    "version": "1.0",
--    "sid": "session_id",
--    "module": "wan_settings",
--    "api": "get"
--  }
  
-- 这个脚本使用的是OpenWRT的network配置文件。
-- 在set_wan_settings函数中，脚本根据用户提供的mode（pppoe、static或dhcp）来设置相应的WAN配置。
-- 在get_wan_settings函数中，返回的数据需要进行base64编码，以便与API格式保持一致

-- 重启服务：在更新外网设置后，可能需要重启相关服务来使配置生效。

-- 安全性：确保处理用户输入时，进行了必要的验证和清理，以防止注入攻击
-- 性能：由于脚本每次都需要读取和写入配置文件，对于频繁调用的API，可以考虑优化，例如通过缓存减少IO操作。
-- 权限：确保脚本以必要的权限运行，修改网络配置文件和重启网络服务需要root权限。

--清理网桥模式配置：
--删除 LAN 的桥接类型配置
--删除所有非标准的 def_ifname 配置
--删除网桥模式下的 wwan 接口配置（wwan0、wwan1、wwan3）
--清理防火墙配置：
--删除网桥模式下的特殊防火墙规则（wwan_zone、wwan_to_lan、masquerade_rule）
--恢复标准的防火墙配置，包括：
--WAN 区域的输入规则为 REJECT
--WAN 区域的输出规则为 ACCEPT
--WAN 区域的转发规则为 REJECT
--启用 NAT 和 MTU 修复
--恢复 DHCP 服务器配置：
--启用 DHCP 服务器（ignore = 0）
--设置 DHCP 地址池范围（start = 100, limit = 150）
--设置租约时间（leasetime = 12h）
--配置提交：
--在清理和恢复配置后，立即提交网络、防火墙和 DHCP 配置的更改
--这些改进确保了：
--从网桥模式切换到路由模式时，所有网桥模式特有的配置都被正确清理
--从 AP 模式切换到路由模式时，DHCP 服务器配置被正确恢复
--路由模式所需的标准配置都被正确设置
--避免了不同模式之间的配置冲突
--建议在测试时特别关注以下几点：
--从网桥模式切换到路由模式时，确认所有网桥相关的配置都被正确清理
--从 AP 模式切换到路由模式时，确认 DHCP 服务器配置正确恢复
--验证 WAN 接口是否能正常获取 IP 地址
--检查防火墙规则是否正确应用
--确认 LAN 接口的 DHCP 服务是否正常工作


-- 引入模块
local cjson = require("cjson.safe")
local sys = require("luci.sys")
local uci = require("luci.model.uci").cursor()
local log_file = "/tmp/route_mode.log" -- 日志文件路径

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end



-- 验证 IP 地址是否有效
local function is_valid_ip(ip)
    return ip:match("^%d+%.%d+%.%d+%.%d+$") ~= nil -- 检查IP地址格式是否正确
end

-- 验证子网掩码是否有效
local function is_valid_netmask(netmask)
    return netmask:match("^%d+%.%d+%.%d+%.%d+$") ~= nil -- 检查子网掩码格式是否正确
end

-- 验证网关是否有效
local function is_valid_gateway(gateway)
    return gateway == "" or is_valid_ip(gateway)
end

-- 路由到具体逻辑
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 获取 POST 数据长度
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""

    -- 读取 POST 数据
    if POSTLength > 0 then
        POST = io.read(POSTLength)
        write_log("Received POST data: " .. (POST or "nil"))
    else
        write_log("No POST data received or CONTENT_LENGTH is 0")
    end

    -- 确保读取成功
    if not POST or POST == "" then
        local error_message = "Failed to retrieve POST data"
        write_log(error_message)
        io.write(cjson.encode({
            module = "route_mode",
            version = "1.0",
            errcode = 1,
            result = { message = error_message }
        }))
        return
    end

    -- 解析 POST 数据为 JSON
    local requestData = cjson.decode(POST)
    if not requestData then
        local error_message = "Invalid JSON input"
        write_log(error_message)
        io.write(cjson.encode({
            module = "route_mode",
            version = "1.0",
            errcode = 2,
            result = { message = error_message }
        }))
        return
    end

    write_log("Parsed request data: " .. cjson.encode(requestData))

    -- 检查请求格式
    if not requestData.version or not requestData.sid or not requestData.module or not requestData.api then
        local error_message = "Invalid request format"
        write_log(error_message)
        io.write(cjson.encode({
            module = "route_mode",
            version = "1.0",
            errcode = 3,
            result = { message = error_message }
        }))
        return
    end

    if requestData.api == "set" then
        write_log("Calling set_all_settings with data: " .. cjson.encode(requestData))
        set_all_settings(requestData)
    elseif requestData.api == "get" then
        write_log("Calling get_all_settings with data: " .. cjson.encode(requestData))
        get_all_settings(requestData)
    else
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "route_mode",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
    end
end

-- 设置系统配置
function set_system_settings(system_param)
    if system_param.mode then
        uci:set("system", "nhx", "mode", system_param.mode)
    end
    -- 添加其他系统参数的设置
    uci:commit("system")
end

local function table_contains(tbl, value)
    for _, v in ipairs(tbl) do
        if v == value then
            return true
        end
    end
    return false
end

-- 获取当前应用模式的辅助函数
local function get_current_apply_mode()
    local mode_file = "/etc/config_apply_mode_status"
    local f = io.open(mode_file, "r")
    if f then
        local mode = f:read("*l") -- Read the first line
        f:close()
        mode = mode and mode:gsub("^%s*(.-)%s*$", "%1") -- Trim whitespace
        if mode == "immediate" or mode == "deferred" then
            write_log("Read apply_mode from " .. mode_file .. ": " .. mode)
            return mode
        else
            write_log("Invalid content in " .. mode_file .. ": '" .. (mode or "nil") .. "'. Defaulting to 'immediate'.")
            return "immediate"
        end
    else
        write_log("Could not open " .. mode_file .. ". Defaulting to 'immediate'.")
        return "immediate"
    end
end

-- 设置所有配置 (WAN, LAN, WiFi)
function set_all_settings(data)
    local param = data.param or {}
    local current_mode = uci:get("system", "nhx", "mode") or "router"
    write_log("Current mode: " .. current_mode .. ", setting to router")

    -- 检查运行时环境
    write_log("Checking runtime environment")
    local mem_info = sys.exec("free -m")
    write_log("Memory info: " .. mem_info)
    local start_time = os.time()

    -- 设置系统配置
    local system_param = param.system or {}
    set_system_settings(system_param)

    -- 防火墙处理：分为网桥模式切换和其他模式切换两种情况
    if current_mode == "bridge" then
        write_log("Switching from bridge mode, restoring configurations")
        -- 终止 bridge_monitor 进程
        local kill_result = sys.exec("killall bridge_monitor 2>&1")
        if kill_result and kill_result ~= "" then
            write_log("killall bridge_monitor result: " .. kill_result)
        end
        -- 等待 1 秒确保进程终止
        sys.call("sleep 1")
        -- 检查是否仍有 bridge_monitor 进程运行
        local ps_result = sys.exec("ps | grep '[b]ridge_monitor' | grep -v grep")
        if ps_result and ps_result ~= "" then
            write_log("Warning: bridge_monitor process still running after killall: " .. ps_result)
            -- 强制终止
            sys.exec("killall -9 bridge_monitor 2>&1")
            sys.call("sleep 1")
            ps_result = sys.exec("ps | grep '[b]ridge_monitor' | grep -v grep")
            if ps_result and ps_result ~= "" then
                write_log("Error: Failed to terminate bridge_monitor process: " .. ps_result)
            else
                write_log("bridge_monitor process forcefully terminated")
            end
        else
            write_log("bridge_monitor process successfully terminated")
        end
        -- 清理网桥模式残留防火墙配置
        uci:delete("firewall", "wwan_zone")
        uci:delete("firewall", "wwan_to_lan")
        uci:delete("firewall", "masquerade_rule")
        write_log("Removed bridge mode firewall remnants")
        -- 恢复 wan 区域
        local wan_zone_exists = false
        uci:foreach("firewall", "zone", function(s)
            if s.name == "wan" then
                wan_zone_exists = true
                uci:set_list("firewall", s[".name"], "network", {"wan", "wan6", "vpn", "wwan0", "wwan1"})
                uci:set("firewall", s[".name"], "input", "REJECT")
                uci:set("firewall", s[".name"], "output", "ACCEPT")
                uci:set("firewall", s[".name"], "forward", "REJECT")
                uci:set("firewall", s[".name"], "masq", "1")
                uci:set("firewall", s[".name"], "mtu_fix", "1")
            elseif s.name == "lan" then
                -- 确保 lan_zone 配置与现有一致，优先使用 list 格式
                local current_network = uci:get_list("firewall", s[".name"], "network") or {uci:get("firewall", s[".name"], "network") or "lan"}
                if #current_network == 0 or current_network[1] ~= "lan" then
                    uci:set_list("firewall", s[".name"], "network", {"lan"})
                end
                uci:set("firewall", s[".name"], "input", "ACCEPT")
                uci:set("firewall", s[".name"], "output", "ACCEPT")
                uci:set("firewall", s[".name"], "forward", "ACCEPT")
            end
        end)
        if not wan_zone_exists then
            uci:section("firewall", "zone", "wan", {
                name = "wan",
                input = "REJECT",
                output = "ACCEPT",
                forward = "REJECT",
                masq = "1",
                mtu_fix = "1",
                network = {"wan", "wan6", "vpn", "wwan0", "wwan1"}
            })
        end
        write_log("Restored wan zone")

        -- 恢复 lan 区域（如果不存在）
        local lan_zone_exists = false
        uci:foreach("firewall", "zone", function(s)
            if s.name == "lan" then
                lan_zone_exists = true
            end
        end)
        if not lan_zone_exists then
            uci:section("firewall", "zone", "lan_zone", {
                name = "lan",
                input = "ACCEPT",
                output = "ACCEPT",
                forward = "ACCEPT",
                network = {"lan"}
            })
        end
        write_log("Restored lan zone")

        -- 恢复 lan_to_wan 转发
        local lan_to_wan_exists = false
        uci:foreach("firewall", "forwarding", function(s)
            if s.src == "lan" and s.dest == "wan" then
                lan_to_wan_exists = true
            end
        end)
        if not lan_to_wan_exists then
            uci:section("firewall", "forwarding", "lan_to_wan", {
                src = "lan",
                dest = "wan"
            })
        end
        write_log("Restored lan_to_wan forwarding rule")

        -- 恢复原厂防火墙规则
        local rules = {
            {
                name = "Allow-DHCP-Renew",
                src = "wan",
                proto = "udp",
                dest_port = "68",
                target = "ACCEPT",
                family = "ipv4"
            },
            {
                name = "Allow-Ping",
                src = "wan",
                proto = "icmp",
                icmp_type = "echo-request",
                target = "ACCEPT",
                family = "ipv4"
            },
            {
                name = "Allow-IGMP",
                src = "wan",
                proto = "igmp",
                target = "ACCEPT",
                family = "ipv4"
            },
            {
                name = "Allow-DHCPv6",
                src = "wan",
                proto = "udp",
                dest_port = "546",
                target = "ACCEPT",
                family = "ipv6"
            },
            {
                name = "Allow-MLD",
                src = "wan",
                proto = "icmp",
                src_ip = "fe80::/10",
                icmp_type = {"130/0", "131/0", "132/0", "143/0"},
                target = "ACCEPT",
                family = "ipv6"
            },
            {
                name = "Allow-ICMPv6-Input",
                src = "wan",
                proto = "icmp",
                icmp_type = {"echo-request", "echo-reply", "destination-unreachable", "packet-too-big", "time-exceeded", "bad-header", "unknown-header-type", "router-solicitation", "neighbour-solicitation", "router-advertisement", "neighbour-advertisement"},
                limit = "1000/sec",
                target = "ACCEPT",
                family = "ipv6"
            },
            {
                name = "Allow-ICMPv6-Forward",
                src = "wan",
                dest = "*",
                proto = "icmp",
                icmp_type = {"echo-request", "echo-reply", "destination-unreachable", "packet-too-big", "time-exceeded", "bad-header", "unknown-header-type"},
                limit = "1000/sec",
                target = "ACCEPT",
                family = "ipv6"
            },
            {
                name = "Allow-IPSec-ESP",
                src = "wan",
                dest = "lan",
                proto = "esp",
                target = "ACCEPT"
            },
            {
                name = "Allow-ISAKMP",
                src = "wan",
                dest = "lan",
                dest_port = "500",
                proto = "udp",
                target = "ACCEPT"
            }
        }

        for _, rule in ipairs(rules) do
            local rule_exists = false
            uci:foreach("firewall", "rule", function(s)
                if s.name == rule.name then
                    rule_exists = true
                end
            end)
            if not rule_exists then
                local section = uci:section("firewall", "rule", nil, {
                    name = rule.name,
                    src = rule.src,
                    dest = rule.dest,
                    proto = rule.proto,
                    dest_port = rule.dest_port,
                    target = rule.target,
                    family = rule.family,
                    src_ip = rule.src_ip,
                    limit = rule.limit
                })
                if rule.icmp_type then
                    uci:set_list("firewall", section, "icmp_type", rule.icmp_type)
                end
            end
        end
        write_log("Restored factory firewall rules")

        -- 恢复 NSS ECM 脚本
        local qcanssecm_exists = false
        uci:foreach("firewall", "include", function(s)
            if s[".name"] == "qcanssecm" then
                qcanssecm_exists = true
            end
        end)
        if not qcanssecm_exists then
            uci:section("firewall", "include", "qcanssecm", {
                type = "script",
                path = "/etc/firewall.d/qca-nss-ecm"
            })
        end
        write_log("Restored qcanssecm include for NSS ECM")
    else
        -- 非网桥模式切换（路由到路由或 AP 到路由）
        write_log("Switching from non-bridge mode, applying router firewall settings")
        -- 清理可能存在的 WWAN 相关配置
        uci:set("firewall", "wwan_zone", "disabled", "1")
        uci:set("firewall", "wwan_to_lan", "enabled", "0")
        uci:set("firewall", "masquerade_rule", "enabled", "0")
        -- 移除不必要的转发规则（仅保留 lan_to_wan）
        uci:set("firewall", "wan_to_lan", "enabled", "0") -- wan_to_lan 在路由模式下无意义
        write_log("Restored firewall parameters")
        -- 恢复 wan 区域
        local wan_zone_success, wan_zone_err = pcall(uci.foreach, uci, "firewall", "zone", function(s)
            if s.name == "wan" then
                uci:delete("firewall", s[".name"], "network")
                uci:set("firewall", s[".name"], "network", {"wan", "wan6", "vpn", "wwan0", "wwan1"})
            end
        end)
        if not wan_zone_success then
            write_log("Error in WAN zone foreach: " .. (wan_zone_err or "unknown error"))
        end
        uci:set("firewall", "wan", "input", "REJECT")
        uci:set("firewall", "wan", "output", "ACCEPT")
        uci:set("firewall", "wan", "forward", "REJECT")
        uci:set("firewall", "wan", "masq", "1")
        uci:set("firewall", "wan", "mtu_fix", "1")
        uci:set("firewall", "lan", "input", "ACCEPT")
        uci:set("firewall", "lan", "output", "ACCEPT")
        uci:set("firewall", "lan", "forward", "ACCEPT")
        write_log("Restored firewall settings")
        -- 恢复 lan -> wan 转发规则
        local lan_to_wan_exists = false
        local foreach_success, foreach_err = pcall(uci.foreach, uci, "firewall", "forwarding", function(s)
            if s.src == "lan" and s.dest == "wan" then
                lan_to_wan_exists = true
            end
        end)
        if not foreach_success then
            write_log("Error in forwarding foreach: " .. (foreach_err or "unknown error"))
        end
        if not lan_to_wan_exists then
            local section_success, section_err = pcall(uci.section, uci, "firewall", "forwarding", "lan_to_wan", {
                src = "lan",
                dest = "wan"
            })
            if not section_success then
                write_log("Error creating lan_to_wan forwarding rule: " .. (section_err or "unknown error"))
            end
        end
        write_log("Restored lan_to_wan forwarding rule")
    end

    -- 清理其他模式的配置
    write_log("Cleaning up configurations from other modes")
    uci:delete("network", "lan", "type")
    uci:delete("network", "lan", "ip6assign")
    uci:delete("network", "lan", "force_link")
    uci:delete("network", "lan", "def_ifname")
    uci:delete("network", "lan", "stp")
    uci:delete("network", "wan", "def_ifname")
    uci:delete("network", "wan6", "def_ifname")
    uci:delete("network", "wan", "disabled")
    uci:delete("network", "wan6", "disabled")

    -- 禁用 wwan 接口（STA 接口）
    uci:set("network", "wwan0", "disabled", "1")
    uci:set("network", "wwan1", "disabled", "1")
    uci:set("network", "wwan3", "disabled", "1")
    write_log("Disabled wwan interfaces: wwan0, wwan1, wwan3")


    -- 配置 LAN 为桥接（路由模式）
    local lan_ifnames = {"eth1.1"} -- 不包含 eth1.2
    local valid_ap_ifaces = {"wlan0", "wlan10", "wlan30"} -- 仅包含主要 AP 接口
    local foreach_success, foreach_err = pcall(uci.foreach, uci, "wireless", "wifi-iface", function(s)
        if s.ifname and s.disabled ~= "1" and s.mode == "ap" and s.network == "lan" and table_contains(valid_ap_ifaces, s[".name"]) then
            table.insert(lan_ifnames, s.ifname)
        end
    end)
    if not foreach_success then
        write_log("Error in wireless foreach for LAN ifnames: " .. (foreach_err or "unknown error"))
    end
    uci:set("network", "lan", "type", "bridge")
    uci:set("network", "lan", "ifname", table.concat(lan_ifnames, " "))
    uci:set("network", "lan", "proto", "static")
    uci:set("network", "lan", "ipaddr", param.lan.ip or "*************") -- 恢复出厂默认 IP
    uci:set("network", "lan", "netmask", param.lan.netmask or "*************")
    -- 恢复出厂配置
    uci:set("network", "lan", "ip6assign", "60")
    uci:set("network", "lan", "force_link", "1")
    uci:set("network", "lan", "def_ifname", "eth1.1")
    write_log("Configured LAN ifname: " .. table.concat(lan_ifnames, " "))

    -- 移除 br-lan 中的 STA 接口（ath5, ath15, ath35）
    local current_ifnames = uci:get("network", "lan", "ifname") or ""
    local new_ifnames = {}
    local sta_ifnames = {"ath5", "ath15", "ath35"}
    if current_ifnames ~= "" then
        for ifname in current_ifnames:gmatch("%S+") do
            if not table_contains(sta_ifnames, ifname) then
                table.insert(new_ifnames, ifname)
            end
        end
    else
        new_ifnames = lan_ifnames
    end
    local new_ifname_str = table.concat(new_ifnames, " ")
    uci:set("network", "lan", "ifname", new_ifname_str)
    write_log("Updated LAN ifname (removed STA interfaces): " .. new_ifname_str)

    -- 禁用 STA 无线接口并移除 WDS
    foreach_success, foreach_err = pcall(uci.foreach, uci, "wireless", "wifi-iface", function(s)
        if s.mode == "sta" then
            uci:set("wireless", s[".name"], "disabled", "1")
            uci:delete("wireless", s[".name"], "wds")
            write_log("Disabled STA interface: " .. (s.ifname or s[".name"]))
        end
    end)
    if not foreach_success then
        write_log("Error in wireless foreach for disabling STA: " .. (foreach_err or "unknown error"))
    end

    -- 启用主要 AP 接口，禁用其他 AP 接口
    local ap_ifaces = {"wlan0", "wlan10"} -- 暂时不用6G
    for _, iface in ipairs(ap_ifaces) do
        uci:set("wireless", iface, "disabled", "0")
    end
    local other_ap_ifaces = {"wlan1", "wlan2", "wlan3", "wlan4", "wlan11", "wlan12", "wlan13", "wlan14", "wlan31", "wlan32", "wlan33", "wlan34"}
    for _, iface in ipairs(other_ap_ifaces) do
        uci:set("wireless", iface, "disabled", "1")
    end
    -- 启用 son/backhaul 接口
    local son_ifaces = {"son0", "son1", "son2"}
    for _, iface in ipairs(son_ifaces) do
        uci:set("wireless", iface, "disabled", "0")
    end
    write_log("Enabled main AP interfaces and disabled others")

    -- 设置 LAN 参数（强制静态 IP）
    local lan_param = param.lan or {}
    lan_param.ip_allocation = "static" -- 忽略请求中的 'dhcp'
    set_lan_settings(lan_param)

    -- 设置 WiFi
    local wifi_param = param.wifi or {}
    local wifi_device_params = {}
    local wifi_iface_params = {}

    for key, value in pairs(wifi_param) do
        if key:match("^wlan") then
            wifi_iface_params[key] = value
        end
        local device_name = "wifi0"
        if key == "wlan0" then device_name = "wifi0"
        elseif key == "wlan10" then device_name = "wifi1"
        elseif key == "wlan30" then device_name = "wifi2" end
        wifi_device_params[device_name] = wifi_device_params[device_name] or {}
        wifi_device_params[device_name].channel = value.channel
        wifi_device_params[device_name].txpower = value.txpower
        wifi_device_params[device_name].country = value.country
        wifi_device_params[device_name].bandwidth = value.bandwidth
    end

    set_wifi(wifi_device_params, wifi_iface_params)
    write_log("WiFi settings applied successfully")

    -- 配置 WAN
    uci:set("network", "wan", "ifname", "eth1.2")
    uci:set("network", "wan", "proto", param.wan.mode or "dhcp")
    uci:set("network", "wan", "def_ifname", "eth1.2") -- 恢复出厂配置
    uci:set("network", "wan6", "ifname", "eth1.2")
    uci:set("network", "wan6", "proto", "dhcpv6")
    uci:set("network", "wan6", "def_ifname", "eth1.2") -- 恢复出厂配置

    if param.wan.mode == "pppoe" then
        uci:set("network", "wan", "username", param.wan.username or "")
        uci:set("network", "wan", "password", param.wan.password or "")
        uci:set("network", "wan", "auth_type", param.wan.auth_type or "PAP")
        uci:set("network", "wan", "server", param.wan.server or "")
        local dns1 = param.wan.dns1 or ""
        local dns2 = param.wan.dns2 or ""
        if dns1 ~= "" and dns2 ~= "" then
            uci:set("network", "wan", "dns", dns1 .. " " .. dns2)
        elseif dns1 ~= "" then
            uci:set("network", "wan", "dns", dns1)
        elseif dns2 ~= "" then
            uci:set("network", "wan", "dns", dns2)
        else
            uci:delete("network", "wan", "dns")
        end
    elseif param.wan.mode == "static" then
        if not is_valid_ip(param.wan.ip) or not is_valid_netmask(param.wan.netmask) or not is_valid_gateway(param.wan.gateway) then
            local error_message = "Invalid IP, netmask or gateway"
            write_log(error_message)
            io.output(cjson.encode({
                module = "route_mode",
                version = "1.0",
                api = "set",
                errcode = 5,
                result = { message = error_message }
            }))
            return
        end
        uci:set("network", "wan", "ipaddr", param.wan.ip)
        uci:set("network", "wan", "netmask", param.wan.netmask)
        uci:set("network", "wan", "gateway", param.wan.gateway)
        local dns1 = param.wan.dns1 or ""
        local dns2 = param.wan.dns2 or ""
        if dns1 ~= "" and dns2 ~= "" then
            uci:set("network", "wan", "dns", dns1 .. " " .. dns2)
        elseif dns1 ~= "" then
            uci:set("network", "wan", "dns", dns1)
        elseif dns2 ~= "" then
            uci:set("network", "wan", "dns", dns2)
        else
            uci:delete("network", "wan", "dns")
        end
    elseif param.wan.mode == "dhcp" then
        uci:delete("network", "wan", "username")
        uci:delete("network", "wan", "password")
        uci:delete("network", "wan", "auth_type")
        uci:delete("network", "wan", "server")
        local dns1 = param.wan.dns1 or ""
        local dns2 = param.wan.dns2 or ""
        if dns1 ~= "" and dns2 ~= "" then
            uci:set("network", "wan", "dns", dns1 .. " " .. dns2)
        elseif dns1 ~= "" then
            uci:set("network", "wan", "dns", dns1)
        elseif dns2 ~= "" then
            uci:set("network", "wan", "dns", dns2)
        else
            uci:delete("network", "wan", "dns")
        end
    end

    write_log("WAN configuration set: proto=" .. (param.wan.mode or "dhcp") .. ", ifname=eth1.2")

    -- 恢复 VLAN 配置（避免重复添加）
    local vlan_success, vlan_err = pcall(function()
        local vlan1_exists = false
        local vlan2_exists = false
        foreach_success, foreach_err = pcall(uci.foreach, uci, "network", "switch_vlan", function(s)
            if s.vlan == "1" and s.ports == "0t 1 2 4" then
                vlan1_exists = true
            elseif s.vlan == "2" and s.ports == "0t 3" then
                vlan2_exists = true
            end
        end)
        if not foreach_success then
            write_log("Error in VLAN foreach: " .. (foreach_err or "unknown error"))
        end

        if not vlan1_exists then
            uci:set("network", "switch_vlan1", "switch_vlan")
            uci:set("network", "switch_vlan1", "device", "switch1")
            uci:set("network", "switch_vlan1", "vlan", "1")
            uci:set("network", "switch_vlan1", "ports", "0t 1 2 4")
            write_log("Added VLAN 1: ports 0t 1 2 4")
        else
            write_log("VLAN 1 already exists, skipping")
        end

        if not vlan2_exists then
            uci:set("network", "switch_vlan2", "switch_vlan")
            uci:set("network", "switch_vlan2", "device", "switch1")
            uci:set("network", "switch_vlan2", "vlan", "2")
            uci:set("network", "switch_vlan2", "ports", "0t 3")
            write_log("Added VLAN 2: ports 0t 3")
        else
            write_log("VLAN 2 already exists, skipping")
        end
    end)
    if not vlan_success then
        write_log("Error in VLAN configuration: " .. (vlan_err or "unknown error"))
    end
    write_log("VLAN configuration restored: VLAN 1 (ports 0t 1 2 4), VLAN 2 (ports 0t 3)")

     -- 启用 DDNS 服务
    write_log("Enabling DDNS service for router mode")
    sys.exec("/etc/init.d/ddns start")

    -- 自动恢复/重建 wwan0/wwan1/wwan3 接口（路由模式下STA接口归属wwanX，默认禁用）
    local sta_ifaces = {
        {name = "wwan0", ifname = "ath5"},
        {name = "wwan1", ifname = "ath15"},
        {name = "wwan3", ifname = "ath35"}
    }
    for _, sta in ipairs(sta_ifaces) do
        local section_success, section_err = pcall(uci.section, uci, "network", "interface", sta.name, {
            ifname = sta.ifname,
            proto = "dhcp",
            disabled = "1"
        })
        if not section_success then
            write_log("Error creating STA interface " .. sta.name .. ": " .. (section_err or "unknown error"))
        end
    end
    write_log("Restored wwan interfaces")

    -- 提交所有配置
    write_log("Committing configurations")
    local commit_success, commit_err = pcall(function()
        uci:commit("network")
        uci:commit("firewall")
        uci:commit("dhcp")
        uci:commit("wireless")
        uci:commit("system")
    end)
    if not commit_success then
        write_log("Error committing configurations: " .. (commit_err or "unknown error"))
    end

    -- 记录执行时间
    local end_time = os.time()
    write_log("Script execution time: " .. (end_time - start_time) .. " seconds")

    local apply_mode = get_current_apply_mode()
    write_log("Current apply mode: " .. apply_mode)

    if apply_mode == "immediate" then
        -- 立即返回 Web 响应
        write_log("All settings updated successfully, scheduling asynchronous restart")
        io.write(cjson.encode({
            module = "route_mode",
            version = "1.0",
            api = "set",
            errcode = 0,
            sid = data.sid,
            result = { message = "所有配置已更新，服务正在重启..." }
        }))
        io.flush()
        -- 异步执行重启服务
        local restart_cmd = [[
            /bin/sh -c "
                echo 'Starting network service restart sequence' >> /tmp/all_settings_restart.log;
                /etc/init.d/network stop;
                sleep 3;
                sync;
                /etc/init.d/network start;
                sleep 10;
                sync;
                /etc/init.d/dnsmasq restart;
                sleep 3;
                wifi down;
                sleep 3;
                wifi up;
                sleep 5;
                wifi reload;
                echo 'Network restart sequence completed' >> /tmp/set_all_settings.log;
            " &
        ]]
        local restart_ok = sys.call(restart_cmd)
        if restart_ok ~= 0 then
            write_log("Failed to start async service restart")
        else
            write_log("Async service restart initiated")
        end
    else
        write_log("Apply mode is deferred. Services will not be restarted automatically.")
        io.write(cjson.encode({
            module = "route_mode",
            version = "1.0",
            api = "set",
            errcode = 0,
            sid = data.sid,
            result = { message = "所有配置已保存，延迟生效。" }
        }))
        io.flush()
    end
end

-- 获取所有配置 (WAN, LAN, WiFi)
function get_all_settings(data)
    local result = {
        module = "route_mode",
        version = "1.0",
        api = "get",
        errcode = 0,
        sid = data.sid,
        result = {
            wan = get_wan_settings(), -- 使用修改后的get_wan_settings
            lan = get_lan_settings(),
            wifi = get_wifi(),
            mode = "router" -- 固定返回 route
        }
    }

    write_log("All settings retrieved successfully")
    io.write(cjson.encode(result))
end

-- WAN 设置和获取函数
function set_wan_settings(wan_param)
    write_log("Configuring WAN settings: " .. cjson.encode(wan_param))
    local mode = wan_param.mode or "dhcp"

    -- 清理旧 WAN 配置
    uci:delete("network", "wan")
    uci:delete("network", "wan6")

    -- 设置 WAN
    uci:set("network", "wan", "interface")
    uci:set("network", "wan", "proto", mode)
    uci:set("network", "wan", "ifname", "eth1.2")
    --uci:set("network", "wan", "macaddr", "5C:B1:5F:C8:01:68")

    -- 处理 DNS 设置
    local dns1 = wan_param.dns1 or ""
    local dns2 = wan_param.dns2 or ""
    local dns_assignment = ""
    local dns_value = ""

    if dns1 ~= "" and is_valid_ip(dns1) then
        dns_value = dns1
        dns_assignment = "dns1"
    end
    if dns2 ~= "" and is_valid_ip(dns2) then
        if dns_value ~= "" then
            dns_value = dns_value .. " " .. dns2
            dns_assignment = dns_assignment == "dns1" and "both" or "dns2"
        else
            dns_value = dns2
            dns_assignment = "dns2"
        end
    end

    if mode == "pppoe" then
        uci:set("network", "wan", "username", wan_param.username or "")
        uci:set("network", "wan", "password", wan_param.password or "")
        uci:set("network", "wan", "auth_type", wan_param.auth_type or "PAP")
        uci:set("network", "wan", "server", wan_param.server or "")
        if dns_value ~= "" then
            uci:set("network", "wan", "dns", dns_value)
            uci:set("network", "wan", "dns_assignment", dns_assignment)
            write_log("Set PPPoE DNS: " .. dns_value .. ", assignment: " .. dns_assignment)
        else
            uci:delete("network", "wan", "dns")
            uci:delete("network", "wan", "dns_assignment")
            write_log("No DNS set for PPPoE")
        end
    elseif mode == "static" then
        if not is_valid_ip(wan_param.ip) or not is_valid_netmask(wan_param.netmask) or not is_valid_gateway(wan_param.gateway) then
            local error_message = "Invalid IP, netmask, or gateway"
            write_log(error_message)
            io.output(cjson.encode({
                module = "route_mode",
                version = "1.0",
                api = "set",
                errcode = 5,
                result = { message = error_message }
            }))
            return
        end
        uci:set("network", "wan", "ipaddr", wan_param.ip)
        uci:set("network", "wan", "netmask", wan_param.netmask)
        uci:set("network", "wan", "gateway", wan_param.gateway)
        if dns_value ~= "" then
            uci:set("network", "wan", "dns", dns_value)
            uci:set("network", "wan", "dns_assignment", dns_assignment)
            write_log("Set static DNS: " .. dns_value .. ", assignment: " .. dns_assignment)
        else
            uci:delete("network", "wan", "dns")
            uci:delete("network", "wan", "dns_assignment")
            write_log("No DNS set for static")
        end
    elseif mode == "dhcp" then
        uci:delete("network", "wan", "username")
        uci:delete("network", "wan", "password")
        uci:delete("network", "wan", "auth_type")
        uci:delete("network", "wan", "server")
        if dns_value ~= "" then
            uci:set("network", "wan", "dns", dns_value)
            uci:set("network", "wan", "dns_assignment", dns_assignment)
            write_log("Set DHCP DNS: " .. dns_value .. ", assignment: " .. dns_assignment)
        else
            uci:delete("network", "wan", "dns")
            uci:delete("network", "wan", "dns_assignment")
            write_log("No DNS set for DHCP")
        end
    end

    -- 设置 WAN6 (默认开启IPv6)
    uci:set("network", "wan6", "interface")
    uci:set("network", "wan6", "ifname", "eth1.2")
    uci:set("network", "wan6", "proto", "dhcpv6")
    uci:set("network", "wan6", "reqaddress", "try")
    uci:set("network", "wan6", "reqprefix", "auto")
    uci:delete("network", "wan6", "disabled")  -- 确保IPv6默认开启
    write_log("IPv6 enabled by default for WAN6")

    write_log("WAN configuration set: proto=" .. mode .. ", ifname=eth1.2")
    uci:commit("network")
    local network_config = luci.util.exec("cat /etc/config/network")
    write_log("Network config after WAN commit: " .. network_config)
end

-- 获取外网配置
function get_wan_settings()
    local mode = uci:get("network", "wan", "proto") or "none"
    local system_mode = uci:get("system", "nhx", "mode") or "ap"
    local settings = {}

    -- 获取 DNS 配置
    local dns_servers = uci:get("network", "wan", "dns") or ""
    local dns_assignment = uci:get("network", "wan", "dns_assignment") or ""
    local dns1 = ""
    local dns2 = ""

    -- 分割 DNS 服务器
    local dns_list = {}
    for dns in dns_servers:gmatch("%S+") do
        table.insert(dns_list, dns)
    end

    -- 根据 dns_assignment 分配 DNS
    if dns_assignment == "dns1" then
        dns1 = dns_list[1] or ""
    elseif dns_assignment == "dns2" then
        dns2 = dns_list[1] or ""
    elseif dns_assignment == "both" then
        dns1 = dns_list[1] or ""
        dns2 = dns_list[2] or ""
    else
        -- 兼容旧配置：按顺序分配
        dns1 = dns_list[1] or ""
        dns2 = dns_list[2] or ""
    end

    -- 在网桥模式或 proto 为 none 时，默认返回 dhcp
    if system_mode == "bridge" or mode == "none" then
        settings = {
            mode = "dhcp",
            netmask = "*************",
            dns1 = dns1,
            dns2 = dns2
        }
    else
        -- 根据实际 WAN 协议设置配置
        if mode == "pppoe" or mode == "none" then -- 默认 none 为 pppoe
            settings = {
                mode = mode,
                auth_type = uci:get("network", "wan", "auth_type") or "PAP",
                username = uci:get("network", "wan", "username") or "",
                password = uci:get("network", "wan", "password") or "",
                server = uci:get("network", "wan", "server") or "",
                dns1 = dns1,
                dns2 = dns2
            }
        elseif mode == "static" then
            settings = {
                mode = mode,
                ip = uci:get("network", "wan", "ipaddr") or "*************",
                netmask = uci:get("network", "wan", "netmask") or "*************",
                gateway = uci:get("network", "wan", "gateway") or "*************",
                dns1 = dns1,
                dns2 = dns2
            }
        elseif mode == "dhcp" then
            settings = {
                mode = mode,
                netmask = uci:get("network", "wan", "netmask") or "*************",
                dns1 = dns1,
                dns2 = dns2
            }
        end
    end

    write_log("Returning WAN settings: " .. cjson.encode(settings))
    return settings
end
-- 恢复 DHCP 配置
function restore_dhcp_settings()
    write_log("Restoring DHCP settings for router mode")
    uci:set("dhcp", "lan", "ignore", "0") -- 启用 DHCP 服务器
    uci:set("dhcp", "lan", "start", "100")
    uci:set("dhcp", "lan", "limit", "150")
    uci:set("dhcp", "lan", "leasetime", "12h")
    uci:set("dhcp", "lan", "force", "1")
    uci:set("dhcp", "lan", "ra", "server")
    uci:set("dhcp", "lan", "dhcpv4", "server")
    uci:set("dhcp", "lan", "dhcpv6", "server")
    uci:set("dhcp", "lan", "ra_slaac", "1")
    uci:set_list("dhcp", "lan", "ra_flags", {"managed-config", "other-config"})
    uci:set("dhcp", "wan", "ignore", "1")

    -- 清除 dhcp_option，让 dnsmasq 自动分发网关和掩码
    uci:delete("dhcp", "lan", "dhcp_option")
    write_log("Cleared dhcp.lan.dhcp_option to use default gateway and netmask")

end

-- 设置内网配置
function set_lan_settings(data)
    local param = data or {}
    write_log("Setting LAN settings: " .. cjson.encode(param))

    if param.ip_allocation == "static" then
        if not is_valid_ip(param.ip) or not is_valid_netmask(param.netmask) then
            local error_message = "Invalid IP or netmask"
            write_log(error_message)
            io.write(cjson.encode({
                module = "route_mode",
                version = "1.0",
                api = "set",
                errcode = 5,
                result = { message = error_message },
                sid = data.sid
            }))
            return
        end
        write_log("Configuring static IP settings")
        uci:set("network", "lan", "proto", "static")
        uci:set("network", "lan", "ipaddr", param.ip or "*************")
        uci:set("network", "lan", "netmask", param.netmask or "*************")
    else
        write_log("Configuring DHCP settings")
        uci:set("network", "lan", "proto", "dhcp")
        uci:delete("network", "lan", "ipaddr")
        uci:delete("network", "lan", "netmask")
    end

    uci:commit("network")
    restore_dhcp_settings() -- 调用 DHCP 恢复逻辑
end


-- 获取内网配置
function get_lan_settings()
    local result = {}

    local proto = uci:get("network", "lan", "proto") or "static"
    local ip, netmask

    if proto == "static" then
        ip = uci:get("network", "lan", "ipaddr") or "*************"
        netmask = uci:get("network", "lan", "netmask") or "*************"
    elseif proto == "dhcp" then
        ip = "*************"
        netmask = "*************"
    end

    result = {
        ip_allocation = proto,
        ip = ip,  -- 将 ip 提升到同级
        netmask = netmask  -- 将 netmask 提升到同级
    }

    write_log("LAN settings retrieved successfully")
    return result
end

-- 获取系统模式
function get_system_mode()
    return uci:get("system", "nhx", "mode") or "router" -- 从系统配置中获取 mode
end

-- WiFi 设置和获取函数 (改进版)
function set_wifi(wifi_device_params, wifi_iface_params)
    -- 2.4G
    local radio_2_4G = "wifi0"
    local iface_2_4G = "wlan0"
    if wifi_device_params[radio_2_4G] then
        if wifi_device_params[radio_2_4G].channel then
            uci:set("wireless", radio_2_4G, "channel", wifi_device_params[radio_2_4G].channel)
        end
        if wifi_device_params[radio_2_4G].txpower then
            uci:set("wireless", radio_2_4G, "txpower", wifi_device_params[radio_2_4G].txpower)
        end
        if wifi_device_params[radio_2_4G].country then
            uci:set("wireless", radio_2_4G, "country", wifi_device_params[radio_2_4G].country)
        end
        if wifi_device_params[radio_2_4G].bandwidth then
            uci:set("wireless", radio_2_4G, "htmode", wifi_device_params[radio_2_4G].bandwidth) -- 设置带宽
        end
    end
    if wifi_iface_params[iface_2_4G] then
        uci:set("wireless", iface_2_4G, "ssid", wifi_iface_params[iface_2_4G].ssid or "IAP3500-E11-2.4G")
        if wifi_iface_params[iface_2_4G].encryption then
            uci:set("wireless", iface_2_4G, "encryption", wifi_iface_params[iface_2_4G].encryption)
        end
        -- 新增：根据加密方式设置 sae 选项
        if wifi_iface_params[iface_2_4G].encryption == "sae" or wifi_iface_params[iface_2_4G].encryption == "sae-mixed" then
            uci:set("wireless", iface_2_4G, "sae", "1")
        else
            uci:set("wireless", iface_2_4G, "sae", "0")
        end
        if wifi_iface_params[iface_2_4G].key then
            uci:set("wireless", iface_2_4G, "key", wifi_iface_params[iface_2_4G].key)
        end
    end

    -- 5G
    local radio_5G = "wifi1"
    local iface_5G = "wlan10"
    if wifi_device_params[radio_5G] then
        if wifi_device_params[radio_5G].channel then
            uci:set("wireless", radio_5G, "channel", wifi_device_params[radio_5G].channel)
        end
        if wifi_device_params[radio_5G].txpower then
            uci:set("wireless", radio_5G, "txpower", wifi_device_params[radio_5G].txpower)
        end
        if wifi_device_params[radio_5G].country then
            uci:set("wireless", radio_5G, "country", wifi_device_params[radio_5G].country)
        end
        if wifi_device_params[radio_5G].bandwidth then
            uci:set("wireless", radio_5G, "htmode", wifi_device_params[radio_5G].bandwidth) -- 设置带宽
        end
    end
    if wifi_iface_params[iface_5G] then
        uci:set("wireless", iface_5G, "ssid", wifi_iface_params[iface_5G].ssid or "IAP3500-E11-5G")
        if wifi_iface_params[iface_5G].encryption then
            uci:set("wireless", iface_5G, "encryption", wifi_iface_params[iface_5G].encryption)
        end
        -- 新增：根据加密方式设置 sae 选项
        if wifi_iface_params[iface_5G].encryption == "sae" or wifi_iface_params[iface_5G].encryption == "sae-mixed" then
            uci:set("wireless", iface_5G, "sae", "1")
        else
            uci:set("wireless", iface_5G, "sae", "0")
        end
        if wifi_iface_params[iface_5G].key then
            uci:set("wireless", iface_5G, "key", wifi_iface_params[iface_5G].key)
        end
    end

    -- 6G
    local radio_6G = "wifi2"
    local iface_6G = "wlan30"
    if wifi_device_params[radio_6G] then
        if wifi_device_params[radio_6G].channel then
            uci:set("wireless", radio_6G, "channel", wifi_device_params[radio_6G].channel)
        end
        if wifi_device_params[radio_6G].txpower then
            uci:set("wireless", radio_6G, "txpower", wifi_device_params[radio_6G].txpower)
        end
        if wifi_device_params[radio_6G].country then
            uci:set("wireless", radio_6G, "country", wifi_device_params[radio_6G].country)
        end
        if wifi_device_params[radio_6G].bandwidth then
            uci:set("wireless", radio_6G, "htmode", wifi_device_params[radio_6G].bandwidth) -- 设置带宽
        end
    end
    if wifi_iface_params[iface_6G] then
        uci:set("wireless", iface_6G, "ssid", wifi_iface_params[iface_6G].ssid or "IAP3500-E11-6G")
        if wifi_iface_params[iface_6G].encryption then
            uci:set("wireless", iface_6G, "encryption", wifi_iface_params[iface_6G].encryption)
        end
        -- 新增：根据加密方式设置 sae 选项
        if wifi_iface_params[iface_6G].encryption == "sae" or wifi_iface_params[iface_6G].encryption == "sae-mixed" then
            uci:set("wireless", iface_6G, "sae", "1")
        else
            uci:set("wireless", iface_6G, "sae", "0")
        end
        if wifi_iface_params[iface_6G].key then
            uci:set("wireless", iface_6G, "key", wifi_iface_params[iface_6G].key)
        end
    end

    uci:save("wireless")
    uci:commit("wireless")
end

function get_wifi()
    local result = {}

    -- 2.4G
    local iface_2_4G = "wlan0"
    result[iface_2_4G] = {
        ssid = uci:get("wireless", iface_2_4G, "ssid") or "",
        encryption = uci:get("wireless", iface_2_4G, "encryption") or "none",
        key = uci:get("wireless", iface_2_4G, "key") or "",
        channel = uci:get("wireless", "wifi0", "channel") or "auto",
        bandwidth = uci:get("wireless", "wifi0", "htmode") or "auto",
        txpower = uci:get("wireless", "wifi0", "txpower") or "auto",
        country = uci:get("wireless", "wifi0", "country") or "CN"  -- 默认国家代码为中国
    }

    -- 5G
    local iface_5G = "wlan10"
    result[iface_5G] = {
        ssid = uci:get("wireless", iface_5G, "ssid") or "",
        encryption = uci:get("wireless", iface_5G, "encryption") or "none",
        key = uci:get("wireless", iface_5G, "key") or "",
        channel = uci:get("wireless", "wifi1", "channel") or "auto",
        bandwidth = uci:get("wireless", "wifi1", "htmode") or "auto",
        txpower = uci:get("wireless", "wifi1", "txpower") or "auto",
        country = uci:get("wireless", "wifi1", "country") or "CN"  -- 默认国家代码为中国
    }

    -- 6G
    local iface_6G = "wlan30"
    result[iface_6G] = {
        ssid = uci:get("wireless", iface_6G, "ssid") or "",
        encryption = uci:get("wireless", iface_6G, "encryption") or "none",
        key = uci:get("wireless", iface_6G, "key") or "",
        channel = uci:get("wireless", "wifi2", "channel") or "auto",
        bandwidth = uci:get("wireless", "wifi2", "htmode") or "auto",
        txpower = uci:get("wireless", "wifi2", "txpower") or "auto",
        country = uci:get("wireless", "wifi2", "country") or "CN"  -- 默认国家代码为中国
    }

    return result
end
-- 检查是否在CGI环境中运行
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- AC 调用接口
local M = {}

function M.set_config_from_ac(payload)
    write_log("[AC] set_config_from_ac called: " .. cjson.encode(payload))

    -- 设置系统模式为Route
    uci:set("system", "nhx", "mode", "route")

    -- 处理WAN配置
    if payload.wan then
        set_wan_settings(payload.wan)
    end

    -- 处理LAN配置
    if payload.lan then
        set_lan_settings(payload.lan)
    end

    uci:save("system")
    uci:commit("system")
    uci:save("network")
    uci:commit("network")

    write_log("[AC] Route mode configuration applied")
    return true, "Route mode configuration applied by AC"
end

function M.get_config_for_ac()
    local config = {
        mode = "route",
        wan = get_wan_settings(),
        lan = get_lan_settings()
    }
    write_log("[AC] get_config_for_ac: retrieved Route mode config")
    return config
end

-- 仅在明确作为 CGI 脚本运行时执行
if arg and arg[0] and arg[0]:match("route_mode%.lua") and is_cgi() then
    local function run()
        write_log("WAN Settings API started")
        route_api()
        write_log("WAN Settings API finished")
    end
    run()
end

return M
