# WiFi7 Lua 模式文件修改总结

## 修改概述

本次修改涉及4个主要文件，主要目的是：
1. 为bridge_mode.lua增加无线NAT模式支持
2. 修复client_mode.lua的函数调用问题和扫描功能
3. 去掉所有模式文件的延迟重启逻辑，改为立即重启
4. 提供完整的API调用指南

## 1. bridge_mode.lua 修改详情

### 1.1 新增功能
- **无线NAT模式支持**: 参考client_mode.lua实现，支持STA接口连接到wwan网络
- **外网配置功能**: 支持DHCP、静态IP、PPPoE三种外网配置方式
- **防火墙配置**: 为NAT模式单独配置防火墙规则，启用masquerade
- **VLAN分离**: NAT模式下分离LAN和WAN VLAN，其他模式合并VLAN

### 1.2 主要修改
```lua
-- 新增NAT防火墙配置函数
local function configure_nat_firewall()
    -- 配置LAN和WWAN区域，启用NAT转发
end

-- 修改连接类型检测
function get_connection_type()
    -- 增加NAT模式检测逻辑
    if network == "wwan" then
        return "nat"
    end
end

-- 新增外网配置函数
function set_wan_settings(wan_param, sta_ifname)
    -- 支持DHCP、静态IP、PPPoE配置
end
```

### 1.3 去掉延迟重启
- 删除`get_current_apply_mode()`函数
- 修改`set_all_settings()`函数，立即重启所有服务
- 简化重启逻辑，使用并行重启提高效率

## 2. client_mode.lua 修改详情

### 2.1 修复函数调用问题
- 将`local function get_sta_status`改为`function get_sta_status`
- 解决函数作用域问题，确保API调用正常

### 2.2 增强扫描功能
- **自动频段切换**: 扫描时临时启用需要的radio和STA接口
- **状态恢复**: 扫描完成后恢复原始接口状态
- **接口映射**: 明确2.4G/5G/6G与radio0/1/2的对应关系

### 2.3 扫描逻辑改进
```lua
-- 临时启用对应的radio和STA接口
if radio_device and sta_interface then
    -- 记录原始状态
    local original_radio_disabled = uci:get("wireless", radio_device, "disabled")
    local original_sta_disabled = uci:get("wireless", sta_interface, "disabled")
    
    -- 临时启用接口进行扫描
    uci:set("wireless", radio_device, "disabled", "0")
    uci:set("wireless", sta_interface, "disabled", "0")
    
    -- 执行扫描后恢复状态
end
```

## 3. ap_mode.lua 修改详情

### 3.1 去掉延迟重启逻辑
- 删除`get_current_apply_mode()`函数
- 简化重启逻辑，立即重启所有服务
- 使用并行重启命令提高效率

### 3.2 修改内容
```lua
-- 立即重启服务
local restart_cmd = [[
    /etc/init.d/network restart &
    sleep 2
    /etc/init.d/wireless restart &
    sleep 1
    /etc/init.d/firewall restart &
    /etc/init.d/dnsmasq restart &
]]
os.execute(restart_cmd)
```

## 4. route_mode.lua 修改详情

### 4.1 去掉延迟重启逻辑
- 删除`get_current_apply_mode()`函数
- 简化重启逻辑，立即重启所有服务
- 统一重启命令格式

## 5. 新增API文档

### 5.1 BRIDGE_MODE_API_GUIDE.md
- 详细说明bridge_mode.lua的所有API参数
- 包含WDS、万能桥接、无线NAT三种模式的完整配置示例
- 提供错误码说明和使用示例

### 5.2 CLIENT_MODE_API_GUIDE.md
- 详细说明client_mode.lua的所有API参数
- 包含扫描、状态查询、配置设置的完整示例
- 说明自动频段切换扫描功能

## 6. 支持的连接模式

### 6.1 Bridge Mode
1. **WDS桥接** (`connection_type: "wds"`)
   - 支持点对点连接
   - 设置`wds: "1"`和可选的`bssid`
   - STA接口桥接到LAN

2. **万能桥接** (`connection_type: "universal"`)
   - 使用extap技术
   - 设置`extap: "1"`
   - 兼容性更好

3. **无线NAT** (`connection_type: "nat"`)
   - STA接口连接到wwan网络
   - 启用NAT转发和DHCP服务器
   - 支持多种外网配置方式

### 6.2 Client Mode
- 支持STA连接配置
- 支持外网配置（DHCP/静态IP/PPPoE）
- 支持内网配置
- 支持WiFi热点配置

## 7. JSON API 参数格式

### 7.1 Bridge Mode 设置示例
```json
{
    "module": "bridge_mode",
    "api": "set",
    "param": {
        "connection_type": "nat",
        "connection": {
            "band": "5G",
            "ssid": "UpstreamAP",
            "password": "password123",
            "encryption": "psk2"
        },
        "wan": {
            "mode": "dhcp",
            "dns": "******* *******"
        },
        "lan": {
            "ip_allocation": "static",
            "ip": "***********",
            "netmask": "*************"
        },
        "wifi": {
            "2g": {
                "enabled": true,
                "ssid": "MyWiFi_2G",
                "password": "my_password"
            }
        }
    }
}
```

### 7.2 Client Mode 扫描示例
```json
{
    "module": "client_mode",
    "api": "scan_ssid",
    "param": {
        "band": "5G"
    }
}
```

## 8. 技术改进

### 8.1 性能优化
- 并行重启服务，减少等待时间
- 优化扫描逻辑，减少接口切换时间
- 简化配置流程，提高响应速度

### 8.2 功能增强
- 支持跨频段扫描，无需手动切换
- 完善NAT模式配置，支持多种外网方式
- 统一API格式，便于前端调用

### 8.3 稳定性提升
- 修复函数作用域问题
- 完善错误处理机制
- 统一重启逻辑，减少配置冲突

## 9. 使用建议

### 9.1 模式选择
- **WDS桥接**: 适用于点对点连接，性能最佳
- **万能桥接**: 适用于兼容性要求高的场景
- **无线NAT**: 适用于需要独立网段的场景

### 9.2 配置建议
- NAT模式建议使用5G频段连接上级AP
- 扫描功能会临时影响当前连接，建议在配置时使用
- 重启服务需要等待约10-15秒完成

### 9.3 故障排除
- 检查API返回的errcode字段
- 查看系统日志确认配置是否生效
- 确认网络接口状态和IP配置

## 10. 后续开发建议

### 10.1 功能扩展
- 考虑添加6G频段的完整支持
- 增加更多的加密方式支持
- 添加网络质量监控功能

### 10.2 性能优化
- 考虑使用更高效的重启方式
- 优化扫描算法，减少对当前连接的影响
- 增加配置缓存机制

### 10.3 用户体验
- 添加配置验证功能
- 提供更详细的错误信息
- 增加配置导入导出功能

## 11. 文件清单

修改的文件：
- `bridge_mode.lua` - 增加NAT模式，去掉延迟重启
- `client_mode.lua` - 修复函数调用，增强扫描功能
- `ap_mode.lua` - 去掉延迟重启逻辑
- `route_mode.lua` - 去掉延迟重启逻辑

新增的文件：
- `BRIDGE_MODE_API_GUIDE.md` - Bridge模式API文档
- `CLIENT_MODE_API_GUIDE.md` - Client模式API文档
- `MODIFICATIONS_SUMMARY.md` - 修改总结文档

所有修改已完成，可以进行测试和部署。
