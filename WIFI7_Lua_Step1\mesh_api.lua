#!/usr/bin/lua

--[[
WiFi7 Mesh Main API Entry Point
基于WiFi6 son_topo实现，为WiFi7设备提供统一的mesh API接口
整合所有mesh相关功能模块
作者: WiFi7开发团队
日期: 2025-07-07
]]

local json = require("json")

-- 引入所有mesh相关模块
local mesh_config = require("mesh_config")
local mesh_service = require("mesh_service")
local mesh_cap_api = require("mesh_cap_api")
local mesh_re_api = require("mesh_re_api")
local mesh_monitor = require("mesh_monitor")

local mesh_api = {}

-- 检测是否为CGI环境
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- 日志函数
local function log_message(level, message)
    if is_cgi() then
        os.execute(string.format("logger -t mesh_api '[%s] %s'", level, message))
    else
        print(string.format("[%s] %s", level, message))
    end
end

-- API版本信息
mesh_api.version = {
    major = 1,
    minor = 0,
    patch = 0,
    build = "20250107"
}

-- 获取API版本信息
function mesh_api.get_version()
    return mesh_api.version
end

-- 获取mesh功能概览
function mesh_api.get_overview()
    local config = mesh_config.get_config()
    local status = mesh_monitor.get_full_status_report()

    local overview = {
        mesh_enabled = config.mesh_enabled or false,
        mesh_mode = config.cap_mode and "CAP" or "RE",
        mesh_ssid = config.meshssid or "",
        node_count = status.mesh_topology.total_nodes,
        health_score = status.health_score,
        system_uptime = status.system.uptime,
        services_status = status.mesh_services,
        last_update = os.time()
    }

    return overview
end

-- 初始化mesh功能
function mesh_api.initialize_mesh()
    log_message("INFO", "Initializing mesh functionality")

    -- 初始化配置
    local config_init = mesh_config.init_uci_config()
    if not config_init then
        return false, "Failed to initialize mesh configuration"
    end

    -- 检查系统要求
    local system_check = mesh_api.check_system_requirements()
    if not system_check.compatible then
        return false, "System requirements not met: " .. (system_check.message or "unknown error")
    end

    log_message("INFO", "Mesh functionality initialized successfully")
    return true, "Mesh functionality initialized successfully"
end

-- 检查系统要求
function mesh_api.check_system_requirements()
    local requirements = {
        compatible = true,
        message = "",
        checks = {}
    }

    -- 检查内存
    local memory = mesh_monitor.get_system_status().memory
    local memory_check = memory.total >= 64 * 1024 -- 至少64MB
    requirements.checks.memory = {
        passed = memory_check,
        current = math.floor(memory.total / 1024) .. "MB",
        required = "64MB"
    }

    if not memory_check then
        requirements.compatible = false
        requirements.message = "Insufficient memory"
    end

    -- 检查无线接口
    local wireless_stats = mesh_monitor.get_wireless_stats()
    local wireless_check = wireless_stats.wlan0 ~= nil or wireless_stats.wlan8 ~= nil
    requirements.checks.wireless = {
        passed = wireless_check,
        interfaces = {}
    }

    for iface, _ in pairs(wireless_stats) do
        if string.find(iface, "wlan") then
            table.insert(requirements.checks.wireless.interfaces, iface)
        end
    end

    if not wireless_check then
        requirements.compatible = false
        requirements.message = "No wireless interfaces found"
    end

    -- 检查必要的服务
    local services = {"wsplcd", "hyd", "repacd"}
    local services_check = true
    requirements.checks.services = {}

    for _, service in ipairs(services) do
        local service_exists = os.execute("which " .. service .. " >/dev/null 2>&1") == 0
        requirements.checks.services[service] = service_exists

        if not service_exists then
            services_check = false
        end
    end

    if not services_check then
        requirements.compatible = false
        requirements.message = "Required mesh services not found"
    end

    return requirements
end

-- 快速配置向导
function mesh_api.quick_setup_wizard(setup_config)
    log_message("INFO", "Starting mesh quick setup wizard")

    if not setup_config or type(setup_config) ~= "table" then
        return false, "Invalid setup configuration"
    end

    -- 验证必要参数
    if not setup_config.mesh_ssid or not setup_config.mesh_password then
        return false, "Mesh SSID and password are required"
    end

    if not setup_config.mode or (setup_config.mode ~= "CAP" and setup_config.mode ~= "RE") then
        return false, "Mode must be either CAP or RE"
    end

    -- 根据模式执行配置
    local success, message

    if setup_config.mode == "CAP" then
        -- 配置主AP模式
        local cap_config = {
            mesh_ssid = setup_config.mesh_ssid,
            mesh_password = setup_config.mesh_password,
            gateway_mode = setup_config.gateway_mode or true,
            wan_type = setup_config.wan_type or "dhcp",
            wan_config = setup_config.wan_config
        }

        success, message = mesh_cap_api.configure_cap_mode(cap_config)

    else
        -- 配置从AP模式
        local re_config = {
            mesh_ssid = setup_config.mesh_ssid,
            mesh_password = setup_config.mesh_password
        }

        success, message = mesh_re_api.configure_re_mode(re_config)
    end

    if success then
        log_message("INFO", "Mesh quick setup completed successfully")
        return true, "Mesh setup completed successfully"
    else
        log_message("ERROR", "Mesh quick setup failed: " .. (message or "unknown error"))
        return false, message
    end
end

-- 重置mesh配置
function mesh_api.factory_reset()
    log_message("INFO", "Performing mesh factory reset")

    -- 停止所有mesh服务
    mesh_service.stop_mesh_services()

    -- 重置配置
    mesh_config.disable_mesh()

    -- 清理临时文件
    os.execute("rm -f /tmp/mac_mesh /tmp/mesh_topology /var/run/mesh_topo 2>/dev/null")

    log_message("INFO", "Mesh factory reset completed")
    return true, "Factory reset completed successfully"
end

-- 获取API帮助信息
function mesh_api.get_help()
    local help = {
        description = "WiFi7 Mesh API - Comprehensive mesh networking solution",
        version = mesh_api.version,
        endpoints = {
            {
                path = "/mesh_api.lua?action=overview",
                method = "GET",
                description = "Get mesh network overview"
            },
            {
                path = "/mesh_api.lua?action=version",
                method = "GET",
                description = "Get API version information"
            },
            {
                path = "/mesh_api.lua?action=requirements",
                method = "GET",
                description = "Check system requirements"
            },
            {
                path = "/mesh_api.lua",
                method = "POST",
                description = "Configure mesh network",
                body = {
                    action = "quick_setup",
                    config = {
                        mesh_ssid = "string",
                        mesh_password = "string",
                        mode = "CAP|RE",
                        gateway_mode = "boolean (CAP only)",
                        wan_type = "dhcp|static|pppoe (CAP only)"
                    }
                }
            },
            {
                path = "/mesh_api.lua",
                method = "POST",
                description = "Factory reset mesh configuration",
                body = {
                    action = "factory_reset"
                }
            }
        },
        modules = {
            mesh_config = "Basic mesh configuration management",
            mesh_service = "Mesh service control and wireless configuration",
            mesh_cap_api = "Controller Access Point (main AP) API",
            mesh_re_api = "Range Extender (slave AP) API",
            mesh_monitor = "Status monitoring and performance tracking"
        }
    }

    return help
end

-- CGI接口处理
function mesh_api.handle_cgi()
    if not is_cgi() then
        return
    end

    -- 设置HTTP头
    print("Content-Type: application/json")
    print("Cache-Control: no-cache")
    print("")

    local method = os.getenv("REQUEST_METHOD")
    local query_string = os.getenv("QUERY_STRING") or ""
    local response = {success = false, message = "Unknown error"}

    if method == "GET" then
        if string.find(query_string, "action=overview") then
            -- 获取mesh概览
            local overview = mesh_api.get_overview()
            response = {success = true, data = overview}

        elseif string.find(query_string, "action=version") then
            -- 获取版本信息
            local version = mesh_api.get_version()
            response = {success = true, data = version}

        elseif string.find(query_string, "action=requirements") then
            -- 检查系统要求
            local requirements = mesh_api.check_system_requirements()
            response = {success = true, data = requirements}

        elseif string.find(query_string, "action=help") then
            -- 获取帮助信息
            local help = mesh_api.get_help()
            response = {success = true, data = help}

        else
            response = {success = false, message = "Invalid action"}
        end

    elseif method == "POST" then
        -- 处理POST请求
        local content_length = tonumber(os.getenv("CONTENT_LENGTH")) or 0
        if content_length > 0 then
            local post_data = io.read(content_length)
            local success, data = pcall(json.decode, post_data)

            if success and data then
                if data.action == "initialize" then
                    local result, msg = mesh_api.initialize_mesh()
                    response = {success = result, message = msg}

                elseif data.action == "quick_setup" then
                    local result, msg = mesh_api.quick_setup_wizard(data.config)
                    response = {success = result, message = msg}

                elseif data.action == "factory_reset" then
                    local result, msg = mesh_api.factory_reset()
                    response = {success = result, message = msg}

                else
                    response = {success = false, message = "Invalid action"}
                end
            else
                response = {success = false, message = "Invalid JSON data"}
            end
        else
            response = {success = false, message = "No data provided"}
        end
    end

    print(json.encode(response))
end

-- 如果作为CGI运行，处理请求
if is_cgi() then
    mesh_api.handle_cgi()
end

return mesh_api