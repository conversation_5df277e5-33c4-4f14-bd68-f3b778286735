#!/bin/bash

echo "=== Client Mode 诊断脚本 ==="

# 1. 检查文件是否存在
echo "1. 检查文件存在性:"
if [ -f "client_mode.lua" ]; then
    echo "✓ client_mode.lua 存在"
    ls -la client_mode.lua
else
    echo "✗ client_mode.lua 不存在"
fi

# 2. 检查文件权限
echo ""
echo "2. 检查文件权限:"
if [ -x "client_mode.lua" ]; then
    echo "✓ client_mode.lua 有执行权限"
else
    echo "✗ client_mode.lua 没有执行权限"
    echo "尝试添加执行权限..."
    chmod +x client_mode.lua
fi

# 3. 检查文件内容关键部分
echo ""
echo "3. 检查文件是否包含GET请求支持:"
if grep -q "REQUEST_METHOD" client_mode.lua; then
    echo "✓ 文件包含GET请求支持"
else
    echo "✗ 文件不包含GET请求支持，需要更新文件"
fi

# 4. 检查Lua语法
echo ""
echo "4. 检查Lua语法:"
lua -c client_mode.lua
if [ $? -eq 0 ]; then
    echo "✓ Lua语法正确"
else
    echo "✗ Lua语法错误"
fi

# 5. 测试基本执行
echo ""
echo "5. 测试基本执行:"
export REQUEST_METHOD="GET"
export QUERY_STRING="action=get_status"

echo "设置环境变量:"
echo "REQUEST_METHOD: $REQUEST_METHOD"
echo "QUERY_STRING: $QUERY_STRING"

echo ""
echo "执行脚本:"
timeout 10 lua client_mode.lua 2>&1
exit_code=$?

if [ $exit_code -eq 0 ]; then
    echo "✓ 脚本执行成功"
elif [ $exit_code -eq 124 ]; then
    echo "⚠ 脚本执行超时（可能正常，因为可能在等待网络操作）"
else
    echo "✗ 脚本执行失败，退出码: $exit_code"
fi

# 6. 检查日志
echo ""
echo "6. 检查日志文件:"
if [ -f "/tmp/client_mode.log" ]; then
    echo "✓ 日志文件存在"
    echo "最新日志内容:"
    tail -5 /tmp/client_mode.log
else
    echo "✗ 日志文件不存在"
fi

# 7. 检查依赖模块
echo ""
echo "7. 检查Lua模块依赖:"
lua -e "
local ok, cjson = pcall(require, 'cjson.safe')
if ok then print('✓ cjson.safe 模块可用') else print('✗ cjson.safe 模块不可用') end

local ok, sys = pcall(require, 'luci.sys')
if ok then print('✓ luci.sys 模块可用') else print('✗ luci.sys 模块不可用') end

local ok, uci = pcall(require, 'luci.model.uci')
if ok then print('✓ luci.model.uci 模块可用') else print('✗ luci.model.uci 模块不可用') end
"

echo ""
echo "=== 诊断完成 ==="
