#!/usr/bin/lua

--[[
WiFi7 Mesh Configuration Management Module
基于WiFi6 son_topo实现，为WiFi7设备提供mesh配置管理功能
参考: package/son_topo/files/mesh.config 和 ubus_mesh.c
作者: WiFi7开发团队
日期: 2025-01-07
]]

local uci = require("uci")
local json = require("json")

local mesh_config = {}

-- 检测是否为CGI环境
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- 日志函数
local function log_message(level, message)
    if is_cgi() then
        -- CGI环境下写入系统日志
        os.execute(string.format("logger -t mesh_config '[%s] %s'", level, message))
    else
        -- 命令行环境下直接输出
        print(string.format("[%s] %s", level, message))
    end
end

-- UCI配置路径定义
local UCI_MESH_CONFIG = "mesh"
local UCI_WIRELESS_CONFIG = "wireless"
local UCI_NETWORK_CONFIG = "network"
local UCI_DHCP_CONFIG = "dhcp"

-- Mesh配置默认值
local MESH_DEFAULTS = {
    workmode = "",           -- 工作模式: "mesh", "mesh_gw", ""
    meshmode = "control",    -- mesh模式: "control", "extend"
    meshssid = "wifison",    -- mesh网络SSID
    meshpass = "1234567890", -- mesh网络密码
    meshdhcpproxy = "ap",    -- DHCP代理模式: "ap", "client"
    changeflag = "0",        -- 配置变更标志
    mesh_enabled = false,    -- mesh功能是否启用
    cap_mode = false,        -- 是否为主AP(CAP)模式
    gateway_mode = true,     -- 是否为网关模式
    wan_type = "dhcp"        -- 上网方式: "dhcp", "static", "pppoe"
}

-- 初始化UCI配置文件
function mesh_config.init_uci_config()
    local cursor = uci.cursor()

    -- 检查mesh配置文件是否存在
    local mesh_exists = cursor:get_all(UCI_MESH_CONFIG)
    if not mesh_exists then
        log_message("INFO", "Creating mesh UCI configuration")

        -- 创建mesh配置段
        cursor:set(UCI_MESH_CONFIG, "mesh", "wirelessmesh")
        for key, value in pairs(MESH_DEFAULTS) do
            if type(value) == "boolean" then
                cursor:set(UCI_MESH_CONFIG, "mesh", key, value and "1" or "0")
            else
                cursor:set(UCI_MESH_CONFIG, "mesh", key, tostring(value))
            end
        end

        cursor:commit(UCI_MESH_CONFIG)
        log_message("INFO", "Mesh UCI configuration created successfully")
    end

    return true
end

-- 获取mesh配置
function mesh_config.get_config()
    local cursor = uci.cursor()
    local config = {}

    -- 从UCI读取mesh配置
    local mesh_section = cursor:get_all(UCI_MESH_CONFIG, "mesh")
    if mesh_section then
        for key, value in pairs(mesh_section) do
            if key ~= ".type" and key ~= ".name" then
                -- 转换布尔值
                if value == "1" or value == "true" then
                    config[key] = true
                elseif value == "0" or value == "false" then
                    config[key] = false
                else
                    config[key] = value
                end
            end
        end
    else
        -- 使用默认配置
        config = MESH_DEFAULTS
    end

    -- 获取当前网络模式信息
    local network_workmode = cursor:get(UCI_NETWORK_CONFIG, "lan", "workmode") or ""
    config.current_workmode = network_workmode

    -- 获取无线配置状态
    local radio0_disabled = cursor:get(UCI_WIRELESS_CONFIG, "radio0", "disabled") or "0"
    local radio1_disabled = cursor:get(UCI_WIRELESS_CONFIG, "radio1", "disabled") or "0"
    config.radio0_enabled = (radio0_disabled == "0")
    config.radio1_enabled = (radio1_disabled == "0")

    log_message("DEBUG", "Retrieved mesh config: " .. json.encode(config))
    return config
end

-- 设置mesh配置
function mesh_config.set_config(new_config)
    if not new_config or type(new_config) ~= "table" then
        log_message("ERROR", "Invalid mesh configuration provided")
        return false, "Invalid configuration"
    end

    local cursor = uci.cursor()

    -- 确保mesh配置段存在
    mesh_config.init_uci_config()

    -- 更新mesh配置
    for key, value in pairs(new_config) do
        if MESH_DEFAULTS[key] ~= nil then
            if type(value) == "boolean" then
                cursor:set(UCI_MESH_CONFIG, "mesh", key, value and "1" or "0")
            else
                cursor:set(UCI_MESH_CONFIG, "mesh", key, tostring(value))
            end
            log_message("DEBUG", string.format("Set mesh.%s = %s", key, tostring(value)))
        end
    end

    -- 设置变更标志
    cursor:set(UCI_MESH_CONFIG, "mesh", "changeflag", "1")

    -- 提交配置
    cursor:commit(UCI_MESH_CONFIG)

    log_message("INFO", "Mesh configuration updated successfully")
    return true
end

-- 启用mesh模式
function mesh_config.enable_mesh(mesh_ssid, mesh_password, is_cap_mode, gateway_config)
    local config = {
        mesh_enabled = true,
        meshssid = mesh_ssid or MESH_DEFAULTS.meshssid,
        meshpass = mesh_password or MESH_DEFAULTS.meshpass,
        cap_mode = is_cap_mode or false,
        workmode = is_cap_mode and "mesh_gw" or "mesh"
    }

    -- 如果是主AP模式，设置网关配置
    if is_cap_mode and gateway_config then
        config.gateway_mode = gateway_config.gateway_mode or true
        config.wan_type = gateway_config.wan_type or "dhcp"
    end

    return mesh_config.set_config(config)
end

-- 禁用mesh模式
function mesh_config.disable_mesh()
    local config = {
        mesh_enabled = false,
        workmode = "",
        cap_mode = false
    }

    return mesh_config.set_config(config)
end

-- 获取mesh状态
function mesh_config.get_status()
    local config = mesh_config.get_config()
    local status = {
        enabled = config.mesh_enabled or false,
        mode = config.workmode or "",
        ssid = config.meshssid or "",
        is_cap = config.cap_mode or false,
        gateway_mode = config.gateway_mode or false,
        radio_status = {
            radio0_enabled = config.radio0_enabled or false,
            radio1_enabled = config.radio1_enabled or false
        }
    }

    return status
end

-- 验证mesh配置
function mesh_config.validate_config(config)
    if not config or type(config) ~= "table" then
        return false, "Configuration must be a table"
    end

    -- 验证SSID
    if config.meshssid then
        if type(config.meshssid) ~= "string" or #config.meshssid < 1 or #config.meshssid > 32 then
            return false, "Mesh SSID must be 1-32 characters"
        end
    end

    -- 验证密码
    if config.meshpass then
        if type(config.meshpass) ~= "string" or #config.meshpass < 8 or #config.meshpass > 63 then
            return false, "Mesh password must be 8-63 characters"
        end
    end

    -- 验证工作模式
    if config.workmode then
        local valid_modes = {[""] = true, ["mesh"] = true, ["mesh_gw"] = true}
        if not valid_modes[config.workmode] then
            return false, "Invalid work mode"
        end
    end

    return true
end

-- CGI接口处理
function mesh_config.handle_cgi()
    if not is_cgi() then
        return
    end

    -- 设置HTTP头
    print("Content-Type: application/json")
    print("Cache-Control: no-cache")
    print("")

    local method = os.getenv("REQUEST_METHOD")
    local response = {success = false, message = "Unknown error"}

    if method == "GET" then
        -- 获取mesh配置
        local config = mesh_config.get_config()
        response = {success = true, data = config}

    elseif method == "POST" then
        -- 设置mesh配置
        local content_length = tonumber(os.getenv("CONTENT_LENGTH")) or 0
        if content_length > 0 then
            local post_data = io.read(content_length)
            local success, new_config = pcall(json.decode, post_data)

            if success and new_config then
                local valid, err_msg = mesh_config.validate_config(new_config)
                if valid then
                    local result, msg = mesh_config.set_config(new_config)
                    response = {success = result, message = msg or "Configuration updated"}
                else
                    response = {success = false, message = err_msg}
                end
            else
                response = {success = false, message = "Invalid JSON data"}
            end
        else
            response = {success = false, message = "No data provided"}
        end
    end

    print(json.encode(response))
end

-- 如果作为CGI运行，处理请求
if is_cgi() then
    mesh_config.handle_cgi()
end

return mesh_config