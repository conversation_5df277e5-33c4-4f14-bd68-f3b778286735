#!/usr/bin/lua
-- Copyright (c) 2013 The Linux Foundation. All rights reserved.
-- Not a Contribution.

-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

-- Lua环境：此脚本在OpenWRT的Lua环境中运行，确保有必要的Lua库，如luci.jsonc和luci.sys
-- UCI：这个脚本使用了uci库来直接操作配置文件。这比直接读写文件更为安全和标准。

-- 说明:
-- 用户事件配置文件：使用一个自定义的JSON配置文件来存储用户事件的配置。

-- 读写配置：read_user_event_config和write_user_event_config函数用于处理配置文件的读写。

-- 发送事件：send_user_event函数根据配置发送事件到指定的服务器。

-- API调用：

  -- 设置配置：
   -- JSON
   --{
   --"version": "1.0",
   -- "sid": "5bdd8aec153a8ff183387bfcea40e3aa",
   -- "module": "user_event",
   -- "api": "set",
   -- "param": {
   --     "enabled": "1",
   --     "protocol": "udp",
   --     "server": "192.168.1.100",
   --     "port": "5000"
   -- }
 --}

  --获取配置：
  --{
  --  "version": "1.0",
  --  "sid": "5bdd8aec153a8ff183387bfcea40e3aa",
  --  "module": "user_event",
  --  "api": "get",
  --  "param": {}
  --}

-- 用户事件功能需求：
-- 开关：控制是否允许用户事件（默认关闭，enabled = "0" ）。
-- 协议类型：支持TCP、UDP和HTTP（默认TCP）
-- 服务器地址和端口：指定事件发送的目标服务器。
-- Set 和 Get：设置和获取上述配置。
-- 是否需要启动socket进程：
-- 当前实现：你的代码中，send_user_event函数在事件发生时通过sys.call外部调用工具（nc或curl）发送数据到服务器。这种方式不需要常驻的socket进程，而是依赖事件触发时即时发送。
-- 判断：只需设置服务器地址和端口，并在事件发生时调用发送逻辑即可，不需要额外启动一个socket进程。设备的无线用户上下线事件通常由系统（如hostapd或其他守护进程）触发，实际的事件捕获和调用send_user_event的逻辑应在其他地方实现（如通过脚本或系统钩子）。
--建议：当前代码仅负责配置管理（set和get），发送逻辑应由外部机制（如hostapd的脚本或事件监听器）调用send_user_event，从而在Lua脚本中启动socket进程。
-- 配置文件：

-- 引入模块
-- 移除 UCI 依赖
local cjson = require("cjson.safe")
local sys = require("luci.sys")
local socket = require("socket")
local log_file = "/tmp/user_event.log"
local config_file = "/etc/config/user_event.json"
local pid_file = "/tmp/user_event_monitor.pid"  -- 监听进程 PID 文件

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    end
end



-- 验证 IP 地址或域名
local function is_valid_server(server)
    return server and (server:match("^%d+%.%d+%.%d+%.%d+$") or server:match("^[%w%.%-]+$")) ~= nil
end

-- 验证端口号
local function is_valid_port(port)
    local p = tonumber(port)
    return p and p >= 1 and p <= 65535
end
-- 检查监控进程是否正在运行
local function is_monitor_running()
    local file = io.open(pid_file, "r")
    if file then
        local pid = file:read("*all"):match("(%d+)")
        file:close()
        if pid then
            local running = sys.call("ps | grep -w '" .. pid .. "' | grep 'user_event_monitor' | grep -v grep") == 0
            return running
        end
    end
    return false
end

-- 启动监听进程
local function start_monitor()
    if not is_monitor_running() then
        local pid = sys.call("lua /www/cgi-bin/3onedata/user_event_monitor & echo $! > " .. pid_file)
        if pid == 0 then
            write_log("Started user event monitor with PID: " .. io.open(pid_file, "r"):read("*all"))
        else
            write_log("Failed to start user event monitor")
        end
    else
        write_log("User event monitor is already running.")
    end
end

-- 停止监听进程 (此函数看起来没有问题，不需要修改)
local function stop_monitor()
    local file = io.open(pid_file, "r")
    if file then
        local pid = file:read("*all"):match("(%d+)")
        file:close()
        if pid and pid ~= "" then
            sys.call("kill " .. pid)
            sys.call("rm -f " .. pid_file)
            write_log("Stopped user event monitor with PID: " .. pid)
        else
            write_log("No PID found for user_event_monitor")
        end
    else
        write_log("No running monitor found")
    end
end

-- 发送用户事件
local function send_user_event(protocol, server, port, event_data)
    if protocol == "tcp" then
        local f = io.popen(string.format("nc %s %s", server, port), "w")
        if f then
            f:write(event_data)
            f:close()
        end
    elseif protocol == "udp" then
        local udp = socket.udp()
        udp:settimeout(1)
        udp:sendto(event_data, server, tonumber(port))
        udp:close()
    elseif protocol == "http" then
        os.execute(string.format("curl -X POST -d '%s' http://%s:%s/", event_data, server, port))
    end
end

-- 设置用户事件配置
function set_user_event(data)
    local param = data.param or {}
    local current_config = {}
    local file = io.open(config_file, "r")
    if file then
        local content = file:read("*all")
        current_config = cjson.decode(content) or {}
        file:close()
    else
        current_config = { enabled = "0", protocol = "tcp", server = "", port = "80" }
    end

    local enabled = param.enabled or current_config.enabled
    local protocol = param.protocol or current_config.protocol
    local server = param.server or current_config.server
    local port = param.port or current_config.port

    if enabled ~= "0" and enabled ~= "1" then
        local error_message = "Invalid enabled value: " .. enabled
        write_log(error_message)
        io.write(cjson.encode({
            module = "user_event",
            version = "1.0",
            api = "set",
            errcode = 5,
            sid = data.sid,
            result = { message = error_message }
        }))
        return
    end

    if protocol ~= "tcp" and protocol ~= "udp" and protocol ~= "http" then
        local error_message = "Invalid protocol: " .. protocol
        write_log(error_message)
        io.write(cjson.encode({
            module = "user_event",
            version = "1.0",
            api = "set",
            errcode = 6,
            sid = data.sid,
            result = { message = error_message }
        }))
        return
    end

    if not is_valid_server(server) then
        local error_message = "Invalid server address: " .. server
        write_log(error_message)
        io.write(cjson.encode({
            module = "user_event",
            version = "1.0",
            api = "set",
            errcode = 7,
            sid = data.sid,
            result = { message = error_message }
        }))
        return
    end

    if not is_valid_port(port) then
        local error_message = "Invalid port: " .. port
        write_log(error_message)
        io.write(cjson.encode({
            module = "user_event",
            version = "1.0",
            api = "set",
            errcode = 8,
            sid = data.sid,
            result = { message = error_message }
        }))
        return
    end

    local new_config = {
        enabled = enabled,
        protocol = protocol,
        server = server,
        port = port
    }
    file = io.open(config_file, "w")
    if file then
        file:write(cjson.encode(new_config))
        file:close()
        write_log("Configuration file written successfully: " .. config_file)
    else
        write_log("Failed to write configuration file: " .. config_file)
    end

    -- 开关控制
    if enabled == "1" and current_config.enabled == "0" then
        start_monitor()
    elseif enabled == "0" and current_config.enabled == "1" then
        stop_monitor()
    end

    write_log("User event settings updated: enabled=" .. enabled .. ", protocol=" .. protocol .. ", server=" .. server .. ", port=" .. port)
    io.write(cjson.encode({
        module = "user_event",
        version = "1.0",
        api = "set",
        errcode = 0,
        sid = data.sid,
        result = { message = "User event settings updated successfully" }
    }))
end

-- 获取用户事件配置
function get_user_event(data)
    local config = { enabled = "0", protocol = "tcp", server = "", port = "80" }
    local file = io.open(config_file, "r")
    if file then
        local content = file:read("*all")
        config = cjson.decode(content) or config
        file:close()
    end

    write_log("User event settings retrieved: enabled=" .. config.enabled .. ", protocol=" .. config.protocol .. ", server=" .. config.server .. ", port=" .. config.port)
    io.write(cjson.encode({
        module = "user_event",
        version = "1.0",
        api = "get",
        errcode = 0,
        sid = data.sid,
        result = {
            enabled = config.enabled,
            protocol = config.protocol,
            server = config.server,
            port = config.port
        }
    }))
end

-- 路由到具体逻辑
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 获取 POST 数据
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""
    if POSTLength > 0 then
        POST = io.read(POSTLength)
        write_log("Received POST data: " .. (POST or "nil"))
    else
        write_log("No POST data received or CONTENT_LENGTH is 0")
    end

    if not POST or POST == "" then
        local error_message = "Failed to retrieve POST data"
        write_log(error_message)
        io.write(cjson.encode({
            module = "user_event",
            version = "1.0",
            errcode = 1,
            result = { message = error_message }
        }))
        return
    end

    -- 解析 POST 数据为 JSON
    local requestData = cjson.decode(POST)
    if not requestData then
        local error_message = "Invalid JSON input"
        write_log(error_message)
        io.write(cjson.encode({
            module = "user_event",
            version = "1.0",
            errcode = 2,
            result = { message = error_message }
        }))
        return
    end

    write_log("Parsed request data: " .. cjson.encode(requestData))

    -- 检查请求格式
    if not requestData.version or not requestData.sid or not requestData.module or not requestData.api then
        local error_message = "Invalid request format"
        write_log(error_message)
        io.write(cjson.encode({
            module = "user_event",
            version = "1.0",
            errcode = 3,
            result = { message = error_message }
        }))
        return
    end

    if requestData.api == "set" then
        write_log("Calling set_user_event with data: " .. cjson.encode(requestData))
        set_user_event(requestData)
    elseif requestData.api == "get" then
        write_log("Calling get_user_event with data: " .. cjson.encode(requestData))
        get_user_event(requestData)
    else
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "user_event",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
    end
end

-- 检查是否在CGI环境中运行
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- AC 调用接口
local M = {}

function M.set_config_from_ac(payload)
    write_log("[AC] set_config_from_ac called: " .. cjson.encode(payload))

    -- 调用现有的set_user_event函数
    local data = { param = payload }
    set_user_event(data)

    write_log("[AC] User event configuration applied")
    return true, "User event configuration applied by AC"
end

function M.get_config_for_ac()
    local config = read_user_event_config()
    write_log("[AC] get_config_for_ac: retrieved user event config")
    return config
end

-- 仅在明确作为 CGI 脚本运行时执行
if arg and arg[0] and arg[0]:match("user_event%.lua") and is_cgi() then
    local function run()
        write_log("User Event API started")
        route_api()
        write_log("User Event API finished")
    end
    run()
end

return M