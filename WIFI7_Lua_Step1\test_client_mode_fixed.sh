#!/bin/bash

echo "=== 测试 client_mode.lua 修复版本 ==="

# 1. 首先检查语法
echo "1. 检查Lua语法:"
lua -c client_mode.lua
if [ $? -eq 0 ]; then
    echo "✓ 语法检查通过"
else
    echo "✗ 语法检查失败"
    exit 1
fi

echo ""
echo "2. 测试 GET 请求 - get_status API"

# 设置环境变量
export REQUEST_METHOD="GET"
export QUERY_STRING="action=get_status"

echo "REQUEST_METHOD: $REQUEST_METHOD"
echo "QUERY_STRING: $QUERY_STRING"

echo ""
echo "执行结果:"
timeout 10 lua client_mode.lua 2>&1
exit_code=$?

if [ $exit_code -eq 0 ]; then
    echo ""
    echo "✓ 脚本执行成功"
elif [ $exit_code -eq 124 ]; then
    echo ""
    echo "⚠ 脚本执行超时（可能正常）"
else
    echo ""
    echo "✗ 脚本执行失败，退出码: $exit_code"
fi

echo ""
echo "3. 检查日志文件:"
if [ -f "/tmp/client_mode.log" ]; then
    echo "✓ 日志文件存在"
    echo "最新日志内容:"
    tail -10 /tmp/client_mode.log
else
    echo "✗ 日志文件不存在"
fi

echo ""
echo "4. 测试 Web 请求模拟:"
echo "模拟curl请求..."

# 模拟Web环境变量
export REQUEST_METHOD="GET"
export QUERY_STRING="action=get_status"
export HTTP_HOST="*************"
export SERVER_NAME="*************"
export REQUEST_URI="/cgi-bin/3onedata/client_mode.lua?action=get_status"

echo "Web环境变量已设置"
echo "再次执行脚本:"
timeout 5 lua client_mode.lua 2>&1

echo ""
echo "=== 测试完成 ==="
