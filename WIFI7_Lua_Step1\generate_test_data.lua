#!/usr/bin/lua

-- AC协议测试数据生成工具
-- 生成完整的十六进制数据包，用于网络助手测试

local cjson = require("cjson.safe")

-- 协议常量
local PROTOCOL_VERSION = "1.0"
local AC_IP = "*************"  -- AC控制器IP
local AP_MAC = "AA:BB:CC:DD:EE:FF"  -- AP设备MAC地址

-- TLV类型定义
local TLV_TYPES = {
    COMMAND = 0x01,
    DATA = 0x02,
    STATUS = 0x03,
    CONFIG = 0x04,
    HEARTBEAT = 0x05
}

-- 命令类型定义
local COMMANDS = {
    DISCOVERY = 0x01,
    CONFIG_GET = 0x02,
    CONFIG_SET = 0x03,
    STATUS_GET = 0x04,
    UPGRADE = 0x05,
    REBOOT = 0x06,
    HEARTBEAT = 0x07
}

-- 将字符串转换为十六进制
local function string_to_hex(str)
    return (str:gsub('.', function(c)
        return string.format('%02X', string.byte(c))
    end))
end

-- 将数字转换为十六进制字节
local function num_to_hex_bytes(num, bytes)
    local result = ""
    for i = bytes-1, 0, -1 do
        result = result .. string.format("%02X", (num >> (i*8)) & 0xFF)
    end
    return result
end

-- 生成TLV数据
local function generate_tlv(type, data)
    local hex_data = string_to_hex(data)
    local length = #data
    return num_to_hex_bytes(type, 1) .. num_to_hex_bytes(length, 2) .. hex_data
end

-- 生成UDP头部
local function generate_udp_header(payload_length)
    local total_length = payload_length + 8  -- UDP头部8字节
    return "1388" .. "C351" .. num_to_hex_bytes(total_length, 2) .. "0000"  -- 源端口5000, 目标端口50001
end

-- 生成基础JSON数据
local function generate_base_json(module, api, extra_data)
    local data = {
        version = PROTOCOL_VERSION,
        sid = "test_session_" .. os.time(),
        module = module,
        api = api,
        timestamp = os.time()
    }
    
    if extra_data then
        for k, v in pairs(extra_data) do
            data[k] = v
        end
    end
    
    return cjson.encode(data)
end

-- 测试用例定义
local test_cases = {
    {
        name = "Discovery广播包",
        description = "AP发现AC控制器",
        json_data = generate_base_json("discovery", "broadcast", {
            ap_mac = AP_MAC,
            device_type = "IAP3500-E11",
            firmware_version = "1.0.0"
        })
    },
    {
        name = "心跳包",
        description = "AP向AC发送心跳",
        json_data = generate_base_json("heartbeat", "ping", {
            ap_mac = AP_MAC,
            status = "online"
        })
    },
    {
        name = "配置获取",
        description = "AC获取AP配置",
        json_data = generate_base_json("config", "get", {
            target_mac = AP_MAC,
            config_type = "all"
        })
    },
    {
        name = "WiFi配置下发",
        description = "AC下发WiFi配置到AP",
        json_data = generate_base_json("wifi", "set", {
            target_mac = AP_MAC,
            param = {
                wifi_2_4g = {
                    enabled = true,
                    ssid = "Test_2.4G",
                    password = "12345678",
                    channel = 6,
                    bandwidth = "20MHz"
                },
                wifi_5g = {
                    enabled = true,
                    ssid = "Test_5G",
                    password = "12345678",
                    channel = 36,
                    bandwidth = "80MHz"
                }
            }
        })
    },
    {
        name = "网络模式配置",
        description = "AC配置AP网络模式",
        json_data = generate_base_json("network", "set", {
            target_mac = AP_MAC,
            param = {
                mode = "route",  -- ap/route/bridge
                lan_ip = "***********",
                lan_mask = "*************",
                dhcp_enabled = true,
                dhcp_start = "*************",
                dhcp_end = "*************"
            }
        })
    },
    {
        name = "VLAN配置",
        description = "AC配置AP的VLAN",
        json_data = generate_base_json("vlan", "set", {
            target_mac = AP_MAC,
            param = {
                vlan_id = 100,
                vlan_name = "Guest",
                ports = "1,2,3"
            }
        })
    },
    {
        name = "QoS配置",
        description = "AC配置AP的QoS策略",
        json_data = generate_base_json("qos", "set", {
            target_mac = AP_MAC,
            param = {
                enabled = true,
                upload_limit = 1000,  -- Mbps
                download_limit = 1000,
                priority_rules = {
                    {protocol = "tcp", port = 80, priority = "high"},
                    {protocol = "udp", port = 53, priority = "high"}
                }
            }
        })
    },
    {
        name = "用户管理",
        description = "AC管理连接用户",
        json_data = generate_base_json("user", "set", {
            target_mac = AP_MAC,
            param = {
                action = "add",
                user_mac = "11:22:33:44:55:66",
                bandwidth_limit = 10,  -- Mbps
                access_control = "allow"
            }
        })
    },
    {
        name = "定时重启",
        description = "AC配置AP定时重启",
        json_data = generate_base_json("reboot", "set", {
            target_mac = AP_MAC,
            param = {
                enabled = true,
                schedule = "0 3 * * 0",  -- 每周日凌晨3点
                immediate = false
            }
        })
    },
    {
        name = "固件升级",
        description = "AC触发AP固件升级",
        json_data = generate_base_json("upgrade", "start", {
            target_mac = AP_MAC,
            param = {
                firmware_url = "http://*************/firmware.bin",
                firmware_version = "1.1.0",
                md5_checksum = "d41d8cd98f00b204e9800998ecf8427e"
            }
        })
    }
}

-- 生成完整的测试数据包
local function generate_packet(json_data)
    -- 生成TLV数据
    local tlv_data = generate_tlv(TLV_TYPES.DATA, json_data)
    
    -- 计算总长度
    local payload_length = #tlv_data / 2  -- 十六进制字符串长度除以2
    
    -- 生成UDP头部
    local udp_header = generate_udp_header(payload_length)
    
    -- 组合完整数据包
    return udp_header .. tlv_data
end

-- 主函数
local function main()
    print("=== AC协议测试数据生成工具 ===")
    print("目标设备: " .. AP_MAC)
    print("AC控制器: " .. AC_IP)
    print("目标端口: 50001")
    print("")
    
    for i, test_case in ipairs(test_cases) do
        print(string.format("[%d] %s", i, test_case.name))
        print("描述: " .. test_case.description)
        print("JSON数据:")
        print(test_case.json_data)
        print("")
        print("十六进制数据包:")
        local hex_packet = generate_packet(test_case.json_data)
        print(hex_packet)
        print("")
        print("网络助手发送命令:")
        print(string.format("目标IP: 设备IP地址"))
        print(string.format("目标端口: 50001"))
        print(string.format("数据: %s", hex_packet))
        print("=" .. string.rep("=", 60))
        print("")
    end
    
    print("使用说明:")
    print("1. 在PC网络助手中设置目标IP为设备IP地址")
    print("2. 设置目标端口为50001")
    print("3. 选择UDP协议")
    print("4. 复制上述十六进制数据到发送框")
    print("5. 点击发送，观察设备日志反应")
    print("")
    print("预期结果:")
    print("- 设备应该接收到数据包")
    print("- /tmp/ac_main_loop.log 应该显示接收到的数据")
    print("- /tmp/ac_handlers.log 应该显示处理过程")
    print("- 设备应该返回相应的响应数据")
end

-- 执行主函数
main()
