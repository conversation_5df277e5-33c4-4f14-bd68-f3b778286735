# PPPoE 拨号连接修复总结

## 问题描述

client_mode.lua和bridge_mode.lua中的PPPoE拨号连接不成功，主要原因是PPPoE配置方式不正确。PPPoE需要特殊的网络接口配置，不能简单地在wwan接口上设置proto为pppoe。

## 修复方案

### 1. PPPoE配置架构

正确的PPPoE配置需要两个网络接口：
1. **基础物理接口 (wwan)**: 设置为`proto = "none"`，作为物理连接
2. **PPPoE逻辑接口 (pppoe-wan)**: 设置为`proto = "pppoe"`，依赖于wwan接口

### 2. 修复的文件

#### 2.1 client_mode.lua 修复内容

**修复前的问题:**
```lua
-- 错误的配置方式
uci:set("network", "wwan", "proto", "pppoe")
uci:set("network", "wwan", "username", username)
uci:set("network", "wwan", "password", password)
```

**修复后的正确配置:**
```lua
-- 正确的PPPoE配置
-- 1. 配置基础wwan接口
uci:set("network", "wwan", "proto", "none")
uci:set("network", "wwan", "ifname", sta_ifname)

-- 2. 配置PPPoE接口
uci:set("network", "pppoe-wan", "proto", "pppoe")
uci:set("network", "pppoe-wan", "device", "wwan")  -- 依赖wwan接口
uci:set("network", "pppoe-wan", "username", username)
uci:set("network", "pppoe-wan", "password", password)
uci:set("network", "pppoe-wan", "defaultroute", "1")
uci:set("network", "pppoe-wan", "peerdns", "1")
```

#### 2.2 bridge_mode.lua 修复内容

应用了与client_mode.lua相同的修复方案，确保两个模式的PPPoE配置一致。

#### 2.3 防火墙配置修复

**修复前:**
```lua
-- 只包含wwan网络
network = "wwan"
```

**修复后:**
```lua
-- 动态检测并包含PPPoE网络
local wwan_networks = "wwan"
if uci:get("network", "pppoe-wan", "proto") == "pppoe" then
    wwan_networks = "wwan pppoe-wan"
end
```

### 3. 支持的PPPoE参数

#### 3.1 必需参数
- **username**: PPPoE用户名
- **password**: PPPoE密码

#### 3.2 可选参数
- **auth_type**: 认证类型 ("PAP" | "CHAP")，默认"PAP"
- **server**: PPPoE服务器名称
- **service**: PPPoE服务名称
- **ac**: PPPoE AC (Access Concentrator) 名称
- **dns**: DNS服务器设置

#### 3.3 自动设置参数
- **defaultroute**: 自动设置为"1"，使PPPoE成为默认路由
- **peerdns**: 自动设置为"1"，使用ISP提供的DNS

### 4. JSON API 格式

#### 4.1 Client Mode PPPoE 配置
```json
{
    "module": "client_mode",
    "api": "set",
    "param": {
        "wan": {
            "mode": "pppoe",
            "username": "your_username",
            "password": "your_password",
            "auth_type": "PAP",
            "server": "",
            "service": "",
            "ac": "",
            "dns1": "*******",
            "dns2": "*******",
            "dns": "******* *******"
        }
    }
}
```

#### 4.2 Bridge Mode NAT PPPoE 配置
```json
{
    "module": "bridge_mode",
    "api": "set",
    "param": {
        "connection_type": "nat",
        "wan": {
            "mode": "pppoe",
            "username": "your_username",
            "password": "your_password",
            "auth_type": "PAP",
            "dns": "******* *******"
        }
    }
}
```

### 5. 网络接口结构

#### 5.1 PPPoE模式下的接口关系
```
STA接口 (ath5) 
    ↓
wwan接口 (proto=none, ifname=ath5)
    ↓
pppoe-wan接口 (proto=pppoe, device=wwan)
    ↓
防火墙wwan区域 (network="wwan pppoe-wan")
```

#### 5.2 与其他模式的对比
- **DHCP模式**: STA → wwan (proto=dhcp)
- **静态IP模式**: STA → wwan (proto=static)
- **PPPoE模式**: STA → wwan (proto=none) → pppoe-wan (proto=pppoe)

### 6. 修复验证

#### 6.1 配置验证命令
```bash
# 检查网络接口配置
uci show network.wwan
uci show network.pppoe-wan

# 检查防火墙配置
uci show firewall | grep wwan

# 检查接口状态
ifconfig
ip route
```

#### 6.2 连接状态检查
```bash
# 检查PPPoE连接状态
ifconfig ppp0  # PPPoE连接成功后会创建ppp0接口

# 检查路由表
ip route show default

# 检查DNS解析
nslookup www.baidu.com
```

### 7. 常见问题解决

#### 7.1 PPPoE拨号失败
- 检查用户名密码是否正确
- 检查wwan接口是否正常up
- 检查STA是否成功连接到上级AP

#### 7.2 拨号成功但无法上网
- 检查防火墙配置是否包含pppoe-wan
- 检查默认路由是否正确
- 检查DNS配置是否生效

#### 7.3 配置不生效
- 确保network服务重启: `/etc/init.d/network restart`
- 确保防火墙服务重启: `/etc/init.d/firewall restart`
- 检查UCI配置是否正确提交: `uci commit`

### 8. 技术改进

#### 8.1 配置健壮性
- 增加了PPPoE参数验证
- 支持多种DNS配置格式
- 自动清理冲突的网络配置

#### 8.2 兼容性
- 保持向后兼容，支持旧的dns参数格式
- 同时支持dns1/dns2和dns参数
- 自动处理接口依赖关系

#### 8.3 调试支持
- 增加详细的日志输出
- 提供配置状态查询API
- 支持配置验证和错误提示

### 9. 部署建议

#### 9.1 升级步骤
1. 备份现有配置
2. 部署新的Lua文件
3. 重启网络服务
4. 验证PPPoE连接功能

#### 9.2 测试建议
- 测试PPPoE拨号连接
- 测试网络连通性
- 测试DNS解析功能
- 测试防火墙NAT转发

### 10. 文档更新

已更新以下文档：
- `CLIENT_MODE_API_GUIDE.md` - 增加完整的PPPoE参数说明
- `BRIDGE_MODE_API_GUIDE.md` - 增加NAT模式PPPoE配置
- `PPPOE_FIX_SUMMARY.md` - 本修复总结文档

## 总结

通过正确配置PPPoE的网络接口架构，修复了client_mode.lua和bridge_mode.lua中PPPoE拨号连接不成功的问题。新的配置方式符合OpenWRT的网络配置标准，提供了更好的稳定性和兼容性。
