# 需要上传到设备的修复文件清单

## 🚨 紧急修复文件（必须上传）
这些文件有CGI问题，导致ac_handlers无法加载：

1. **upgrade.lua** - ⚠️ 新发现的问题文件
   - 错误：line 26 attempt to concatenate local 'POST' (a nil value)
   - 状态：已修复CGI代码

2. **network_status.lua** - ⚠️ 新发现的问题文件  
   - 错误：顶层执行CGI代码
   - 状态：已修复CGI代码

3. **user_event.lua** - ✅ 已修复
   - 错误：line 81 attempt to concatenate local 'POST' (a nil value)
   - 状态：CGI代码已移到route_api()函数

## 📋 其他已修复文件（建议上传）
如果之前没有上传过，建议一并上传：

4. **info.lua** - ✅ 已修复
5. **status.lua** - ✅ 已修复  
6. **route_table.lua** - ✅ 已修复
7. **ap_mode.lua** - ✅ 已修复
8. **route_mode.lua** - ✅ 已修复
9. **bridge_mode.lua** - ✅ 已修复
10. **vlan.lua** - ✅ 已修复
11. **user_setup.lua** - ✅ 已修复
12. **snmp.lua** - ✅ 已修复
13. **qos_management.lua** - ✅ 已修复
14. **current_device.lua** - ✅ 已修复
15. **scheduled_reboot.lua** - ✅ 已修复

## 🧪 测试工具（可选上传）
16. **generate_test_data.lua** - 完整测试数据生成器
17. **quick_test_generator.lua** - 快速测试数据生成器

## 📝 上传步骤

### 1. 停止当前服务
```bash
killall lua
```

### 2. 上传修复文件
```bash
# 上传到设备的 /www/cgi-bin/3onedata/ 目录
scp upgrade.lua root@设备IP:/www/cgi-bin/3onedata/
scp network_status.lua root@设备IP:/www/cgi-bin/3onedata/
scp user_event.lua root@设备IP:/www/cgi-bin/3onedata/
# ... 其他文件
```

### 3. 重启AC管理服务
```bash
cd /www/cgi-bin/3onedata
lua ac_main_loop.lua &
```

### 4. 验证服务状态
```bash
# 检查进程
ps | grep lua

# 检查端口
netstat -ulnp | grep 50001

# 检查日志
tail -f /tmp/ac_main_loop.log
```

### 5. 预期结果
- ✅ ac_main_loop.lua 成功启动
- ✅ 监听UDP端口50001
- ✅ ac_handlers模块成功加载（不再有错误）
- ✅ 日志显示 "ac_handlers loaded successfully"

## 🔍 测试验证

### 生成测试数据
```bash
cd /www/cgi-bin/3onedata
lua quick_test_generator.lua
```

### 使用网络助手测试
1. 打开PC网络助手
2. 选择UDP协议
3. 目标IP：设备IP地址
4. 目标端口：50001
5. 发送生成的十六进制数据

### 预期响应
- 设备日志显示接收到数据
- ac_handlers处理请求
- 返回相应的JSON响应

## ⚠️ 注意事项
- 必须先上传 upgrade.lua 和 network_status.lua
- 这两个文件是导致ac_handlers加载失败的根本原因
- 修复后应该能看到ac_handlers成功加载的日志
