# Client Mode 部署和测试指南

## 问题分析

根据您的测试结果，问题可能有以下几个方面：

1. **设备上的client_mode.lua文件还是旧版本**，没有包含GET请求支持
2. **文件权限问题**，CGI脚本可能没有执行权限
3. **Lua模块依赖问题**，某些必需的模块可能不可用
4. **CGI环境问题**，Web服务器可能没有正确设置环境变量

## 解决步骤

### 第1步：诊断当前问题

在设备上运行诊断脚本：

```bash
# 进入CGI目录
cd /www/cgi-bin/3onedata

# 运行诊断脚本
chmod +x diagnose_client_mode.sh
./diagnose_client_mode.sh
```

### 第2步：测试最小化版本

首先测试最小化版本，确认基本功能：

```bash
# 复制最小化测试文件到设备
# (假设您已经将文件传输到设备)

# 设置权限
chmod +x client_mode_minimal.lua

# 测试最小化版本
export REQUEST_METHOD="GET"
export QUERY_STRING="action=get_status"
lua client_mode_minimal.lua

# 检查日志
cat /tmp/client_mode_minimal.log
```

### 第3步：备份并更新完整版本

```bash
# 备份原文件
cp client_mode.lua client_mode.lua.backup.$(date +%Y%m%d_%H%M%S)

# 复制新版本
# (将修复后的client_mode.lua复制到设备)

# 设置正确权限
chmod +x client_mode.lua
chown root:root client_mode.lua
```

### 第4步：验证修复

```bash
# 测试GET请求
export REQUEST_METHOD="GET"
export QUERY_STRING="action=get_status"
lua client_mode.lua

# 检查日志
tail -f /tmp/client_mode.log
```

### 第5步：Web测试

使用curl或浏览器测试：

```bash
# 测试GET请求
curl "http://192.168.1.254/cgi-bin/3onedata/client_mode.lua?action=get_status"

# 测试POST请求
curl -X POST "http://192.168.1.254/cgi-bin/3onedata/client_mode.lua" \
  -H "Content-Type: application/json" \
  -d '{
    "module": "client_mode",
    "api": "scan_ssid",
    "param": {
      "band": "5G"
    }
  }'
```

## 常见问题和解决方案

### 问题1：文件不存在或路径错误

**症状**: `cannot open WIFI7_Lua_Step1/client_mode.lua: No such file or directory`

**解决方案**:
```bash
# 确认当前目录
pwd
# 应该在 /www/cgi-bin/3onedata

# 确认文件存在
ls -la client_mode.lua

# 如果不存在，检查是否在其他位置
find /www -name "client_mode.lua"
```

### 问题2：权限不足

**症状**: `Permission denied`

**解决方案**:
```bash
# 设置正确权限
chmod +x client_mode.lua
chown root:root client_mode.lua

# 检查权限
ls -la client_mode.lua
# 应该显示类似: -rwxr-xr-x 1 <USER> <GROUP>
```

### 问题3：Lua模块缺失

**症状**: 脚本执行但没有输出，或者模块加载错误

**解决方案**:
```bash
# 检查Lua模块
lua -e "print(require('cjson.safe'))"
lua -e "print(require('luci.sys'))"
lua -e "print(require('luci.model.uci'))"

# 如果模块缺失，可能需要安装
opkg update
opkg install luci-lib-json
opkg install luci-base
```

### 问题4：CGI环境变量问题

**症状**: Web请求失败，但命令行测试成功

**解决方案**:
```bash
# 检查Web服务器配置
# 确认CGI目录配置正确

# 检查Web服务器日志
tail -f /var/log/httpd/error.log
# 或
tail -f /var/log/nginx/error.log
```

### 问题5：脚本执行超时

**症状**: "Bad Gateway" 或超时错误

**解决方案**:
```bash
# 增加脚本执行超时时间
# 在脚本开头添加：
# io.stdout:setvbuf("no")
# io.stderr:setvbuf("no")

# 或者简化脚本逻辑，减少执行时间
```

## 调试技巧

### 1. 启用详细日志

在client_mode.lua开头添加：
```lua
-- 启用详细调试
local DEBUG = true
local function debug_log(msg)
    if DEBUG then
        write_log("[DEBUG] " .. msg)
    end
end
```

### 2. 检查环境变量

添加环境变量日志：
```lua
write_log("REQUEST_METHOD: " .. (os.getenv("REQUEST_METHOD") or "nil"))
write_log("QUERY_STRING: " .. (os.getenv("QUERY_STRING") or "nil"))
write_log("CONTENT_LENGTH: " .. (os.getenv("CONTENT_LENGTH") or "nil"))
write_log("CONTENT_TYPE: " .. (os.getenv("CONTENT_TYPE") or "nil"))
```

### 3. 简化测试

使用最小化版本逐步测试：
1. 先测试基本的HTTP响应
2. 再测试GET请求解析
3. 最后测试完整功能

## 文件清单

确保以下文件都已正确部署：

```
/www/cgi-bin/3onedata/
├── client_mode.lua              # 主文件（修复后版本）
├── client_mode_minimal.lua      # 最小化测试版本
├── diagnose_client_mode.sh      # 诊断脚本
├── test_client_mode.sh          # 测试脚本
└── client_mode.lua.backup.*     # 备份文件
```

## 成功标志

修复成功后，您应该看到：

1. **命令行测试成功**:
   ```bash
   export REQUEST_METHOD="GET"
   export QUERY_STRING="action=get_status"
   lua client_mode.lua
   # 应该返回JSON格式的响应
   ```

2. **日志文件生成**:
   ```bash
   ls -la /tmp/client_mode.log
   # 文件应该存在并包含日志内容
   ```

3. **Web请求成功**:
   ```bash
   curl "http://192.168.1.254/cgi-bin/3onedata/client_mode.lua?action=get_status"
   # 应该返回JSON响应而不是"Bad Gateway"
   ```

## 联系支持

如果问题仍然存在，请提供：
1. 诊断脚本的完整输出
2. /tmp/client_mode.log的内容
3. Web服务器错误日志
4. 设备的系统信息（OpenWrt版本、Lua版本等）
