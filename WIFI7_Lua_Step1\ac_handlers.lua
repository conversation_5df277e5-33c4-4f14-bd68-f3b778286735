#!/usr/bin/lua

-- Copyright (c) 2013 The Linux Foundation. All rights reserved.
-- Not a Contribution.
-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

-- Lua环境：此脚本在OpenWRT的Lua环境中运行，需要ac_config_manager, ac_status, ac_protocol, ac_comm_wrap, cjson.safe。
-- 功能：包含处理各种AC命令（配置下发、查询请求、升级指令等）的具体逻辑。

-- 引入模块
local cjson = require("cjson.safe")
local ac_config_manager = require("ac_config_manager")
local ac_status = require("ac_status")
-- 加载C编译的协议库
local ac_protocol = require("ac_protocol")
local ac_comm = require("ac_comm")
local log = require("ac_log")
local wifi24 = require("wifi_2_4g")
local wifi5g = require("wifi_5g")
local scheduled_reboot = require("scheduled_reboot")
local ap_mode = require("ap_mode")
local route_mode = require("route_mode")
local bridge_mode = require("bridge_mode")
local vlan = require("vlan")
local user_setup = require("user_setup")
local user_event = require("user_event")
local qos_management = require("qos_management")
local current_device = require("current_device")
local mac_access_control = require("mac_access_control")
local snmp = require("snmp")
local network_status = require("network_status")
local upgrade = require("upgrade")

-- 前向声明函数
local handle_set_params
local handle_query_request
local send_response_to_ac

if not ac_status then
    log.error("Failed to require ac_status!")
    os.exit(1)
end
if not ac_protocol then
    log.error("Failed to require ac_protocol!")
    os.exit(1)
end

local log_file = "/tmp/ac_handlers.log" -- 日志文件路径

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    end
end

-- 命令注册表
local CMD_HANDLERS = {
    -- 配置下发命令 (data_type)
    [1001] = {
        handler = function(header, payload)
            return handle_ac_wifi_config(header, payload)
        end,
        desc = "WiFi configuration set (2.4G/5G)"
    },
    [1002] = {
        handler = function(header, payload)
            return handle_ac_wifi_query(header, payload)
        end,
        desc = "WiFi configuration query (2.4G/5G)"
    },
    [3001] = {
        handler = function(header, payload)
            return handle_set_params(header, payload, "scheduled_reboot")
        end,
        desc = "Scheduled reboot set"
    },
    [3002] = {
        handler = function(header, payload)
            return handle_query_request(header, { query_type = "scheduled_reboot", sid = payload.sid })
        end,
        desc = "Scheduled reboot query"
    },
    -- 模式管理命令
    [4001] = {
        handler = function(header, payload)
            return handle_set_params(header, payload, "ap_mode")
        end,
        desc = "AP mode configuration set"
    },
    [4002] = {
        handler = function(header, payload)
            return handle_query_request(header, { query_type = "ap_mode", sid = payload.sid })
        end,
        desc = "AP mode configuration query"
    },
    [4003] = {
        handler = function(header, payload)
            return handle_set_params(header, payload, "route_mode")
        end,
        desc = "Route mode configuration set"
    },
    [4004] = {
        handler = function(header, payload)
            return handle_query_request(header, { query_type = "route_mode", sid = payload.sid })
        end,
        desc = "Route mode configuration query"
    },
    [4005] = {
        handler = function(header, payload)
            return handle_set_params(header, payload, "bridge_mode")
        end,
        desc = "Bridge mode configuration set"
    },
    [4006] = {
        handler = function(header, payload)
            return handle_query_request(header, { query_type = "bridge_mode", sid = payload.sid })
        end,
        desc = "Bridge mode configuration query"
    },
    -- VLAN配置命令
    [5001] = {
        handler = function(header, payload)
            return handle_set_params(header, payload, "vlan")
        end,
        desc = "VLAN configuration set"
    },
    [5002] = {
        handler = function(header, payload)
            return handle_query_request(header, { query_type = "vlan", sid = payload.sid })
        end,
        desc = "VLAN configuration query"
    },
    -- 用户设置命令
    [6001] = {
        handler = function(header, payload)
            return handle_set_params(header, payload, "user_setup")
        end,
        desc = "User setup configuration set"
    },
    [6002] = {
        handler = function(header, payload)
            return handle_query_request(header, { query_type = "user_setup", sid = payload.sid })
        end,
        desc = "User setup configuration query"
    },
    -- 用户事件命令
    [6003] = {
        handler = function(header, payload)
            return handle_set_params(header, payload, "user_event")
        end,
        desc = "User event configuration set"
    },
    [6004] = {
        handler = function(header, payload)
            return handle_query_request(header, { query_type = "user_event", sid = payload.sid })
        end,
        desc = "User event configuration query"
    },
    -- QOS管理命令
    [7001] = {
        handler = function(header, payload)
            return handle_set_params(header, payload, "qos_management")
        end,
        desc = "QOS management configuration set"
    },
    [7002] = {
        handler = function(header, payload)
            return handle_query_request(header, { query_type = "qos_management", sid = payload.sid })
        end,
        desc = "QOS management configuration query"
    },
    -- 无线过滤命令
    [8001] = {
        handler = function(header, payload)
            return handle_set_params(header, payload, "mac_access_control")
        end,
        desc = "MAC access control configuration set"
    },
    [8002] = {
        handler = function(header, payload)
            return handle_query_request(header, { query_type = "mac_access_control", sid = payload.sid })
        end,
        desc = "MAC access control configuration query"
    },
    -- 当前设备查询命令
    [8003] = {
        handler = function(header, payload)
            return handle_query_request(header, { query_type = "current_device", sid = payload.sid })
        end,
        desc = "Current device list query"
    },
    -- SNMP管理命令
    [9001] = {
        handler = function(header, payload)
            return handle_set_params(header, payload, "snmp")
        end,
        desc = "SNMP configuration set"
    },
    [9002] = {
        handler = function(header, payload)
            return handle_query_request(header, { query_type = "snmp", sid = payload.sid })
        end,
        desc = "SNMP configuration query"
    },
    -- 网络状态查询命令
    [10001] = {
        handler = function(header, payload)
            return handle_query_request(header, { query_type = "network_status", sid = payload.sid })
        end,
        desc = "Network status query"
    },
    -- 固件升级命令
    [11001] = {
        handler = function(header, payload)
            return handle_set_params(header, payload, "upgrade")
        end,
        desc = "Firmware upgrade set"
    },
    [11002] = {
        handler = function(header, payload)
            return handle_query_request(header, { query_type = "upgrade", sid = payload.sid })
        end,
        desc = "Firmware upgrade status query"
    },
    -- 固件升级
    [1003] = {
        handler = function(header, payload)
            return handle_upgrade_command(header, payload)
        end,
        desc = "Firmware upgrade"
    },
    -- 心跳响应
    [2001] = {
        handler = function(header, payload)
            return handle_heartbeat_response(header, payload)
        end,
        desc = "Heartbeat response"
    },
    -- 状态查询 (通过 query_type)
    query_types = {
        system_status = {
            handler = function(header, payload)
                return ac_status.get_system_info(), 0
            end,
            desc = "System status query"
        },
        network_status = {
            handler = function(header, payload)
                return ac_status.get_network_status(), 0
            end,
            desc = "Network status query"
        },
        wifi_status = {
            handler = function(header, payload)
                return ac_status.get_wifi_info(), 0
            end,
            desc = "WiFi status query"
        },
        all_status = {
            handler = function(header, payload)
                return ac_status.get_all_status(), 0
            end,
            desc = "All status query"
        },
        firmware_info = {
            handler = function(header, payload)
                return ac_status.get_firmware_info(), 0
            end,
            desc = "Firmware info query"
        }
    }
}

-- 辅助函数：发送响应给 AC
local function send_ac_response(header, response_payload_table, errcode, source_ip, source_port)
    write_log("Preparing AC response...")
    local response_header = {
        data_type = header.data_type or 0,
        dev_type = header.dev_type or 0,
        errcode = errcode,
        session = header.session or 0,
        proto_version = header.proto_version or 1,
        mac_addr = header.dev_mac or "",
        hash_code = header.hashcode or ""
    }

    local response_payload_json = cjson.encode(response_payload_table)
    local packed_data = ac_protocol.pack_udp_data(response_header, response_payload_json)
    if not packed_data then
        write_log("Failed to pack response data.")
        return false, "Failed to pack response."
    end

    local encrypted_data = packed_data  -- 默认不加密
    if ac_protocol.encrypt then
        encrypted_data = ac_protocol.encrypt(packed_data)
        if not encrypted_data then
            write_log("Failed to encrypt response data, using unencrypted data.")
            encrypted_data = packed_data
        end
    else
        write_log("encrypt function not available, using unencrypted data.")
    end

    local target_ip = source_ip or config.ac_ip or "*************"
    local target_port = source_port or config.ac_port or 50000
    write_log("Sending response to " .. target_ip .. ":" .. target_port)
    local success, msg = ac_comm.send_udp_broadcast(encrypted_data, target_ip, target_port)
    if not success then
        write_log("Failed to send response to AC: " .. msg)
        return false, msg
    end
    write_log("Response sent successfully to AC.")
    return true
end

-- 处理配置下发
local function handle_set_params(header, payload, config_type)
    write_log("Handling SET PARAMS command for " .. config_type .. "...")
    local ok, msg
    if config_type == "scheduled_reboot" then
        ok, msg = pcall(function()
            return scheduled_reboot.set_config_from_ac(payload)
        end)
    elseif config_type == "ap_mode" then
        ok, msg = pcall(function()
            return ap_mode.set_config_from_ac(payload)
        end)
    elseif config_type == "route_mode" then
        ok, msg = pcall(function()
            return route_mode.set_config_from_ac(payload)
        end)
    elseif config_type == "bridge_mode" then
        ok, msg = pcall(function()
            return bridge_mode.set_config_from_ac(payload)
        end)
    elseif config_type == "vlan" then
        ok, msg = pcall(function()
            return vlan.set_config_from_ac(payload)
        end)
    elseif config_type == "user_setup" then
        ok, msg = pcall(function()
            return user_setup.set_config_from_ac(payload)
        end)
    elseif config_type == "user_event" then
        ok, msg = pcall(function()
            return user_event.set_config_from_ac(payload)
        end)
    elseif config_type == "qos_management" then
        ok, msg = pcall(function()
            return qos_management.set_config_from_ac(payload)
        end)
    elseif config_type == "mac_access_control" then
        ok, msg = pcall(function()
            return mac_access_control.set_config_from_ac(payload)
        end)
    elseif config_type == "snmp" then
        ok, msg = pcall(function()
            return snmp.set_config_from_ac(payload)
        end)
    elseif config_type == "upgrade" then
        ok, msg = pcall(function()
            return upgrade.set_config_from_ac(payload)
        end)
    else
        local param_json_str = cjson.encode(payload)
        ok, msg = ac_config_manager.save(param_json_str)
    end

    if ok then
        write_log("Configuration saved successfully: " .. (msg.message or msg))
        return { status = "ok", message = msg.message or "Configuration applied." }, 0
    else
        write_log("Failed to save configuration: " .. tostring(msg))
        return { status = "error", message = tostring(msg) }, 1
    end
end

-- 处理查询请求
local function handle_query_request(header, payload)
    local query_type = payload.query_type
    write_log("Handling QUERY REQUEST for " .. (query_type or "nil") .. "...")
    
    local handler = CMD_HANDLERS.query_types[query_type]
    if handler then
        local response_data, errcode = handler.handler(header, payload)
        return response_data, errcode
    elseif query_type == "scheduled_reboot" then
        local ok, msg = pcall(function()
            return scheduled_reboot.get_config_for_ac(payload)
        end)
        return msg or { message = "Failed to retrieve scheduled reboot settings." }, ok and 0 or 1
    elseif query_type == "ap_mode" then
        local ok, msg = pcall(function()
            return ap_mode.get_config_for_ac(payload)
        end)
        return msg or { message = "Failed to retrieve AP mode settings." }, ok and 0 or 1
    elseif query_type == "route_mode" then
        local ok, msg = pcall(function()
            return route_mode.get_config_for_ac(payload)
        end)
        return msg or { message = "Failed to retrieve Route mode settings." }, ok and 0 or 1
    elseif query_type == "bridge_mode" then
        local ok, msg = pcall(function()
            return bridge_mode.get_config_for_ac(payload)
        end)
        return msg or { message = "Failed to retrieve Bridge mode settings." }, ok and 0 or 1
    elseif query_type == "vlan" then
        local ok, msg = pcall(function()
            return vlan.get_config_for_ac(payload)
        end)
        return msg or { message = "Failed to retrieve VLAN settings." }, ok and 0 or 1
    elseif query_type == "user_setup" then
        local ok, msg = pcall(function()
            return user_setup.get_config_for_ac(payload)
        end)
        return msg or { message = "Failed to retrieve user setup settings." }, ok and 0 or 1
    elseif query_type == "user_event" then
        local ok, msg = pcall(function()
            return user_event.get_config_for_ac(payload)
        end)
        return msg or { message = "Failed to retrieve user event settings." }, ok and 0 or 1
    elseif query_type == "qos_management" then
        local ok, msg = pcall(function()
            return qos_management.get_config_for_ac(payload)
        end)
        return msg or { message = "Failed to retrieve QOS management settings." }, ok and 0 or 1
    elseif query_type == "mac_access_control" then
        local ok, msg = pcall(function()
            return mac_access_control.get_config_for_ac(payload)
        end)
        return msg or { message = "Failed to retrieve MAC access control settings." }, ok and 0 or 1
    elseif query_type == "current_device" then
        local ok, msg = pcall(function()
            return current_device.get_config_for_ac(payload)
        end)
        return msg or { message = "Failed to retrieve current device list." }, ok and 0 or 1
    elseif query_type == "snmp" then
        local ok, msg = pcall(function()
            return snmp.get_config_for_ac(payload)
        end)
        return msg or { message = "Failed to retrieve SNMP settings." }, ok and 0 or 1
    elseif query_type == "network_status" then
        local ok, msg = pcall(function()
            return network_status.get_config_for_ac(payload)
        end)
        return msg or { message = "Failed to retrieve network status." }, ok and 0 or 1
    elseif query_type == "upgrade" then
        local ok, msg = pcall(function()
            return upgrade.get_config_for_ac(payload)
        end)
        return msg or { message = "Failed to retrieve upgrade status." }, ok and 0 or 1
    else
        write_log("Unknown query type: " .. (query_type or "nil"))
        return { message = "Unknown query type." }, 1
    end
end

-- 处理 WiFi 配置
local function handle_ac_wifi_config(header, payload)
    local band = payload.band or "2.4G"
    write_log("Handling WiFi config for band: " .. band)
    local wifi_module = (band == "5G") and wifi5g or wifi24
    if not wifi_module or type(wifi_module.set_config_from_ac) ~= "function" then
        write_log("[ERROR] set_config_from_ac not found for band " .. band)
        return { status = "error", message = "set_config_from_ac not found for band " .. band }, 1
    end
    local ok, msg = wifi_module.set_config_from_ac(payload)
    return { status = ok and "ok" or "error", message = msg }, ok and 0 or 1
end

-- 处理 WiFi 查询
local function handle_ac_wifi_query(header, payload)
    local band = payload.band or "2.4G"
    write_log("Handling WiFi query for band: " .. band)
    local wifi_module = (band == "5G") and wifi5g or wifi24
    if not wifi_module or type(wifi_module.get_config_for_ac) ~= "function" then
        write_log("[ERROR] get_config_for_ac not found for band " .. band)
        return { status = "error", message = "get_config_for_ac not found for band " .. band }, 1
    end
    local cfg = wifi_module.get_config_for_ac(payload)
    return cfg, 0
end

-- 处理固件升级
local function handle_upgrade_command(header, payload)
    write_log("Handling UPGRADE COMMAND...")
    local firmware_url = payload.url
    local firmware_md5 = payload.md5

    if not firmware_url then
        write_log("Missing firmware URL for upgrade.")
        return { status = "error", message = "Missing firmware URL." }, 1
    end

    write_log("Starting firmware download from: " .. firmware_url)
    ac_status.report_upgrade_status(10, "Downloading firmware...")
    write_log("Firmware downloaded (simulated).")
    ac_status.report_upgrade_status(50, "Verifying firmware...")
    write_log("Firmware verified (simulated). Starting upgrade...")
    ac_status.report_upgrade_status(70, "Starting upgrade...")
    write_log("Upgrade command issued (simulated). AP will reboot.")
    return { status = "ok", message = "Firmware upgrade initiated. AP will reboot." }, 0
end

-- 处理心跳响应
local function handle_heartbeat_response(header, payload)
    write_log("Handling HEARTBEAT RESPONSE...")
    write_log("Heartbeat response received: " .. cjson.encode(payload))
    return { status = "ok", message = "Heartbeat received." }, 0
end

-- 安全的 JSON 解码
local function safe_json_decode(json_str)
    if not json_str or json_str == "" then
        write_log("safe_json_decode: Empty or nil JSON string")
        return {}, nil
    end
    write_log("safe_json_decode: Input JSON: " .. json_str)
    local ok, res = pcall(cjson.decode, json_str)
    if not ok then
        write_log("safe_json_decode: Failed to decode JSON: " .. tostring(res))
        return nil, tostring(res) or "JSON decode error"
    end
    write_log("safe_json_decode: Successfully decoded JSON: " .. cjson.encode(res))
    return res, nil
end

-- 初始化模块表
local M = {}

-- 读取配置
local config = ac_config_manager.read() or {}

-- 中央消息分发器
function M.handle_packet(raw_data, source_ip, source_port)
    log.info("handle_packet: Received " .. #raw_data .. " bytes from " .. tostring(source_ip) .. ":" .. tostring(source_port))
    log.info("Raw packet hex: " .. (raw_data:gsub('.', function(c) return string.format('%02X ', string.byte(c)) end)))

    local header, payload_str = ac_protocol.unpack_udp_data(raw_data)
    if not header then
        log.error("handle_packet: Failed to unpack udp packet: " .. tostring(payload_str))
        send_ac_response({ data_type = 0, dev_type = 0, session = 0, proto_version = 1, mac_addr = "", hash_code = "" }, 
                        { status = "error", message = "Failed to unpack UDP packet: " .. tostring(payload_str) }, 10, source_ip, source_port)
        return
    end
    log.info("[DEBUG] header: " .. cjson.encode(header))
    log.info("[DEBUG] payload_str: " .. tostring(payload_str))

    local payload, err = safe_json_decode(payload_str)
    if not payload then
        log.error("handle_packet: JSON decode failed: " .. tostring(err))
        send_ac_response(header, { status = "error", message = "Invalid JSON payload: " .. tostring(err) }, 10, source_ip, source_port)
        return
    end

    -- 统一分发逻辑
    local handler = CMD_HANDLERS[header.data_type]
    if handler then
        write_log("Dispatching to handler for data_type: " .. header.data_type .. " (" .. handler.desc .. ")")
        local response_data, errcode = handler.handler(header, payload)
        send_ac_response(header, response_data, errcode, source_ip, source_port)
    elseif payload.query_type and CMD_HANDLERS.query_types[payload.query_type] then
        write_log("Dispatching to query handler for query_type: " .. payload.query_type)
        local response_data, errcode = CMD_HANDLERS.query_types[payload.query_type].handler(header, payload)
        send_ac_response(header, response_data, errcode, source_ip, source_port)
    else
        write_log("Unknown or unhandled data type: " .. tostring(header.data_type) .. ", query_type: " .. tostring(payload.query_type))
        send_ac_response(header, { status = "error", message = "Unknown command or query type." }, 99, source_ip, source_port)
    end
end

-- 导出模块
return M
