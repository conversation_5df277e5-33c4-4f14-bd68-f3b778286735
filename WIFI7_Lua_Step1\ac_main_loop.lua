#!/usr/bin/lua

-- Copyright (c) 2013 The Linux Foundation. All rights reserved.
-- Not a Contribution.

-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

-- Lua环境：此脚本在OpenWRT的Lua环境中运行，需要ac_comm, ac_protocol, cjson.safe。
-- 功能：作为AC适配功能的主控，管理通信循环，接收消息，进行协议处理，并分发命令。

-- 引入模块
local cjson = require("cjson.safe")
local socket = require("socket") -- 用于 socket.sleep
local ac_comm = require("ac_comm_wrap") -- 引入网络通信模块
local ac_protocol = require('ac_protocol') -- 引入协议处理模块
local log = require("ac_log")
local config_manager = require("ac_config_manager")
local ac_discovery = require("ac_discovery")

-- 安全加载 ac_handlers
local ac_handlers = nil
local handlers_ok, handlers_err = pcall(function()
    ac_handlers = require("ac_handlers")
end)

if not handlers_ok then
    log.error("Failed to load ac_handlers: " .. tostring(handlers_err))
    print("Error loading ac_handlers:", handlers_err)
    print("Continuing without ac_handlers...")
    ac_handlers = nil
end

local log_file = "/tmp/ac_main_loop.log" -- 日志文件路径

local M = {}

-- Main function to run the agent
function M.run()
    log.info("==============================================")
    log.info("     Starting AC Management Agent...          ")
    log.info("==============================================")

    -- 1. Load configuration
    local config, err = config_manager.read()
    if not config then
        log.error("Main loop: Could not start. Failed to read config:", err)
        return
    end

    if not config.enable or config.enable ~= 1 then
        log.info("Main loop: AC Management is disabled in the configuration. Exiting.")
        return
    end
    
    log.info("Main loop: Configuration loaded successfully.")
    
    -- 2. Initialize communication
    local comm_ok, comm_err = ac_comm.init({ local_port = config.ap_port })
    if not comm_ok then
        log.error("Main loop: Failed to initialize communication:", comm_err)
        return
    end

    log.info("Main loop: Agent is up and listening on port", config.ap_port)
    
    -- Example: Send a startup notification to the AC
    -- This is a good place to inform the AC that the AP is online.
    -- We can invent a "type" for this, e.g., type=4, subtype=1 for "AP_ONLINE"
    
    local last_heartbeat_time = socket.gettime()
    local heartbeat_interval = config.heartbeat_interval or 30 -- seconds

    -- 3. Start the main loop
    local function handle_packet_and_log(data, ip, port)
        -- 打印明文和HEX
        log.info(string.format("[main_loop] Received %d bytes from %s:%s", #data, tostring(ip), tostring(port)))
        log.info("[main_loop] HEX: " .. (data:gsub('.', function(c) return string.format('%02X ', string.byte(c)) end)))
        -- 协议解析和处理
        local ok, err = pcall(function()
            -- 优先交给ac_discovery处理自动发现包
            if not ac_discovery.handle_packet(data, ip, port) then
                if ac_handlers then
                    ac_handlers.handle_packet(data, ip, port)
                else
                    log.warn("[main_loop] ac_handlers not available, sending simple ACK")
                    print("  -> ac_handlers not available, sending ACK")
                    -- 简单回复确认包
                    local reply = "ACK"
                    ac_comm.send(reply, ip, port)
                end
            end
        end)
        if not ok then
            log.error("[main_loop] Handler error: " .. tostring(err))
        end
    end

    -- 持续监听并处理
    ac_comm.listen_and_handle(1, handle_packet_and_log)

    log.info("Main loop: Agent is shutting down.")
    ac_comm.close()
end

-- Run the agent
M.run()

-- 如果此文件作为独立脚本运行，则启动主循环
-- local status, msg = start_ac_adapter()
-- if not status then
--     write_log("AC adapter failed to start: " .. msg)
-- end

-- 导出函数，以便其他脚本可以调用启动主循环
return M
