#!/usr/bin/lua

--实际情况：在 OpenWrt 中，uhttpd 是默认的轻量级 Web 服务器，支持 HTTP 和 HTTPS（如果配置了证书）。它通过 UCI 配置（通常是 /etc/config/uhttpd）来管理监听端口和协议。Nginx 不是 OpenWrt 的默认组件，除非手动安装并配置了它。因此，如果当前设备只使用 uhttpd，理论上不需要 Nginx 也能实现 HTTP 和 HTTPS 的切换。
--1. HTTP/HTTPS 配置的问题
--现状：代码中既有对 uhttpd 的 UCI 配置（如 uci:set("httpd", "main", "listen_http", ...)），又有对 Nginx 配置文件的直接修改。这可能会导致配置冲突。
--建议：
--如果设备只使用 uhttpd（OpenWrt 默认），移除 Nginx 相关的代码，直接通过 UCI 配置管理 HTTP 和 HTTPS。
--如果设备依赖 Nginx，移除 uhttpd 的配置逻辑，专注于 Nginx 的配置文件修改。
--为简单起见，当前只使用 uhttpd（更符合 OpenWrt 标准环境），并优化代码。
--2. 服务重启逻辑优化
--当前使用 os.execute("nohup /etc/init.d/xxx restart &") 重启服务，这种方式不够可靠，可能导致进程残留或重启失败。
--建议：使用 LuCI 的 sys 模块提供的 exec 或直接调用服务脚本的 stop 和 start，确保服务状态一致。

-- 引入模块
local cjson = require("cjson.safe")
local sys = require("luci.sys")
local uci = require("luci.model.uci").cursor()
local log_file = "/tmp/access_settings.log"
local remote_config_file_path = "/etc/remoteaccess" -- New global variable for config file path

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end

-- 获取当前应用模式的辅助函数
local function get_current_apply_mode()
    local mode_file = "/etc/config_apply_mode_status"
    local f = io.open(mode_file, "r")
    if f then
        local mode = f:read("*l") -- Read the first line
        f:close()
        mode = mode and mode:gsub("^%s*(.-)%s*$", "%1") -- Trim whitespace
        if mode == "immediate" or mode == "deferred" then
            write_log("Read apply_mode from " .. mode_file .. ": " .. mode)
            return mode
        else
            write_log("Invalid content in " .. mode_file .. ": '" .. (mode or "nil") .. "'. Defaulting to 'immediate'.")
            return "immediate"
        end
    else
        write_log("Could not open " .. mode_file .. ". Defaulting to 'immediate'.")
        return "immediate"
    end
end

-- 确保 telnetd 配置文件存在 (existing function)
local function ensure_telnetd_config()
    local config_path = "/etc/config/telnetd"
    local file = io.open(config_path, "r")
    if not file then
        local f = io.open(config_path, "w")
        if f then
            f:write("config telnetd 'main'\n")
            f:write("    option enable '0'\n")
            f:write("    option port '23'\n")
            f:close()
            write_log("Created default telnetd config.")
        end
    else
        file:close()
    end
end

-- [REVISED] 确保远程访问配置文件存在 (直接读写文件，不再使用UCI)
local function ensure_remoteaccess_config()
    local file = io.open(remote_config_file_path, "r")
    if not file then
        write_log("Remote access config file '" .. remote_config_file_path .. "' not found. Creating with defaults.")
        local defaults = {
            http_wan_enabled = "0", http_wan_port = "8080",
            https_wan_enabled = "0", https_wan_port = "8443",
            ssh_wan_enabled = "0", ssh_wan_port = "2222",
            telnet_wan_enabled = "0", telnet_wan_port = "2323"
        }
        local new_file_content = cjson.encode(defaults)
        local f_write = io.open(remote_config_file_path, "w")
        if f_write then
            f_write:write(new_file_content)
            f_write:close()
            write_log("Created default remote access config file: " .. remote_config_file_path)
        else
            write_log("ERROR: Failed to create remote access config file: " .. remote_config_file_path .. ". Check permissions.")
        end
    else
        -- File exists, try to parse it. If it's not valid JSON, overwrite with defaults.
        local content = file:read("*a")
        file:close()
        local ok, decoded_data = pcall(cjson.decode, content)
        if not ok or type(decoded_data) ~= "table" then
            write_log("Remote access config file '" .. remote_config_file_path .. "' is corrupted. Recreating with defaults.")
            local defaults = {
                http_wan_enabled = "0", http_wan_port = "8080",
                https_wan_enabled = "0", https_wan_port = "8443",
                ssh_wan_enabled = "0", ssh_wan_port = "2222",
                telnet_wan_enabled = "0", telnet_wan_port = "2323"
            }
            local new_file_content = cjson.encode(defaults)
            local f_write = io.open(remote_config_file_path, "w")
            if f_write then
                f_write:write(new_file_content)
                f_write:close()
                write_log("Recreated default remote access config file due to corruption: " .. remote_config_file_path)
            else
                write_log("ERROR: Failed to recreate remote access config file: " .. remote_config_file_path .. ". Check permissions.")
            end
        else
            -- Ensure all default keys exist
            local defaults_template = {
                http_wan_enabled = "0", http_wan_port = "8080",
                https_wan_enabled = "0", https_wan_port = "8443",
                ssh_wan_enabled = "0", ssh_wan_port = "2222",
                telnet_wan_enabled = "0", telnet_wan_port = "2323"
            }
            local changed_existing = false
            for key, default_value in pairs(defaults_template) do
                if decoded_data[key] == nil then
                    decoded_data[key] = default_value
                    changed_existing = true
                    write_log("Added missing option '" .. key .. "' to remote access config.")
                end
            end
            if changed_existing then
                local updated_content = cjson.encode(decoded_data)
                local f_write_update = io.open(remote_config_file_path, "w")
                if f_write_update then
                    f_write_update:write(updated_content)
                    f_write_update:close()
                    write_log("Updated remote access config file with missing default options.")
                else
                    write_log("ERROR: Failed to update remote access config file: " .. remote_config_file_path .. ". Check permissions.")
                end
            else
                 write_log("Remote access config file '" .. remote_config_file_path .. "' is valid and contains all keys.")
            end
        end
    end
end

-- Helper function to read remote access settings from file
local function get_remote_settings_from_file()
    ensure_remoteaccess_config() -- Ensure file exists and has defaults if needed
    local file = io.open(remote_config_file_path, "r")
    if not file then
        write_log("ERROR: Could not open remote access config file for reading: " .. remote_config_file_path)
        return {}
    end
    local content = file:read("*a")
    file:close()
    local ok, data = pcall(cjson.decode, content)
    if not ok or type(data) ~= "table" then
        write_log("ERROR: Failed to decode remote access config file or not a table: " .. remote_config_file_path .. ". Returning empty settings.")
        -- Optionally, could try to recreate defaults here again if critical
        return {}
    end
    return data
end

-- Helper function to write remote access settings to file
local function write_remote_settings_to_file(settings_table)
    local content = cjson.encode(settings_table)
    local file = io.open(remote_config_file_path, "w")
    if file then
        file:write(content)
        file:close()
        write_log("Successfully wrote settings to remote access config file: " .. remote_config_file_path)
        return true
    else
        write_log("ERROR: Failed to write settings to remote access config file: " .. remote_config_file_path .. ". Check permissions.")
        return false
    end
end

-- 兼容性强的 kill telnetd
local function kill_telnetd()
    os.execute("killall telnetd 2>/dev/null || (ps | grep '[t]elnetd' | awk '{print $1}' | xargs kill -9 2>/dev/null)")
end


-- 设置 HTTP 响应头
io.write("Content-type: application/json\nPragma: no-cache\n\n")

-- 获取 POST 数据
local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
local POST = POSTLength > 0 and io.read(POSTLength) or ""

if not POST or POST == "" then
    local error_message = "Failed to retrieve POST data"
    write_log(error_message)
    io.write(cjson.encode({
        module = "access_settings",
        version = "1.0",
        errcode = 1,
        result = { message = error_message }
    }))
    io.flush() 
    return
end

write_log("Received POST data: " .. POST)

-- 解析 JSON 数据
local requestData, decode_err = cjson.decode(POST)
if not requestData then
    local error_message = "Invalid JSON input: " .. (decode_err or "unknown error")
    write_log(error_message)
    io.write(cjson.encode({
        module = "access_settings",
        version = "1.0",
        errcode = 2,
        result = { message = error_message }
    }))
    io.flush()
    return
end

write_log("Parsed request data: " .. cjson.encode(requestData))

-- 检查请求格式
if not requestData.version or not requestData.sid or not requestData.module or not requestData.api then
    local error_message = "Invalid request format"
    write_log(error_message)
    io.write(cjson.encode({
        module = "access_settings",
        version = "1.0",
        errcode = 3,
        result = { message = error_message }
    }))
    io.flush()
    return
end

-- 声明函数以便在 route_api 中调用
local set_access_settings
local get_access_settings

-- 路由到具体逻辑
local function route_api()
    if requestData.api == "set" then
        write_log("Calling set_access_settings with data: " .. cjson.encode(requestData))
        set_access_settings(requestData)
    elseif requestData.api == "get" then
        write_log("Calling get_access_settings with data: " .. cjson.encode(requestData))
        get_access_settings(requestData)
    else
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "access_settings",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
        io.flush()
    end
end

-- [MODIFIED] 设置访问设置
function set_access_settings(data)
    ensure_telnetd_config()
    -- ensure_remoteaccess_config() is called by get_remote_settings_from_file and when writing

    local protocols_param = data.param and data.param.protocols or {}
    local need_restart_uhttpd = false
    local need_restart_dropbear = false
    local uci_changed_uhttpd = false
    local uci_changed_dropbear = false
    local uci_changed_telnetd = false
    -- local uci_changed_remoteaccess = false -- No longer using UCI for remoteaccess

    local current_remote_settings = get_remote_settings_from_file()
    local remote_settings_changed = false

    -- Helper to check if a value was changed before setting (for current_remote_settings)
    local function remote_set_if_changed(key, value)
        if tostring(current_remote_settings[key] or "") ~= tostring(value or "") then
            current_remote_settings[key] = value
            remote_settings_changed = true
            write_log("REMOTE_CONFIG SET: " .. key .. " = " .. cjson.encode(value) .. " (was: " .. cjson.encode(current_remote_settings[key] or nil) .. ")")
            return true
        end
        return false
    end

    -- [REVISED] Helper to check if a value was changed before setting, handles strings and tables (for UCI)
    local function uci_set_if_changed(config, section, option, value)
        local current_value = uci:get(config, section, option)
        local changed_flag = false

        if type(value) == "table" then
            if type(current_value) ~= "table" or #current_value ~= #value then
                changed_flag = true
            else
                for i = 1, #value do
                    if current_value[i] ~= value[i] then
                        changed_flag = true
                        break
                    end
                end
            end
        else -- Simple type (string, number, boolean)
            if tostring(current_value or "") ~= tostring(value or "") then -- Ensure comparison handles nil
                changed_flag = true
            end
        end

        if changed_flag then
            uci:set(config, section, option, value)
            if config == "remoteaccess" then uci_changed_remoteaccess = true end
            write_log("UCI SET: " .. config .. "." .. section .. "." .. option .. " = " .. cjson.encode(value) .. " (was: " .. cjson.encode(current_value) .. ")")
            return true -- changed
        end
        return false -- not changed
    end
    
    -- [REVISED] Helper to delete an option if it exists
    local function uci_delete_if_exists(config, section, option)
        local current_value = uci:get(config, section, option)
        if current_value ~= nil then -- Check if option exists
            uci:delete(config, section, option)
            if config == "remoteaccess" then uci_changed_remoteaccess = true end -- This flag might not be strictly needed here if only deleting from main configs
            write_log("UCI DELETE: " .. config .. "." .. section .. "." .. option .. " (was: " .. cjson.encode(current_value) .. ")")
            return true
        end
        return false
    end

    -- 1. Update WAN settings in the remote access config file
    -- No longer using service_map_wan for combined input
    
    local http_settings = protocols_param.http or {}
    local https_settings = protocols_param.https or {}
    local ssh_settings = protocols_param.ssh or {}
    local telnet_settings = protocols_param.telnet or {}

    -- Process HTTP WAN settings
    remote_set_if_changed("http_wan_enabled", http_settings.enabled_wan == "1" and "1" or "0")
    remote_set_if_changed("http_wan_port", http_settings.remote_port_wan or "8080")
    write_log("Processed HTTP WAN (to be saved in " .. remote_config_file_path .. "): enabled=" .. (http_settings.enabled_wan or "nil") .. ", port=" .. (http_settings.remote_port_wan or "nil"))

    -- Process HTTPS WAN settings
    remote_set_if_changed("https_wan_enabled", https_settings.enabled_wan == "1" and "1" or "0")
    remote_set_if_changed("https_wan_port", https_settings.remote_port_wan or "8443")
    write_log("Processed HTTPS WAN (to be saved in " .. remote_config_file_path .. "): enabled=" .. (https_settings.enabled_wan or "nil") .. ", port=" .. (https_settings.remote_port_wan or "nil"))

    -- Process SSH WAN settings
    remote_set_if_changed("ssh_wan_enabled", ssh_settings.enabled_wan == "1" and "1" or "0")
    remote_set_if_changed("ssh_wan_port", ssh_settings.remote_port_wan or "2222")
    write_log("Processed SSH WAN (to be saved in " .. remote_config_file_path .. "): enabled=" .. (ssh_settings.enabled_wan or "nil") .. ", port=" .. (ssh_settings.remote_port_wan or "nil"))

    -- Process Telnet WAN settings
    remote_set_if_changed("telnet_wan_enabled", telnet_settings.enabled_wan == "1" and "1" or "0")
    remote_set_if_changed("telnet_wan_port", telnet_settings.remote_port_wan or "2323")
    write_log("Processed Telnet WAN (to be saved in " .. remote_config_file_path .. "): enabled=" .. (telnet_settings.enabled_wan or "nil") .. ", port=" .. (telnet_settings.remote_port_wan or "nil"))

    if remote_settings_changed then
        write_remote_settings_to_file(current_remote_settings)
    end
    
    -- Retrieve potentially updated WAN settings for immediate use
    local wan_settings = get_remote_settings_from_file() 

    -- 2. Apply effective settings (LAN and WAN co-exist, no precedence)
    -- HTTP
    -- Removed the 'protocols_param.http_wan' check as it's merged
    if protocols_param.http then
        local http_lan_enabled = (http_settings.enabled == "1")
        local http_lan_port = http_settings.remote_port or "80"
        local http_wan_enabled = (wan_settings.http_wan_enabled == "1")
        local http_wan_port = wan_settings.http_wan_port or "8080"

        local listen_http_ports = {}
        if http_lan_enabled then
            table.insert(listen_http_ports, "0.0.0.0:" .. http_lan_port)
            table.insert(listen_http_ports, "[::]:" .. http_lan_port)
            write_log("HTTP: LAN access enabled on port " .. http_lan_port)
        end
        if http_wan_enabled then
            -- We assume web validation ensures http_wan_port is different from http_lan_port if both enabled
            -- Ensure no duplicate ports if LAN and WAN use the same port (though web should prevent)
            local port_already_added = false
            for _, existing_port_entry in ipairs(listen_http_ports) do
                if string.match(existing_port_entry, ":" .. http_wan_port .. "$") then
                    port_already_added = true
                    break
                end
            end
            if not port_already_added then
                table.insert(listen_http_ports, "0.0.0.0:" .. http_wan_port)
                table.insert(listen_http_ports, "[::]:" .. http_wan_port)
            end
            write_log("HTTP: WAN access enabled on port " .. http_wan_port)
        end

        if #listen_http_ports > 0 then
            if uci_set_if_changed("uhttpd", "main", "listen_http", listen_http_ports) then uci_changed_uhttpd = true end
        else
            if uci_delete_if_exists("uhttpd", "main", "listen_http") then uci_changed_uhttpd = true end
            write_log("HTTP: Both LAN and WAN access are disabled.")
        end
        if uci_changed_uhttpd then need_restart_uhttpd = true end
    end

    -- HTTPS
    -- Removed the 'protocols_param.https_wan' check as it's merged
    if protocols_param.https then
        local https_lan_enabled = (https_settings.enabled == "1")
        local https_lan_port = https_settings.remote_port or "443"
        local https_wan_enabled = (wan_settings.https_wan_enabled == "1")
        local https_wan_port = wan_settings.https_wan_port or "8443"

        local listen_https_ports = {}
        if https_lan_enabled then
            table.insert(listen_https_ports, "0.0.0.0:" .. https_lan_port)
            table.insert(listen_https_ports, "[::]:" .. https_lan_port)
            write_log("HTTPS: LAN access enabled on port " .. https_lan_port)
        end
        if https_wan_enabled then
            -- Ensure no duplicate ports
            local port_already_added = false
            for _, existing_port_entry in ipairs(listen_https_ports) do
                if string.match(existing_port_entry, ":" .. https_wan_port .. "$") then
                    port_already_added = true
                    break
                end
            end
            if not port_already_added then
                table.insert(listen_https_ports, "0.0.0.0:" .. https_wan_port)
                table.insert(listen_https_ports, "[::]:" .. https_wan_port)
            end
            write_log("HTTPS: WAN access enabled on port " .. https_wan_port)
        end

        if #listen_https_ports > 0 then
            if uci_set_if_changed("uhttpd", "main", "listen_https", listen_https_ports) then uci_changed_uhttpd = true end
        else
            if uci_delete_if_exists("uhttpd", "main", "listen_https") then uci_changed_uhttpd = true end
            write_log("HTTPS: Both LAN and WAN access are disabled.")
        end
        if uci_changed_uhttpd then need_restart_uhttpd = true end
    end
    
    if uci_changed_uhttpd then
        local ok_commit_uhttpd, err_commit_uhttpd = pcall(function() uci:commit("uhttpd") end)
        if ok_commit_uhttpd then
            write_log("Committed uhttpd UCI changes.")
        else
            write_log("ERROR committing uhttpd UCI changes: " .. tostring(err_commit_uhttpd))
        end
    end

    -- SSH
    -- Removed the 'protocols_param.ssh_wan' check as it's merged
    if protocols_param.ssh then
        local ssh_lan_enabled = (ssh_settings.enabled == "1")
        local ssh_lan_port = ssh_settings.remote_port or "22"
        local ssh_wan_enabled = (wan_settings.ssh_wan_enabled == "1")
        local ssh_wan_port = wan_settings.ssh_wan_port or "2222"
    
        -- 删除所有旧的 Dropbear 实例（注意：如有多用户配置需保留则略此步）
        uci:foreach("dropbear", "dropbear", function(s)
            uci:delete("dropbear", s[".name"])
            uci_changed_dropbear = true
        end)
    
        if ssh_lan_enabled then
            local lan_inst = uci:add("dropbear", "dropbear")
            uci:set("dropbear", lan_inst, "PasswordAuth", "on")
            uci:set("dropbear", lan_inst, "RootPasswordAuth", "on")
            uci:set("dropbear", lan_inst, "Port", ssh_lan_port)
            uci:set("dropbear", lan_inst, "Interface", "lan")
            uci:set("dropbear", lan_inst, "Enable", "1")
            write_log("Dropbear: added LAN instance on port " .. ssh_lan_port)
            uci_changed_dropbear = true
        end
    
        if ssh_wan_enabled then
            local wan_inst = uci:add("dropbear", "dropbear")
            uci:set("dropbear", wan_inst, "PasswordAuth", "on")
            uci:set("dropbear", wan_inst, "RootPasswordAuth", "on")
            uci:set("dropbear", wan_inst, "Port", ssh_wan_port)
            uci:set("dropbear", wan_inst, "Interface", "wan")
            uci:set("dropbear", wan_inst, "Enable", "1")
            write_log("Dropbear: added WAN instance on port " .. ssh_wan_port)
            uci_changed_dropbear = true
        end
    
        if not ssh_lan_enabled and not ssh_wan_enabled then
            write_log("Dropbear: both LAN/WAN access disabled.")
        end
    
        if uci_changed_dropbear then 
            need_restart_dropbear = true
            local ok_commit_db, err_commit_db = pcall(function() uci:commit("dropbear") end)
            if ok_commit_db then
                write_log("Committed dropbear UCI changes with separate LAN/WAN instances.")
            else
                write_log("ERROR committing dropbear UCI changes: " .. tostring(err_commit_db))
            end
        end
    end
    if protocols_param.telnet then
        write_log("DEBUG: protocols_param.telnet 存在")
        local telnet_lan_enabled = (telnet_settings.enabled == "1")
        local telnet_lan_port = telnet_settings.remote_port or "23"
        local telnet_wan_enabled = (wan_settings.telnet_wan_enabled == "1")
        local telnet_wan_port = wan_settings.telnet_wan_port or "2323"
        write_log("DEBUG: telnet_lan_enabled=" .. tostring(telnet_lan_enabled) .. ", telnet_lan_port=" .. tostring(telnet_lan_port) .. ", telnet_wan_enabled=" .. tostring(telnet_wan_enabled) .. ", telnet_wan_port=" .. tostring(telnet_wan_port))

        local telnet_enabled_overall = false
        local final_telnet_port_to_set = nil -- For single port
        local telnet_ports_to_listen = {} -- For multiple ports

        if telnet_lan_enabled then
            table.insert(telnet_ports_to_listen, telnet_lan_port)
            telnet_enabled_overall = true
            write_log("Telnet: LAN access enabled on port " .. telnet_lan_port)
        else
            write_log("Telnet: LAN access disabled")
        end
        if telnet_wan_enabled then
            local port_already_added = false
            for _, existing_port in ipairs(telnet_ports_to_listen) do
                if existing_port == telnet_wan_port then
                    port_already_added = true
                    break
                end
            end
            if not port_already_added then
                 table.insert(telnet_ports_to_listen, telnet_wan_port)
            end
            telnet_enabled_overall = true
            write_log("Telnet: WAN access enabled on port " .. telnet_wan_port)
        else
            write_log("Telnet: WAN access disabled")
        end
        write_log("DEBUG: telnet_enabled_overall=" .. tostring(telnet_enabled_overall) .. ", telnet_ports_to_listen=" .. table.concat(telnet_ports_to_listen, ","))
        if telnet_enabled_overall then
            if #telnet_ports_to_listen > 0 then
                final_telnet_port_to_set = telnet_ports_to_listen[1] -- Take the first one, for simplicity
                if telnet_wan_enabled and telnet_wan_port then
                    final_telnet_port_to_set = telnet_wan_port -- WAN takes precedence for the single port
                end
            end
            write_log("DEBUG: final_telnet_port_to_set=" .. tostring(final_telnet_port_to_set))
        end
        if telnet_enabled_overall and final_telnet_port_to_set then
            write_log("DEBUG: will set telnetd.main.enable=1, port=" .. tostring(final_telnet_port_to_set))
            if uci_set_if_changed("telnetd", "main", "enable", "1") then uci_changed_telnetd = true end
            if uci_set_if_changed("telnetd", "main", "port", final_telnet_port_to_set) then uci_changed_telnetd = true end
            write_log("Telnet: Configured to listen on port " .. final_telnet_port_to_set .. " (enable=1)")
        else
            write_log("DEBUG: will set telnetd.main.enable=0")
            if uci_set_if_changed("telnetd", "main", "enable", "0") then uci_changed_telnetd = true end
            write_log("Telnet: Both LAN and WAN access are disabled. (enable=0)")
        end
        -- 无论 uci_changed_telnetd 是否为 true，都先 kill 再按配置拉起 telnetd
        write_log("DEBUG: always kill_telnetd and check enable to start")
        kill_telnetd()
        local telnet_enable = uci:get("telnetd", "main", "enable")
        local telnet_port = uci:get("telnetd", "main", "port") or "23"
        write_log("DEBUG: uci:get telnetd.main.enable=" .. tostring(telnet_enable) .. ", port=" .. tostring(telnet_port))
        if telnet_enable == "1" then
            write_log("DEBUG: will try to start telnetd")
            local telnetd_path = "/usr/sbin/telnetd"
            local which_result = io.popen("which telnetd 2>/dev/null"):read("*l")
            write_log("which telnetd result: " .. tostring(which_result))
            if which_result and #which_result > 0 then
                telnetd_path = which_result
            else
                local try_paths = {"/usr/sbin/telnetd", "/sbin/telnetd", "/bin/telnetd"}
                for _, p in ipairs(try_paths) do
                    local f = io.open(p, "r")
                    if f then f:close(); telnetd_path = p; write_log("Found telnetd at: "..p); break end
                end
            end
            os.execute("echo '[TELNETD DEBUG] id:' >>/tmp/access_settings.log; id >>/tmp/access_settings.log")
            os.execute("echo '[TELNETD DEBUG] ls -l " .. telnetd_path .. ":' >>/tmp/access_settings.log; ls -l " .. telnetd_path .. " >>/tmp/access_settings.log 2>&1")
            os.execute("echo '[TELNETD DEBUG] ps before:' >>/tmp/access_settings.log; ps -ef | grep telnetd >>/tmp/access_settings.log 2>&1")
            local telnet_cmd = telnetd_path .. " -p " .. telnet_port .. " -l /bin/login"
            write_log("即将执行 telnetd 启动命令: " .. telnet_cmd)
            local ret = os.execute(telnet_cmd .. " >>/tmp/access_settings.log 2>&1; echo '[TELNETD DEBUG] telnetd exit code: '$? >>/tmp/access_settings.log; ps -ef | grep telnetd >>/tmp/access_settings.log 2>&1")
            write_log("telnetd 启动命令已执行, os.execute 返回值: " .. tostring(ret))
            write_log("Telnetd started with: " .. telnet_cmd .. " (path checked: " .. telnetd_path .. ")")
        else
            write_log("DEBUG: telnet_enable不是1, 跳过telnetd启动, 但已kill所有telnetd")
            write_log("Telnetd not started, enable=0")
        end
    else
        write_log("DEBUG: protocols_param.telnet 不存在, 跳过Telnet配置")
    end


    -- Ensure all commits are done before sending response (redundant but safe)
    pcall(function() uci:commit("uhttpd") end)
    pcall(function() uci:commit("dropbear") end)
    pcall(function() uci:commit("telnetd") end)
    write_log("Ensured all relevant UCI changes are committed.")

    -- Debug: Log current UCI states after commit
    write_log("Current uhttpd UCI config after set:")
    sys.exec("uci show uhttpd >> " .. log_file)
    write_log("Current dropbear UCI config after set:")
    sys.exec("uci show dropbear >> " .. log_file)
    write_log("Current telnetd UCI config after set:")
    sys.exec("uci show telnetd >> " .. log_file)

    local apply_mode = get_current_apply_mode()
    write_log("Current apply mode: " .. apply_mode)

    local response_message
    if apply_mode == "immediate" then
        response_message = "Access settings updated successfully and services restarting."
    else
        response_message = "Access settings updated. Apply deferred."
    end

    -- Send response to Web interface immediately
    io.write(cjson.encode({
        module = "access_settings",
        version = "1.0",
        api = "set",
        errcode = 0,
        result = { message = response_message },
        sid = data.sid
    }))
    io.flush()
    write_log("Response sent to Web interface.")

    -- Schedule service restarts in the background based on apply_mode
    if apply_mode == "immediate" then
        local restart_commands = {}
        if need_restart_uhttpd then
            table.insert(restart_commands, "/etc/init.d/uhttpd restart")
        end
        if need_restart_dropbear then
            local ssh_is_actually_enabled_after_commit = (uci:get("dropbear", "@dropbear[0]", "Enable") == "1")
            if ssh_is_actually_enabled_after_commit then
                table.insert(restart_commands, "/etc/init.d/dropbear restart")
            else
                write_log("Dropbear service is disabled, not restarting.")
            end
        end
        -- telnetd 处理：直接后台执行 kill 和启动
        if uci_changed_telnetd then
            os.execute("(killall telnetd 2>/dev/null || (ps | grep '[t]elnetd' | awk '{print $1}' | xargs kill -9 2>/dev/null)) &")
            local telnet_enable = uci:get("telnetd", "main", "enable")
            local telnet_port = uci:get("telnetd", "main", "port") or "23"
            if telnet_enable == "1" then
                -- 检查 telnetd 路径
                local telnetd_path = "/usr/sbin/telnetd"
                local which_result = io.popen("which telnetd 2>/dev/null"):read("*l")
                write_log("which telnetd result: " .. tostring(which_result))
                if which_result and #which_result > 0 then
                    telnetd_path = which_result
                else
                    local try_paths = {"/usr/sbin/telnetd", "/sbin/telnetd", "/bin/telnetd"}
                    for _, p in ipairs(try_paths) do
                        local f = io.open(p, "r")
                        if f then f:close(); telnetd_path = p; break end
                    end
                end
                -- 记录 telnetd 路径和权限、当前用户
                os.execute("echo '[TELNETD DEBUG] id:' >>/tmp/access_settings.log; id >>/tmp/access_settings.log")
                os.execute("echo '[TELNETD DEBUG] ls -l " .. telnetd_path .. ":' >>/tmp/access_settings.log; ls -l " .. telnetd_path .. " >>/tmp/access_settings.log 2>&1")
                os.execute("echo '[TELNETD DEBUG] ps before:' >>/tmp/access_settings.log; ps -ef | grep telnetd >>/tmp/access_settings.log 2>&1")
                local telnet_cmd = telnetd_path .. " -p " .. telnet_port .. " -l /bin/login"
                write_log("即将执行 telnetd 启动命令: " .. telnet_cmd)
                local ret = os.execute(telnet_cmd .. " >>/tmp/access_settings.log 2>&1; echo '[TELNETD DEBUG] telnetd exit code: '$? >>/tmp/access_settings.log; ps -ef | grep telnetd >>/tmp/access_settings.log 2>&1")
                write_log("telnetd 启动命令已执行, os.execute 返回值: " .. tostring(ret))
                write_log("Telnetd started with: " .. telnet_cmd .. " (path checked: " .. telnetd_path .. ")")
            else
                write_log("Telnetd not started, enable=0")
            end
        end
        if #restart_commands > 0 then
            local temp_script_path = "/tmp/access_settings_restart_" .. os.time() .. ".sh"
            local script_content = "#!/bin/sh\nsleep 2\n"
            for _, cmd in ipairs(restart_commands) do
                script_content = script_content .. cmd .. " >/dev/null 2>&1\n"
            end
            script_content = script_content .. "rm -f '" .. temp_script_path .. "'\n"
            local f_script = io.open(temp_script_path, "w")
            if f_script then
                f_script:write(script_content)
                f_script:close()
                sys.call("chmod +x " .. temp_script_path)
                write_log("Created and made executable temporary restart script: " .. temp_script_path)
                sys.exec("nohup " .. temp_script_path .. " >/dev/null 2>&1 &")
                write_log("Scheduled background service restarts via " .. temp_script_path)
            else
                write_log("ERROR: Failed to create temporary restart script: " .. temp_script_path)
                for _, cmd in ipairs(restart_commands) do
                    sys.exec("(sleep 2; " .. cmd .. ") &")
                    write_log("Falling back to direct sys.exec for restart: " .. cmd)
                end
            end
        end
    end
end

-- [MODIFIED] 获取访问设置
function get_access_settings(data)
    ensure_telnetd_config()
    ensure_remoteaccess_config()

    local result = {
        protocols = {
            http = { enabled = "0", remote_port = "80", enabled_wan = "0", remote_port_wan = "8080" },
            https = { enabled = "0", remote_port = "443", enabled_wan = "0", remote_port_wan = "8443" },
            ssh = { enabled = "0", remote_port = "22", enabled_wan = "0", remote_port_wan = "2222" },
            telnet = { enabled = "0", remote_port = "23", enabled_wan = "0", remote_port_wan = "2323" }
        }
    }

    -- 读取WAN设置
    local wan_cfg = get_remote_settings_from_file()
    for proto, keys in pairs(result.protocols) do
        keys.enabled_wan = wan_cfg[proto .. "_wan_enabled"] or keys.enabled_wan
        keys.remote_port_wan = wan_cfg[proto .. "_wan_port"] or keys.remote_port_wan
    end
  -- 读取LAN侧设置
      -- HTTP / HTTPS: uhttpd 配置监听判断
      local function parse_uhttpd_protocol(proto_name, uci_key)
        local proto = result.protocols[proto_name]
        proto.enabled = "0"
        proto.enabled_wan = "0"

        local ports = uci:get_list("uhttpd", "main", uci_key) or {}
        for _, entry in ipairs(ports) do
            local ip, port = entry:match("^(%S+):(%d+)$")
            if ip and port then
                port = tostring(port)
                if port == proto.remote_port then
                    proto.enabled = "1"
                    proto.remote_port = port
                elseif port == proto.remote_port_wan then
                    proto.enabled_wan = "1"
                    proto.remote_port_wan = port
                else
                    -- 如果端口非默认，但仍监听，优先归为 LAN
                    proto.enabled = "1"
                    proto.remote_port = port
                end
            end
        end
    end

    parse_uhttpd_protocol("http", "listen_http")
    parse_uhttpd_protocol("https", "listen_https")


    -- SSH: dropbear 配置
    local dropbear_main = uci:get_first("dropbear", "dropbear")
    if dropbear_main then
        local list = uci:get_list("dropbear", dropbear_main, "ListenAddress") or {}
        local port = uci:get("dropbear", dropbear_main, "Port")
        if #list > 0 then
            for _, addr in ipairs(list) do
                local _, p = addr:match("^(%S+):(%d+)$")
                if p == result.protocols.ssh.remote_port then
                    result.protocols.ssh.enabled = "1"
                elseif p == result.protocols.ssh.remote_port_wan then
                    result.protocols.ssh.enabled_wan = "1"
                else
                    result.protocols.ssh.remote_port = p
                    result.protocols.ssh.enabled = "1"
                end
            end
        elseif port then
            result.protocols.ssh.remote_port = port
            result.protocols.ssh.enabled = "1"
        end
    end


-- telnet配置获取
local port = uci:get("telnetd", "main", "port")
local enable = uci:get("telnetd", "main", "enable")

write_log("Telnetd config: port=" .. tostring(port) .. ", enable=" .. tostring(enable))
write_log("Compared with remote_port_wan=" .. result.protocols.telnet.remote_port_wan)

if port then
    result.protocols.telnet.remote_port = port  -- LAN端口
end

if enable == "1" then
    result.protocols.telnet.enabled = "1"  -- LAN启用

    -- 如果该端口也配置为 WAN 使用，并且 remoteaccess 中启用了 WAN
    if port == result.protocols.telnet.remote_port_wan and result.protocols.telnet.enabled_wan == "1" then
        result.protocols.telnet.enabled_wan = "1"
    else
        result.protocols.telnet.enabled_wan = "0"
    end
end


    io.write(cjson.encode({
        module = "access_settings", version = "1.0", errcode = 0,
        sid = data.sid, result = result
    }))
    io.flush()
end

-- 入口函数
local function run()
    write_log("Access Settings API started")
        route_api()
    write_log("Access Settings API finished")
end

run()