#!/usr/bin/lua
-- 引入模块
local cjson = require("cjson.safe")
local sys = require("luci.sys")
local log_file = "/tmp/upgrade.log" -- 日志文件路径

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end




-- 路由到具体逻辑
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 获取 POST 数据
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""
    if POSTLength > 0 then
        POST = io.read(POSTLength)
        write_log("Received POST data: " .. (POST or "nil"))
    else
        write_log("No POST data received or CONTENT_LENGTH is 0")
    end

    if not POST or POST == "" then
        local error_message = "Failed to retrieve POST data"
        write_log(error_message)
        io.write(cjson.encode({
            module = "upgrade",
            version = "1.0",
            errcode = 1,
            result = { message = error_message }
        }))
        return
    end

    -- 解析 POST 数据为 JSON
    local requestData = cjson.decode(POST)
    if not requestData then
        local error_message = "Invalid JSON input"
        write_log(error_message)
        io.write(cjson.encode({
            module = "upgrade",
            version = "1.0",
            errcode = 2,
            result = { message = error_message }
        }))
        return
    end

    write_log("Parsed request data: " .. cjson.encode(requestData))

    -- 检查请求格式
    if not requestData.version or not requestData.sid or not requestData.module or not requestData.api then
        local error_message = "Invalid request format"
        write_log(error_message)
        io.write(cjson.encode({
            module = "upgrade",
            version = "1.0",
            errcode = 3,
            result = { message = error_message }
        }))
        return
    end
    if requestData.api == "upgrade" then
        write_log("Calling perform_upgrade with data: " .. cjson.encode(requestData))
        perform_upgrade(requestData)
    else
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "upgrade",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
    end
end

-- 执行固件升级
function perform_upgrade(data)
    local firmware_path = "/tmp/upload/firmware.bin"

    -- 检查固件文件是否存在
    local file_exists = sys.call("ls " .. firmware_path .. " >/dev/null 2>&1") == 0
    if not file_exists then
        local error_message = "Firmware file not found at " .. firmware_path
        write_log(error_message)
        io.write(cjson.encode({
            module = "upgrade",
            version = "1.0",
            api = "upgrade",
            errcode = 5,
            sid = data.sid,
            result = { message = error_message }
        }))
        return
    end

    -- 返回响应后再执行升级
    write_log("Firmware file found, starting upgrade process")
    io.write(cjson.encode({
        module = "upgrade",
        version = "1.0",
        api = "upgrade",
        errcode = 0,
        sid = data.sid,
        result = { message = "Firmware upgrade started, device will reboot soon" }
    }))
    io.flush() -- 确保响应发送完成

    -- 异步执行 sysupgrade
    local upgrade_cmd = "sleep 3 && sysupgrade " .. firmware_path .. " >/dev/null 2>&1 &"
    local result = sys.call(upgrade_cmd)
    if result ~= 0 then
        write_log("Failed to start sysupgrade command, result: " .. result)
    else
        write_log("Sysupgrade command initiated successfully")
    end
end

-- 检查是否在CGI环境中运行
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- AC 调用接口
local M = {}

function M.set_config_from_ac(payload)
    write_log("[AC] set_config_from_ac called: " .. cjson.encode(payload))

    -- 调用现有的upgrade函数
    local data = { param = payload }
    upgrade(data)

    write_log("[AC] Firmware upgrade initiated")
    return true, "Firmware upgrade initiated by AC"
end

function M.get_config_for_ac()
    local config = {
        current_version = sys.exec("cat /etc/openwrt_version 2>/dev/null || echo 'unknown'"):gsub("\n", ""),
        upgrade_status = "ready"
    }
    write_log("[AC] get_config_for_ac: retrieved upgrade status")
    return config
end

-- 仅在明确作为 CGI 脚本运行时执行
if arg and arg[0] and arg[0]:match("upgrade%.lua") and is_cgi() then
    local function run()
        write_log("Upgrade API started")
        route_api()
        write_log("Upgrade API finished")
    end
    run()
end

return M