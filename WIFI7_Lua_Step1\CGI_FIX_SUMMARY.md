# AC管理系统CGI问题完整修复总结

## 问题描述
用户在设备上测试AC管理系统时遇到多个CGI执行问题，主要原因是多个Lua模块在被require()时执行了CGI相关代码，导致ac_handlers模块加载失败。

## 根本原因
- CGI代码（如io.write("Content-type:")、POST数据读取等）在模块顶层执行
- 当其他模块require这些模块时，CGI代码会立即执行
- 在非CGI环境中，os.getenv("CONTENT_LENGTH")等返回nil，导致错误

## 解决方案
将所有CGI相关代码移动到route_api()函数内部，只有在CGI环境中直接调用时才执行。

## 已修复的文件列表

### ✅ 核心AC管理模块（已修复）
1. **user_event.lua** - 用户事件管理
   - 问题：line 81 attempt to concatenate local 'POST' (a nil value)
   - 修复：CGI代码移到route_api()函数

2. **info.lua** - 用户信息查询
   - 修复：CGI代码移到route_api()函数

3. **status.lua** - 系统状态查询
   - 修复：CGI代码移到route_api()函数

4. **route_table.lua** - 路由表管理
   - 修复：CGI代码移到route_api()函数

### ✅ 网络模式管理模块（之前已修复）
5. **ap_mode.lua** - AP模式配置
6. **route_mode.lua** - 路由模式配置  
7. **bridge_mode.lua** - 桥接模式配置

### ✅ 网络功能模块（之前已修复）
8. **vlan.lua** - VLAN配置
9. **user_setup.lua** - 用户设置
10. **snmp.lua** - SNMP配置
11. **qos_management.lua** - QoS管理
12. **current_device.lua** - 当前设备信息
13. **scheduled_reboot.lua** - 定时重启

### ✅ 无线管理模块（之前已修复）
14. **mac_access_control.lua** - MAC访问控制（无CGI问题）

## 修复模式
所有修复都遵循相同的模式：

### 修复前：
```lua
-- 设置 HTTP 响应头
io.write("Content-type: application/json\nPragma: no-cache\n\n")

-- 获取 POST 数据
local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
local POST = ""
if POSTLength > 0 then
    POST = io.read(POSTLength)
    -- ... 处理POST数据
end

local function route_api()
    -- 业务逻辑
end
```

### 修复后：
```lua
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 获取 POST 数据
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""
    if POSTLength > 0 then
        POST = io.read(POSTLength)
        -- ... 处理POST数据
    end
    
    -- 业务逻辑
end
```

## 测试状态
- ✅ ac_main_loop.lua 成功启动，监听UDP端口50001
- ✅ 主要AC管理模块CGI问题已修复
- ⏳ ac_handlers.lua 模块加载测试待验证

## 下一步
1. 上传修复后的文件到设备
2. 重启AC管理服务
3. 验证ac_handlers模块能否正常加载
4. 使用网络助手进行协议测试

## 修复文件清单
用户需要上传以下修复后的文件：
- user_event.lua
- info.lua  
- status.lua
- route_table.lua
- （其他之前修复的文件如果未上传）

## 注意事项
- 所有修复都保持了原有的业务逻辑不变
- 只是将CGI代码从模块顶层移动到route_api()函数内
- 确保在CGI和模块require两种场景下都能正常工作
