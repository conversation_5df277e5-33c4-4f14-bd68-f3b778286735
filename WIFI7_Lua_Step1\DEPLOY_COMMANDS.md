# Client Mode 修复部署命令

## 问题修复说明

1. **语法错误修复**: 修复了第899行的多余`elseif`语句
2. **SAE加密支持增强**: 添加了对混合WPA2/WPA3 PSK/SAE加密方式的支持

## 部署命令序列

### 1. 在设备上备份当前文件
```bash
cd /www/cgi-bin/3onedata
cp client_mode.lua client_mode.lua.backup.$(date +%Y%m%d_%H%M%S)
```

### 2. 下载修复后的文件
```bash
# 删除旧文件
rm client_mode.lua

# 下载修复后的文件
tftp -g 192.168.1.125 -r client_mode.lua
tftp -g 192.168.1.125 -r check_syntax.sh
tftp -g 192.168.1.125 -r test_client_mode_fixed.sh

# 设置权限
chmod +x *.lua *.sh
```

### 3. 验证语法修复
```bash
# 运行语法检查
./check_syntax.sh
```

### 4. 测试功能
```bash
# 运行完整测试
./test_client_mode_fixed.sh
```

### 5. Web测试
```bash
# 使用curl测试Web请求
curl "http://192.168.1.254/cgi-bin/3onedata/client_mode.lua?action=get_status"
```

## 预期结果

### 语法检查成功
```
=== Lua 语法检查 ===
检查 client_mode.lua 语法:
✓ client_mode.lua 语法正确
```

### 功能测试成功
```
=== 测试 client_mode.lua 修复版本 ===
1. 检查Lua语法:
✓ 语法检查通过

2. 测试 GET 请求 - get_status API
REQUEST_METHOD: GET
QUERY_STRING: action=get_status

执行结果:
{"module":"client_mode","version":"1.0","api":"get_sta_status","errcode":0,"sid":"get_status_request","result":{...}}

✓ 脚本执行成功

3. 检查日志文件:
✓ 日志文件存在
```

### Web请求成功
```bash
curl "http://192.168.1.254/cgi-bin/3onedata/client_mode.lua?action=get_status"
# 应该返回JSON响应而不是"Bad Gateway"
```

## SAE加密支持

修复后的版本支持以下SAE加密方式：

1. **WPA3 SAE** → `sae`
2. **WPA2/WPA3 SAE** → `sae-mixed`
3. **mixed WPA2/WPA3 PSK/SAE (CCMP, GCMP, CCMP-256, GCMP-256)** → `sae-mixed`
4. **WPA3 PSK/SAE (CCMP, GCMP, CCMP-256, GCMP-256)** → `sae`
5. **WPA2/WPA3 PSK/SAE** → `sae-mixed`

当检测到SAE加密方式时，会自动设置：
```lua
uci:set("wireless", sta_iface, "sae", "1")
```

## 故障排除

### 如果仍然出现"Bad Gateway"

1. **检查Web服务器日志**:
   ```bash
   tail -f /var/log/httpd/error.log
   ```

2. **检查CGI权限**:
   ```bash
   ls -la client_mode.lua
   # 应该显示: -rwxr-xr-x 1 <USER> <GROUP>
   ```

3. **检查Lua模块**:
   ```bash
   lua -e "print(require('cjson.safe'))"
   lua -e "print(require('luci.sys'))"
   ```

4. **手动测试脚本**:
   ```bash
   export REQUEST_METHOD="GET"
   export QUERY_STRING="action=get_status"
   lua client_mode.lua
   ```

### 如果语法检查失败

1. **重新下载文件**:
   ```bash
   rm client_mode.lua
   tftp -g 192.168.1.125 -r client_mode.lua
   ```

2. **检查文件完整性**:
   ```bash
   wc -l client_mode.lua
   # 应该显示大约1270行左右
   ```

3. **查看具体错误**:
   ```bash
   lua -c client_mode.lua
   # 会显示具体的语法错误位置
   ```

## 成功标志

修复成功后，您应该看到：

1. ✅ 语法检查通过
2. ✅ 生成 `/tmp/client_mode.log` 日志文件
3. ✅ GET请求返回JSON响应
4. ✅ Web请求不再返回"Bad Gateway"
5. ✅ SAE加密方式正确识别和配置
