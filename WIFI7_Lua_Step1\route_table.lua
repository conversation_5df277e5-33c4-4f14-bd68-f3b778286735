#!/usr/bin/lua

-- Copyright (c) 2013 The Linux Foundation. All rights reserved.
-- Not a Contribution.

-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

-- Lua环境：此脚本在OpenWRT的Lua环境中运行，确保有必要的Lua库，如luci.jsonc和luci.sys
-- UCI：这个脚本使用了uci库来直接操作配置文件。这比直接读写文件更为安全和标准。

-- 错误处理：脚本中包含了基本的错误处理，可以根据实际需求进行扩展。

-- 获取路由表：
  -- get_route_table函数使用sys.exec("ip route")来获取系统的路由表信息。
  -- 解析路由表输出，提取目的IP地址、网关、网络掩码和输出接口。
  -- 对于默认路由和直接连接的路由，脚本进行了特殊处理。
  -- 结果存储在route_table数组中，每个条目包含dest_ip、gateway、mask和interface信息。

--只提供get操作，因为路由表通常是系统自动生成的，用户不直接设置。
--没有服务重启的逻辑，因为获取路由表信息不需要重启任何服务。
--使用io.write来输出HTTP响应，包括HTTP头和JSON内容

-- API调用格式 get：
--{
--    "version": "1.0",
--    "sid": "5bdd8aec153a8ff183387bfcea40e3aa",
--    "module": "route_table",
--    "api": "get",
--    "param": {}
--}


-- 返回参数格式
--{
--    "module": "route_table",
--    "version": "1.0",
--    "api": "get",
--    "errcode": 0,
--    "sid": "5bdd8aec153a8ff183387bfcea40e3aa",
--    "result": [
--       {
--            "dest_ip": "***********",
--            "gateway": "0.0.0.0",
--            "mask": "*************",
--            "interface": "br-lan"
--        },
--        {
--            "dest_ip": "default",
--            "gateway": "***********",
--            "mask": "0.0.0.0",
--            "interface": "wan"
--        }
--    ]
--}

-- 安全性：确保处理用户输入时，进行了必要的验证和清理，以防止注入攻击
-- 性能：由于脚本每次都需要读取和写入配置文件，对于频繁调用的API，可以考虑优化，例如通过缓存减少IO操作。
-- 权限：确保脚本以必要的权限运行，修改网络配置文件和重启网络服务需要root权限。

-- 引入模块
local cjson = require("cjson.safe")
local sys = require("luci.sys")
local log_file = "/tmp/route_table.log" -- 日志文件路径

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end



write_log("Parsed request data: " .. cjson.encode(requestData))

-- 检查请求格式
if not requestData.version or not requestData.sid or not requestData.module or not requestData.api then
    local error_message = "Invalid request format"
    write_log(error_message)
    io.write(cjson.encode({
        module = "route_table",
        version = "1.0",
        errcode = 3,
        result = { message = error_message }
    }))
    return
end

-- 路由到具体逻辑
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 获取 POST 数据长度
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""

    -- 读取 POST 数据
    if POSTLength > 0 then
        POST = io.read(POSTLength)
        write_log("Received POST data: " .. (POST or "nil"))
    else
        write_log("No POST data received or CONTENT_LENGTH is 0")
    end

    -- 确保读取成功
    if not POST or POST == "" then
        local error_message = "Failed to retrieve POST data"
        write_log(error_message)
        io.write(cjson.encode({
            module = "route_table",
            version = "1.0",
            errcode = 1,
            result = { message = error_message }
        }))
        return
    end

    -- 解析 POST 数据为 JSON
    local requestData = cjson.decode(POST)
    if not requestData then
        local error_message = "Invalid JSON input"
        write_log(error_message)
        io.write(cjson.encode({
            module = "route_table",
            version = "1.0",
            errcode = 2,
            result = { message = error_message }
        }))
        return
    end
    if requestData.api == "get" then
        write_log("Calling get_route_table with data: " .. cjson.encode(requestData))
        get_route_table(requestData)
    else
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "route_table",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
    end
end

function get_route_table(data)
    local route_table = {}
    local route_entries = sys.exec("ip route") -- 获取系统的路由表信息
    write_log("Route entries output: " .. route_entries) -- 添加日志记录输出

    local lines = {}
    for line in route_entries:gmatch("[^\r\n]+") do
        table.insert(lines, line)
    end

    -- 解析路由表输出
    for _, line in ipairs(lines) do
        write_log("Processing line: " .. line)

        -- 匹配常规路由
        local dest_ip_mask, interface = line:match("(%S+)%s+dev%s+(%S+)")
        if dest_ip_mask and interface then
            local dest_ip, mask = dest_ip_mask:match("(.+)/(%d+)")
            local gateway = "0.0.0.0"  -- 直接连接的路由没有网关
            if dest_ip and mask then
                table.insert(route_table, {
                    dest_ip = dest_ip,
                    gateway = gateway,
                    mask = mask,
                    interface = interface
                })
            end
        end

        -- 匹配默认路由
        if line:match("default") then
            local _, _, gateway, _, interface = line:match("default%s+via%s+(%S+)%s+dev%s+(%S+)")
            if gateway and interface then
                table.insert(route_table, {
                    dest_ip = "default",
                    gateway = gateway,
                    mask = "0.0.0.0",
                    interface = interface
                })
            end
        end
    end

    write_log("Route table retrieved successfully: " .. cjson.encode(route_table))
    io.write(cjson.encode({
        module = "route_table",
        version = "1.0",
        api = "get",
        errcode = 0,
        sid = data.sid,
        result = route_table
    }))
end

-- 显式调用入口函数
local function run()
    write_log("Route Table API started")
    route_api()
    write_log("Route Table API finished")
end

run()
