#!/usr/bin/lua

-- Copyright (c) 2024 The Linux Foundation. All rights reserved.
-- Not a Contribution.

-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

-- Lua环境：此脚本在OpenWRT的Lua环境中运行，确保有必要的Lua库，如luci.sys, luci.jsonc和luci.uci

-- UCI：这个脚本使用了uci库来直接操作配置文件。这比直接读写文件更为安全和标准。

--4.1 连接方式
--支持 WDS 桥接、万能桥接和无线 NAT 三种模式。
--WDS：wds=1，STA 桥接到 lan。
--万能桥接：extap=1，STA 桥接到 lan。
--无线 NAT：STA 连接到 wwan，启用 NAT 和 DHCP。
--4.2 外网设置（无线 NAT）
--复用路由模式的 set_wan_settings，将接口从 eth1.2 改为 STA 接口（ath5 等）。
--支持 PPPoE、静态 IP 和 DHCP。
--4.3 内网设置
--WDS：支持静态 IP 和 DHCP。
--万能桥接和无线 NAT：仅支持静态 IP。
--br-lan 地址不动态监测，获取不到地址时由无线驱动自动获取。
--4.4 连接设置
--复用网桥模式的 set_connection_settings。
--新增高效漫游支持：
--开启时，设置 roaming_rssi_diff 和 scan_channels。
--关闭时，清理相关配置。
--4.5 配置清理
--关闭所有 VAP（ath0~ath3、ath10~ath13、ath30~ath33）。
--禁用 WAN 接口。
--清理路由模式和网桥模式的残留配置。
--根据连接方式调整防火墙和 DHCP。
--4.6 其他
--提供 scan_ssid 和 get_sta_status API，与网桥模式一致。
--异步重启服务，确保配置生效


-- 引入模块
local cjson = require("cjson.safe")
local sys = require("luci.sys")
local uci = require("luci.model.uci").cursor()
local log_file = "/tmp/client_mode.log" -- 日志文件路径

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end

-- 获取当前应用模式的辅助函数
local function get_current_apply_mode()
    local mode_file = "/etc/config_apply_mode_status"
    local f = io.open(mode_file, "r")
    if f then
        local mode = f:read("*l") -- Read the first line
        f:close()
        mode = mode and mode:gsub("^%s*(.-)%s*$", "%1") -- Trim whitespace
        if mode == "immediate" or mode == "deferred" then
            write_log("Read apply_mode from " .. mode_file .. ": " .. mode)
            return mode
        else
            write_log("Invalid content in " .. mode_file .. ": '" .. (mode or "nil") .. "'. Defaulting to 'immediate'.")
            return "immediate"
        end
    else
        write_log("Could not open " .. mode_file .. ". Defaulting to 'immediate'.")
        return "immediate"
    end
end

-- 设置 HTTP 响应头
io.write("Content-type: application/json\nPragma: no-cache\n\n")

-- 获取 POST 数据
local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
local POST = ""
if POSTLength > 0 then
    POST = io.read(POSTLength)
    write_log("Received POST data: " .. POST)
else
    write_log("No POST data received or CONTENT_LENGTH is 0")
end

if not POST or POST == "" then
    local error_message = "Failed to retrieve POST data"
    write_log(error_message)
    io.write(cjson.encode({
        module = "client_mode",
        version = "1.0",
        errcode = 1,
        result = { message = error_message }
    }))
    io.flush()
    return
end

-- 解析 POST 数据为 JSON
local requestData = cjson.decode(POST)
if not requestData then
    local error_message = "Invalid JSON input"
    write_log(error_message)
    io.write(cjson.encode({
        module = "client_mode",
        version = "1.0",
        errcode = 2,
        result = { message = error_message }
    }))
    io.flush()
    return
end

write_log("Parsed request data: " .. cjson.encode(requestData))

-- 检查请求格式
if not requestData.version or not requestData.sid or not requestData.module or not requestData.api then
    local error_message = "Invalid request format"
    write_log(error_message)
    io.write(cjson.encode({
        module = "client_mode",
        version = "1.0",
        errcode = 3,
        result = { message = error_message }
    }))
    io.flush()
    return
end

-- 验证 IP 地址、子网掩码、网关和 DNS
local function is_valid_ip(ip)
    return ip and ip:match("^%d+%.%d+%.%d+%.%d+$") ~= nil
end

local function is_valid_netmask(netmask)
    return netmask and netmask:match("^%d+%.%d+%.%d+%.%d+$") ~= nil
end

local function is_valid_gateway(gateway)
    return gateway == "" or (gateway and is_valid_ip(gateway))
end

local function is_valid_dns(dns)
    return dns == "" or (dns and is_valid_ip(dns))
end

-- 验证 RSSI 阈值
local function is_valid_rssi(rssi)
    local r = tonumber(rssi)
    return r and r >= -100 and r <= 0
end

-- 路由到具体逻辑
local function route_api()
    write_log("route_api entered, api=" .. tostring(requestData.api))
    if requestData.api == "set" then
        write_log("Calling set_all_settings with data: " .. cjson.encode(requestData))
        set_all_settings(requestData)
    elseif requestData.api == "get" then
        write_log("Calling get_all_settings with data: " .. cjson.encode(requestData))
        get_all_settings(requestData)
    elseif requestData.api == "scan_ssid" then
        write_log("Calling scan_ssid with data: " .. cjson.encode(requestData))
        scan_ssid(requestData)
    elseif requestData.api == "get_sta_status" then
        write_log("Calling get_sta_status with data: " .. cjson.encode(requestData))
        get_sta_status_api(requestData)
    else
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "client_mode",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
        io.flush()
    end
end

-- 配置防火墙（WDS 和万能桥接）
local function configure_bridge_firewall()
    uci:delete("firewall", "wwan_zone")
    uci:delete("firewall", "wwan_to_lan")
    uci:delete("firewall", "masquerade_rule")
    uci:foreach("firewall", "zone", function(s)
        if s.name == "wan" then
            uci:delete("firewall", s[".name"])
        end
        if s.name == "lan" then
            uci:delete("firewall", s[".name"])
        end
    end)
    uci:foreach("firewall", "rule", function(s)
        if s.src == "wan" then
            uci:delete("firewall", s[".name"])
        end
    end)
    uci:section("firewall", "zone", "lan_zone", {
        name = "lan",
        input = "ACCEPT",
        output = "ACCEPT",
        forward = "ACCEPT",
        network = "lan"
    })
    uci:commit("firewall")
    write_log("Configured firewall for bridge-like client mode")
end

-- 配置防火墙（无线 NAT）
local function configure_nat_firewall()
    uci:foreach("firewall", "zone", function(s)
        if s.name == "wan" then
            uci:delete("firewall", s[".name"])
        end
        if s.name == "lan" then
            uci:delete("firewall", s[".name"])
        end
    end)
    uci:foreach("firewall", "rule", function(s)
        if s.src == "wan" then
            uci:delete("firewall", s[".name"])
        end
    end)
    uci:section("firewall", "zone", "lan_zone", {
        name = "lan",
        input = "ACCEPT",
        output = "ACCEPT",
        forward = "ACCEPT",
        network = "lan"
    })
    uci:section("firewall", "zone", "wwan_zone", {
        name = "wwan",
        input = "REJECT",
        output = "ACCEPT",
        forward = "REJECT",
        masq = "1",
        mtu_fix = "1",
        network = "wwan"
    })
    uci:section("firewall", "forwarding", "lan_to_wwan", {
        src = "lan",
        dest = "wwan"
    })
    uci:commit("firewall")
    write_log("Configured firewall for NAT client mode")
end

-- 获取 STA 状态
function get_sta_status(wwan_iface)
    local ifname = uci:get("network", wwan_iface, "ifname") or "ath5"
    local status_output = sys.exec("iwinfo " .. ifname .. " info")
    if status_output and status_output:match("ESSID:") then
        local essid = status_output:match("ESSID: \"(.+)\"") or "-"
        local signal = status_output:match("Signal: (%-%d+) dBm") or "-"
        local ap = status_output:match("Access Point: ([%w:]+)") or "-"
        return {
            connected = true,
            ssid = essid,
            signal = signal,
            ap = ap
        }
    else
        return {
            connected = false,
            ssid = "N/A",
            signal = "N/A",
            ap = "N/A"
        }
    end
end

local function get_sta_status_api(data)
    write_log("get_sta_status_api entered")
    local param = data.param or {}
    local band = param.band or "2.4G"
    local ifname = nil

    if band == "2.4G" then
        ifname = "ath5"
    elseif band == "5G" then
        ifname = "ath15"
    elseif band == "6G" then
        ifname = "ath35"
    else
        write_log("Invalid band: " .. tostring(band))
        io.write(cjson.encode({
            module = "client_mode",
            version = "1.0",
            api = "get_sta_status",
            errcode = 10,
            sid = data.sid,
            result = { message = "Invalid band: " .. tostring(band) }
        }))
        io.flush()
        return
    end

    write_log("Selected ifname: " .. ifname)
    local status_output = sys.exec("iwinfo " .. ifname .. " info")
    write_log("iwinfo output: " .. (status_output:sub(1,200) or "<empty>"))
    local status = {
        connected = false,
        ssid = "N/A",
        signal = "N/A",
        ap = "N/A"
    }
    if status_output and status_output:match("ESSID:") then
        status.ssid = status_output:match("ESSID: \"([^\"]+)\"") or "unknown"
        status.signal = status_output:match("Signal: (%-?%d+) dBm") or "unknown"
        status.ap = status_output:match("Access Point: ([%w:]+)") or "unknown"
        status.connected = true
    end

    write_log("STA status: " .. cjson.encode(status))
    io.write(cjson.encode({
        module = "client_mode",
        version = "1.0",
        api = "get_sta_status",
        errcode = 0,
        sid = data.sid,
        result = { band = band, ifname = ifname, status = status }
    }))
    io.flush()
end

-- 管理接口
local function manage_interfaces_for_mode(connection_type, sta_ifname)
    uci:delete("network", "wwan0")
    uci:delete("network", "wwan1")
    uci:delete("network", "wwan3")

    if connection_type == "wds" or connection_type == "universal" then
        local ifnames = {"eth1.1"} -- 不包含 eth1.2，与 WAN 口隔离
        if sta_ifname then
            table.insert(ifnames, sta_ifname)
        end
        uci:set("network", "lan", "ifname", table.concat(ifnames, " "))
        uci:set("network", "lan", "proto", "dhcp")
        uci:set("network", "lan", "type", "bridge")
        uci:set("dhcp", "lan", "ignore", "1")
    elseif connection_type == "nat" then
        uci:set("network", "lan", "ifname", "eth1.1")
        uci:set("network", "lan", "type", "bridge")
        uci:set("network", "wwan", "interface")
        uci:set("network", "wwan", "ifname", sta_ifname or "ath5")
        uci:set("network", "wwan", "proto", "dhcp")
        uci:set("dhcp", "lan", "ignore", "0")
        uci:set("dhcp", "lan", "start", "100")
        uci:set("dhcp", "lan", "limit", "150")
        uci:set("dhcp", "lan", "leasetime", "12h")
    end

    uci:commit("network")
    uci:commit("dhcp")
    write_log("Configured interfaces for connection_type: " .. connection_type)
end

-- 设置所有配置
function set_all_settings(data)
    local param = data.param or {}
    local connection_type = param.connection_type or "wds"
    local current_mode = uci:get("system", "nhx", "mode") or "router"

    -- 设置系统模式为 client
    uci:set("system", "nhx", "mode", "client")
    uci:set("system", "nhx", "client_mode", connection_type)
    write_log("Setting system mode to client, client_mode to " .. connection_type)

    -- 清理其他模式的配置
    write_log("Cleaning up configurations from other modes")

    -- 1. 清理路由模式配置
    if current_mode == "router" then
        write_log("Cleaning up router mode configurations")
        uci:delete("network", "wan", "ipaddr")
        uci:delete("network", "wan", "netmask")
        uci:delete("network", "wan", "gateway")
        uci:delete("network", "wan", "dns")
        uci:set("network", "wan", "disabled", "1")
        uci:set("network", "wan6", "disabled", "1")
    end

    -- 2. 清理网桥模式配置
    if current_mode == "bridge" then
        write_log("Cleaning up bridge mode configurations")
        sys.exec("killall bridge_monitor 2>/dev/null")
        write_log("Killed existing bridge_monitor process")
    end

    -- 3. 清理 VLAN 配置
    uci:foreach("network", "switch_vlan", function(s)
        uci:delete("network", s[".name"])
    end)
    uci:set("network", "switch_vlan1", "switch_vlan")
    uci:set("network", "switch_vlan1", "device", "switch1")
    uci:set("network", "switch_vlan1", "vlan", "1")
    uci:set("network", "switch_vlan1", "ports", "0t 1 2 3 4")

    -- 4. 关闭所有 VAP
    local vap_ifaces = {
        "wlan0", "wlan1", "wlan2", "wlan3",      -- 2.4G (ath0~ath3)
        "wlan10", "wlan11", "wlan12", "wlan13",  -- 5G (ath10~ath13)
        "wlan30", "wlan31", "wlan32", "wlan33"   -- 6G (ath30~ath33)
    }
    for _, iface in ipairs(vap_ifaces) do
        uci:set("wireless", iface, "disabled", "1")
    end
    uci:commit("wireless")
    write_log("Disabled all VAP interfaces")

    -- 5. 禁用 DDNS 服务
    sys.exec("/etc/init.d/ddns stop")
    write_log("Disabled DDNS service")

    -- 6. 设置防火墙
    if connection_type == "wds" or connection_type == "universal" then
        configure_bridge_firewall()
    elseif connection_type == "nat" then
        configure_nat_firewall()
    end

    -- 设置连接方式和连接设置
    local connection = param.connection or {}
    local sta_ifname = set_connection_settings(connection_type, connection)

    -- 管理接口
    manage_interfaces_for_mode(connection_type, sta_ifname)

    -- 设置外网（仅无线 NAT）
    if connection_type == "nat" then
        local wan_param = param.wan or {}
        set_wan_settings(wan_param, sta_ifname)
    end

    -- 设置内网
    local lan_param = param.lan or {}
    set_lan_settings(lan_param, connection_type)

    -- 提交所有更改
    uci:commit("network")
    uci:commit("wireless")
    uci:commit("firewall")
    uci:commit("dhcp")
    uci:commit("system")

    -- 返回响应
    local result = {
        module = "client_mode",
        version = "1.0",
        api = "set",
        errcode = 0,
        sid = data.sid,
        result = { message = "All settings updated successfully" }
    }
    io.write(cjson.encode(result))
    io.flush()

    -- 根据 apply_mode 决定是否立即重启服务
    local apply_mode = get_current_apply_mode()
    if apply_mode == "immediate" then
        write_log("Applying configuration immediately: restarting services.")
        local restart_cmd = "nohup /etc/init.d/network restart >/dev/null 2>&1 &"
        local restart_ok = sys.call(restart_cmd)
        if restart_ok == 0 then
            write_log("Network restart command issued successfully.")
        else
            write_log("Failed to issue network restart command: " .. (restart_ok or "unknown error"))
        end

        local wifi_restart_ok = sys.call("wifi")
        if wifi_restart_ok == 0 then
            write_log("WiFi restart command issued successfully.")
        else
            write_log("Failed to issue WiFi restart command: " .. (wifi_restart_ok or "unknown error"))
        end

        io.write(cjson.encode({
            module = "client_mode",
            version = "1.0",
            api = "Client mode settings applied successfully. Services restarting...",
            errcode = 0,
            sid = data.sid
        }))
        io.flush()
    else
        write_log("Apply mode is deferred. Services will not be restarted automatically.")
        io.write(cjson.encode({
            module = "client_mode",
            version = "1.0",
            api = "Client mode settings saved. Apply deferred.",
            errcode = 0,
            sid = data.sid
        }))
        io.flush()
    end
end

-- 获取所有配置
function get_all_settings(data)
    local result = {
        module = "client_mode",
        version = "1.0",
        api = "get",
        errcode = 0,
        sid = data.sid,
        result = {
            roaming_settings = get_roaming_settings and get_roaming_settings() or {},
            lan_settings = get_lan_settings(),
            wan_settings = get_wan_settings(),
            connect_mode = get_connect_mode and get_connect_mode() or "-"
        }
    }
    write_log("get_all_settings result: " .. cjson.encode(result.result))
    io.write(cjson.encode(result))
    io.flush()
end

-- 设置连接方式和连接设置
function set_connection_settings(connection_type, connection)
    local band = connection.band or "2.4G"
    local sta_iface, wifi_device, wwan_iface, sta_ifname
    if band == "2.4G" then
        sta_iface = "sta0"
        wifi_device = "wifi0"
        wwan_iface = "wwan0"
        sta_ifname = "ath5"
    elseif band == "5G" then
        sta_iface = "sta1"
        wifi_device = "wifi1"
        wwan_iface = "wwan1"
        sta_ifname = "ath15"
    elseif band == "6G" then
        sta_iface = "sta3"
        wifi_device = "wifi2"
        wwan_iface = "wwan3"
        sta_ifname = "ath35"
    else
        local error_message = "Invalid band: " .. band
        write_log(error_message)
        io.write(cjson.encode({
            module = "client_mode",
            version = "1.0",
            api = "set",
            errcode = 5,
            result = { message = error_message }
        }))
        io.flush()
        return
    end

    -- 禁用所有 STA 接口，确保只启用目标 STA
    for _, s in ipairs({"sta0", "sta1", "sta3"}) do
        if s ~= sta_iface then
            uci:set("wireless", s, "disabled", "1")
        end
    end

    -- Encryption 字段映射
    local encryption_map = {
        ["WPA2 PSK (CCMP)"] = "psk2",
        ["WPA2 PSK (TKIP, CCMP)"] = "psk2",
        ["WPA PSK (TKIP)"] = "psk",
        ["mixed WPA/WPA2 PSK (CCMP)"] = "psk-mixed",
        ["mixed WPA/WPA2 PSK (TKIP, CCMP)"] = "psk-mixed",
        ["WPA3 SAE"] = "sae",
        ["WPA2/WPA3 SAE"] = "sae-mixed",
        ["none"] = "none"
    }
    local enc = connection.encryption or "none"
    local mapped_enc = encryption_map[enc] or enc

    -- 设置 STA 接口
    uci:set("wireless", sta_iface, "mode", "sta")
    uci:set("wireless", sta_iface, "ssid", connection.ssid or "")
    uci:set("wireless", sta_iface, "encryption", mapped_enc)
    if mapped_enc == "sae" or mapped_enc == "sae-mixed" then
        uci:set("wireless", sta_iface, "sae", "1")
    else
        uci:set("wireless", sta_iface, "sae", "0")
    end
    if mapped_enc ~= "none" then
        uci:set("wireless", sta_iface, "key", connection.key or "")
    end
    uci:set("wireless", sta_iface, "disabled", "0")
    uci:set("wireless", sta_iface, "ifname", sta_ifname)

    -- 设置发射功率
    if connection.txpower then
        uci:set("wireless", wifi_device, "txpower", connection.txpower)
    end

    -- 设置连接方式
    if connection_type == "wds" then
        uci:set("wireless", sta_iface, "network", "lan")
        uci:set("wireless", sta_iface, "wds", "1")
        uci:set("wireless", sta_iface, "extap", "0")
    elseif connection_type == "universal" then
        uci:set("wireless", sta_iface, "network", "lan")
        uci:set("wireless", sta_iface, "wds", "0")
        uci:set("wireless", sta_iface, "extap", "1")
    elseif connection_type == "nat" then
        uci:set("wireless", sta_iface, "network", "wwan")
        uci:set("wireless", sta_iface, "wds", "0")
        uci:set("wireless", sta_iface, "extap", "0")
    end

    -- 设置连接模式（点对点或漫游）
    if connection.mode == "p2p" and connection.peer_mac then
        uci:set("wireless", sta_iface, "bssid", connection.peer_mac)
    else
        uci:delete("wireless", sta_iface, "bssid")
    end

    -- 设置漫游参数
    if connection.mode == "roaming" then
        if connection.rssi_threshold and is_valid_rssi(connection.rssi_threshold) then
            uci:set("wireless", sta_iface, "rssi_threshold", connection.rssi_threshold)
        end
        if connection.efficient_roaming == "1" then
            if connection.roaming_rssi_diff and tonumber(connection.roaming_rssi_diff) >= 5 and tonumber(connection.roaming_rssi_diff) <= 20 then
                uci:set("wireless", sta_iface, "roaming_rssi_diff", connection.roaming_rssi_diff)
            else
                uci:set("wireless", sta_iface, "roaming_rssi_diff", "10") -- 默认值
            end
            -- 高效漫游：指定信道扫描（假设设备支持）
            if connection.scan_channels then
                uci:set("wireless", sta_iface, "scan_channels", connection.scan_channels)
            end
        else
            uci:delete("wireless", sta_iface, "roaming_rssi_diff")
            uci:delete("wireless", sta_iface, "scan_channels")
        end
    else
        uci:delete("wireless", sta_iface, "rssi_threshold")
        uci:delete("wireless", sta_iface, "roaming_rssi_diff")
        uci:delete("wireless", sta_iface, "scan_channels")
    end

    -- 动态选择信道
    local best_channel = "auto"
    local best_signal = -100
    local scan_interface = (band == "2.4G" and "ath0") or (band == "5G" and "ath10") or "ath30"
    local scan_result = sys.exec("iwinfo " .. scan_interface .. " scan")
    for line in scan_result:gmatch("[^\n]+") do
        if line:match("ESSID: \"" .. (connection.ssid or "") .. "\"") then
            local sig = tonumber(line:match("Signal: (%-%d+) dBm")) or -100
            local ch = line:match("Channel: (%d+)") or "auto"
            if sig > best_signal then
                best_signal = sig
                best_channel = ch
            end
        end
    end
    if best_channel == "auto" then
        if band == "2.4G" then best_channel = "1"
        elseif band == "5G" then best_channel = "36"
        elseif band == "6G" then best_channel = "1"
        end
    end
    uci:set("wireless", wifi_device, "channel", best_channel)

    uci:commit("wireless")
    return sta_ifname
end

-- 获取连接设置
function get_connection_settings()
    local mode = uci:get("system", "nhx", "mode") or "client"
    local ssid = ""
    local sta_iface, band, wwan_iface

    local band_map = {
        ["2.4G"] = {sta = "sta0", ifname = "ath5", wwan = "wwan0"},
        ["5G"]   = {sta = "sta1", ifname = "ath15", wwan = "wwan1"},
        ["6G"]   = {sta = "sta3", ifname = "ath35", wwan = "wwan3"}
    }
    for b, i in pairs(band_map) do
        local status_output = sys.exec("iwinfo " .. i.ifname .. " info")
        if status_output and status_output:match("ESSID:") then
            sta_iface = i.sta
            band = b
            wwan_iface = i.wwan
            ssid = uci:get("wireless", i.sta, "ssid") or ""
            break
        end
    end

    if not band then
        if uci:get("wireless", "sta0", "disabled") == "0" then
            sta_iface = "sta0"
            band = "2.4G"
            wwan_iface = "wwan0"
            ssid = uci:get("wireless", "sta0", "ssid") or ""
        elseif uci:get("wireless", "sta1", "disabled") == "0" then
            sta_iface = "sta1"
            band = "5G"
            wwan_iface = "wwan1"
            ssid = uci:get("wireless", "sta1", "ssid") or ""
        elseif uci:get("wireless", "sta3", "disabled") == "0" then
            sta_iface = "sta3"
            band = "6G"
            wwan_iface = "wwan3"
            ssid = uci:get("wireless", "sta3", "ssid") or ""
        else
            sta_iface = "sta0"
            band = "2.4G"
            wwan_iface = "wwan0"
            ssid = uci:get("wireless", "sta0", "ssid") or ""
        end
    end

    local mode_type = uci:get("wireless", sta_iface, "bssid") and "p2p" or "roaming"

    return {
        band = band,
        ssid = mode == "client" and ssid or "",
        encryption = uci:get("wireless", sta_iface, "encryption") or "none",
        key = uci:get("wireless", sta_iface, "key") or "",
        mode = mode_type,
        peer_mac = uci:get("wireless", sta_iface, "bssid") or "",
        rssi_threshold = uci:get("wireless", sta_iface, "rssi_threshold") or "-70",
        efficient_roaming = uci:get("wireless", sta_iface, "roaming_rssi_diff") and "1" or "0",
        roaming_rssi_diff = uci:get("wireless", sta_iface, "roaming_rssi_diff") or "10",
        scan_channels = uci:get("wireless", sta_iface, "scan_channels") or "",
        status = get_sta_status(wwan_iface)
    }
end

-- 设置外网配置（无线 NAT）
function set_wan_settings(wan_param, sta_ifname)
    local mode = wan_param.mode or "dhcp"
    uci:delete("network", "wwan")
    uci:set("network", "wwan", "interface")
    uci:set("network", "wwan", "ifname", sta_ifname or "ath5")
    uci:set("network", "wwan", "proto", mode)

    local dns1 = wan_param.dns1 or ""
    local dns2 = wan_param.dns2 or ""
    local dns_value = ""
    if dns1 ~= "" and is_valid_ip(dns1) then
        dns_value = dns1
    end
    if dns2 ~= "" and is_valid_ip(dns2) then
        if dns_value ~= "" then
            dns_value = dns_value .. " " .. dns2
        else
            dns_value = dns2
        end
    end

    if mode == "pppoe" then
        uci:set("network", "wwan", "username", wan_param.username or "")
        uci:set("network", "wwan", "password", wan_param.password or "")
        uci:set("network", "wwan", "auth_type", wan_param.auth_type or "PAP")
        uci:set("network", "wwan", "server", wan_param.server or "")
        if dns_value ~= "" then
            uci:set("network", "wwan", "dns", dns_value)
        else
            uci:delete("network", "wwan", "dns")
        end
    elseif mode == "static" then
        if not is_valid_ip(wan_param.ip) or not is_valid_netmask(wan_param.netmask) or not is_valid_gateway(wan_param.gateway) then
            local error_message = "Invalid IP, netmask, or gateway"
            write_log(error_message)
            io.write(cjson.encode({
                module = "client_mode",
                version = "1.0",
                api = "set",
                errcode = 5,
                result = { message = error_message }
            }))
            io.flush()
            return
        end
        uci:set("network", "wwan", "ipaddr", wan_param.ip)
        uci:set("network", "wwan", "netmask", wan_param.netmask)
        uci:set("network", "wwan", "gateway", wan_param.gateway)
        if dns_value ~= "" then
            uci:set("network", "wwan", "dns", dns_value)
        else
            uci:delete("network", "wwan", "dns")
        end
    elseif mode == "dhcp" then
        uci:delete("network", "wwan", "username")
        uci:delete("network", "wwan", "password")
        uci:delete("network", "wwan", "auth_type")
        uci:delete("network", "wwan", "server")
        if dns_value ~= "" then
            uci:set("network", "wwan", "dns", dns_value)
        else
            uci:delete("network", "wwan", "dns")
        end
    end

    uci:commit("network")
    write_log("WAN configuration set: proto=" .. mode .. ", ifname=" .. (sta_ifname or "ath5"))
end

-- 获取外网配置
function get_wan_settings()
    local connection_type = uci:get("system", "nhx", "client_mode") or "wds"
    if connection_type ~= "nat" then
        return { mode = "none" }
    end

    local mode = uci:get("network", "wwan", "proto") or "dhcp"
    local dns_servers = uci:get("network", "wwan", "dns") or ""
    local dns1, dns2 = "", ""
    local dns_list = {}
    for dns in dns_servers:gmatch("%S+") do
        table.insert(dns_list, dns)
    end
    dns1 = dns_list[1] or ""
    dns2 = dns_list[2] or ""

    local settings
    if mode == "pppoe" then
        settings = {
            mode = mode,
            auth_type = uci:get("network", "wwan", "auth_type") or "PAP",
            username = uci:get("network", "wwan", "username") or "",
            password = uci:get("network", "wwan", "password") or "",
            server = uci:get("network", "wwan", "server") or "",
            dns1 = dns1,
            dns2 = dns2
        }
    elseif mode == "static" then
        settings = {
            mode = mode,
            ip = uci:get("network", "wwan", "ipaddr") or "*************",
            netmask = uci:get("network", "wwan", "netmask") or "*************",
            gateway = uci:get("network", "wwan", "gateway") or "*************",
            dns1 = dns1,
            dns2 = dns2
        }
    elseif mode == "dhcp" then
        settings = {
            mode = mode,
            netmask = uci:get("network", "wwan", "netmask") or "*************",
            dns1 = dns1,
            dns2 = dns2
        }
    end

    return settings or { mode = "dhcp" }
end

-- 设置内网配置
function set_lan_settings(data, connection_type)
    local param = data or {}
    if connection_type == "wds" then
        if param.ip_allocation == "static" then
            if not is_valid_ip(param.ip) or not is_valid_netmask(param.netmask) then
                local error_message = "Invalid IP or netmask for static allocation"
                write_log(error_message)
                io.write(cjson.encode({
                    module = "client_mode",
                    version = "1.0",
                    api = "set",
                    errcode = 7,
                    result = { message = error_message }
                }))
                io.flush()
                return
            end
            uci:set("network", "lan", "proto", "static")
            uci:set("network", "lan", "ipaddr", param.ip or "*************")
            uci:set("network", "lan", "netmask", param.netmask or "*************")
            if param.gateway then uci:set("network", "lan", "gateway", param.gateway) end
            if param.dns then uci:set("network", "lan", "dns", param.dns) end
        elseif param.ip_allocation == "dhcp" then
            uci:set("network", "lan", "proto", "dhcp")
            uci:delete("network", "lan", "ipaddr")
            uci:delete("network", "lan", "netmask")
            uci:delete("network", "lan", "gateway")
            uci:delete("network", "lan", "dns")
        else
            uci:set("network", "lan", "proto", "dhcp")
        end
    else -- 万能桥接和无线 NAT 只支持静态 IP
        if not is_valid_ip(param.ip) or not is_valid_netmask(param.netmask) then
            local error_message = "Invalid IP or netmask for static allocation"
            write_log(error_message)
            io.write(cjson.encode({
                module = "client_mode",
                version = "1.0",
                api = "set",
                errcode = 7,
                result = { message = error_message }
            }))
            io.flush()
            return
        end
        uci:set("network", "lan", "proto", "static")
        uci:set("network", "lan", "ipaddr", param.ip or "*************")
        uci:set("network", "lan", "netmask", param.netmask or "*************")
        if param.gateway then uci:set("network", "lan", "gateway", param.gateway) end
        if param.dns then uci:set("network", "lan", "dns", param.dns) end
    end

    uci:commit("network")
end

-- 获取内网配置
function get_lan_settings()
    local connection_type = uci:get("system", "nhx", "client_mode") or "wds"
    local proto = uci:get("network", "lan", "proto") or "dhcp"
    local ip = uci:get("network", "lan", "ipaddr") or ""
    local netmask = uci:get("network", "lan", "netmask") or ""

    if ip == "" or netmask == "" then
        local ifconfig_output = io.popen("ifconfig br-lan 2>/dev/null"):read("*a") or ""
        if ifconfig_output ~= "" then
            local ifconfig_ip = ifconfig_output:match("inet addr:([%d%.]+)")
            local ifconfig_mask = ifconfig_output:match("Mask:([%d%.]+)")
            if ifconfig_ip then ip = ifconfig_ip end
            if ifconfig_mask then netmask = ifconfig_mask end
        end
    end

    if ip == "" then ip = "*************" end
    if netmask == "" then netmask = "*************" end

    return {
        ip_allocation = (connection_type == "wds" and proto) or "static",
        ip = ip,
        netmask = netmask,
        gateway = proto == "static" and (uci:get("network", "lan", "gateway") or "") or "",
        dns = proto == "static" and (uci:get("network", "lan", "dns") or "") or ""
    }
end

-- 扫描 SSID
function scan_ssid(data)
    write_log("scan_ssid called")
    local param = data.param or {}
    local band = param.band or "2.4G"
    local ifname = param.ifname
    local scan_ifname = nil
    local scan_results = {}
    -- 获取所有无线接口
    local ifaces = {}
    local iwinfo_list = sys.exec("iw dev 2>/dev/null | grep Interface | awk '{print $2}'")
    for ifn in iwinfo_list:gmatch("[^\n]+") do
        table.insert(ifaces, ifn)
    end
    write_log("[DEBUG] Existing wireless ifaces: " .. table.concat(ifaces, ", "))
    -- 自动选择接口
    if not ifname then
        if band == "5G" then
            for _, ifn in ipairs(ifaces) do if ifn == "ath10" then scan_ifname = "ath10" break end end
            if not scan_ifname then for _, ifn in ipairs(ifaces) do if ifn == "ath15" then scan_ifname = "ath15" break end end end
        elseif band == "2.4G" then
            for _, ifn in ipairs(ifaces) do if ifn == "ath0" then scan_ifname = "ath0" break end end
            if not scan_ifname then for _, ifn in ipairs(ifaces) do if ifn == "ath5" then scan_ifname = "ath5" break end end end
        elseif band == "6G" then
            for _, ifn in ipairs(ifaces) do if ifn == "ath30" then scan_ifname = "ath30" break end end
            if not scan_ifname then for _, ifn in ipairs(ifaces) do if ifn == "ath35" then scan_ifname = "ath35" break end end end
        end
        if not scan_ifname then scan_ifname = ifaces[1] end
    else
        scan_ifname = ifname
    end
    if not scan_ifname then
        write_log("[ERROR] No wireless interface found for scanning")
        io.write(cjson.encode({module = "client_mode", version = "1.0", api = "scan_ssid", errcode = 12, sid = data.sid, result = { message = "No wireless interface found for scanning" }}))
        io.flush()
        return
    end
    write_log("[DEBUG] scan_ifname selected: " .. scan_ifname)

    -- 临时启用对应的radio和STA接口以支持扫描
    local radio_device = nil
    local sta_interface = nil
    if band == "2.4G" then
        radio_device = "radio0"
        sta_interface = "sta0"
    elseif band == "5G" then
        radio_device = "radio1"
        sta_interface = "sta1"
    elseif band == "6G" then
        radio_device = "radio2"
        sta_interface = "sta3"
    end

    if radio_device and sta_interface then
        -- 记录原始状态
        local original_radio_disabled = uci:get("wireless", radio_device, "disabled") or "0"
        local original_sta_disabled = uci:get("wireless", sta_interface, "disabled") or "0"

        -- 临时启用radio和STA接口
        write_log("[DEBUG] Temporarily enabling " .. radio_device .. " and " .. sta_interface .. " for scanning")
        uci:set("wireless", radio_device, "disabled", "0")
        uci:set("wireless", sta_interface, "disabled", "0")
        uci:commit("wireless")

        -- 重启无线服务以应用更改
        sys.exec("wifi down")
        sys.exec("sleep 2")
        sys.exec("wifi up")
        sys.exec("sleep 5")

        -- 确保接口启动
        sys.exec("ifconfig " .. scan_ifname .. " up")
        sys.exec("sleep 2")

        -- 执行扫描
        local scan_cmd = "iwinfo " .. scan_ifname .. " scan"
        write_log("[DEBUG] scan command: " .. scan_cmd)
        local scan_result = sys.exec(scan_cmd)
        write_log("[DEBUG] scan result (first 200 chars):\n" .. (scan_result:sub(1,200) or "<empty>"))

        -- 恢复原始状态
        write_log("[DEBUG] Restoring original state for " .. radio_device .. " and " .. sta_interface)
        uci:set("wireless", radio_device, "disabled", original_radio_disabled)
        uci:set("wireless", sta_interface, "disabled", original_sta_disabled)
        uci:commit("wireless")

        -- 如果原来是禁用的，重启无线服务恢复状态
        if original_radio_disabled == "1" or original_sta_disabled == "1" then
            sys.exec("wifi down")
            sys.exec("sleep 2")
            sys.exec("wifi up")
            sys.exec("sleep 3")
        end

        -- 处理扫描结果
        if #scan_result == 0 then
            write_log("[ERROR] scan result is empty for interface: " .. scan_ifname)
            io.write(cjson.encode({module = "client_mode", version = "1.0", api = "scan_ssid", errcode = 13, sid = data.sid, result = { message = "Scan result is empty", scan_ifname = scan_ifname, scan_cmd = scan_cmd }}))
            io.flush()
            return
        end

        -- 解析扫描结果
        local ssid_list = {}
        local current_ap = nil
        for line in scan_result:gmatch("[^\n]+") do
            local address = line:match("Cell %d+ %- Address: ([%w:]+)")
            local essid = line:match("ESSID: \"(.+)\"")
            local signal = line:match("Signal: (%-%d+) dBm")
            local encryption = line:match("Encryption: (.+)")
            local channel = line:match("Channel: (%d+)")
            if address then
                if current_ap then table.insert(ssid_list, current_ap) end
                current_ap = { address = address }
            elseif essid and current_ap then
                current_ap.ssid = essid
            elseif signal and current_ap then
                current_ap.signal = signal
            elseif encryption and current_ap then
                current_ap.encryption = encryption
            elseif channel and current_ap then
                current_ap.channel = channel
            end
        end
        if current_ap then table.insert(ssid_list, current_ap) end

        write_log("SSID scan completed for " .. band .. ": " .. cjson.encode(ssid_list))
        io.write(cjson.encode({
            module = "client_mode",
            version = "1.0",
            api = "scan_ssid",
            errcode = 0,
            sid = data.sid,
            result = { band = band, scan_ifname = scan_ifname, ssids = ssid_list }
        }))
        io.flush()
        return
    end

    -- 如果无法确定radio设备，使用原来的逻辑
    sys.exec("ifconfig " .. scan_ifname .. " up")
    sys.exec("sleep 2")
    local scan_cmd = "iwinfo " .. scan_ifname .. " scan"
    write_log("[DEBUG] scan command: " .. scan_cmd)
    local scan_result = sys.exec(scan_cmd)
    write_log("[DEBUG] scan result (first 200 chars):\n" .. (scan_result:sub(1,200) or "<empty>"))
    if #scan_result == 0 then
        write_log("[ERROR] scan result is empty for interface: " .. scan_ifname)
        io.write(cjson.encode({module = "client_mode", version = "1.0", api = "scan_ssid", errcode = 13, sid = data.sid, result = { message = "Scan result is empty", scan_ifname = scan_ifname, scan_cmd = scan_cmd }}))
        io.flush()
        return
    end
    -- 解析扫描结果
    local ssid_list = {}
    local current_ap = nil
    for line in scan_result:gmatch("[^\n]+") do
        local address = line:match("Cell %d+ %- Address: ([%w:]+)")
        local essid = line:match("ESSID: \"(.+)\"")
        local signal = line:match("Signal: (%-%d+) dBm")
        local encryption = line:match("Encryption: (.+)")
        local channel = line:match("Channel: (%d+)")
        if address then
            if current_ap then table.insert(ssid_list, current_ap) end
            current_ap = { address = address }
        elseif essid and current_ap then
            current_ap.ssid = essid
        elseif signal and current_ap then
            current_ap.signal = signal
        elseif encryption and current_ap then
            current_ap.encryption = encryption
        elseif channel and current_ap then
            current_ap.channel = channel
        end
    end
    if current_ap then table.insert(ssid_list, current_ap) end
    write_log("SSID scan completed for " .. band .. ": " .. cjson.encode(ssid_list))
    io.write(cjson.encode({
        module = "client_mode",
        version = "1.0",
        api = "scan_ssid",
        errcode = 0,
        sid = data.sid,
        result = { band = band, scan_ifname = scan_ifname, ssids = ssid_list }
    }))
    io.flush()
end

-- 辅助函数：将逗号分隔的字符串拆分为表
local function split_channels_string(s)
    local t = {}
    for channel in string.gmatch(s, "[^,]+") do
        table.insert(t, tonumber(channel))
    end
    return t
end

-- 辅助函数：将表合并为逗号分隔的字符串
local function join_channels_table(t)
    local parts = {}
    for _, v in ipairs(t) do
        table.insert(parts, tostring(v))
    end
    return table.concat(parts, ",")
end

-- 主函数
local function run()
    write_log("Client Mode API started")
    route_api()
    write_log("Client Mode API finished")
end

run()