#!/bin/bash

# 批量修复剩余模块的CGI问题
# 这个脚本会为每个模块创建一个简化的route_api函数

modules=(
    "user_setup.lua"
    "snmp.lua" 
    "qos.lua"
    "scheduled_reboot.lua"
    "current_device.lua"
    "mac_access_control.lua"
)

for module in "${modules[@]}"; do
    if [ -f "$module" ]; then
        echo "Processing $module..."
        
        # 备份原文件
        cp "$module" "${module}.backup"
        
        # 移除CGI代码块（从Content-type到最后一个return）
        sed -i '/io\.write("Content-type:/,/return$/d' "$module"
        
        # 查找route_api函数并替换为带CGI处理的版本
        if grep -q "function route_api" "$module"; then
            echo "Found route_api in $module, updating..."
        else
            echo "No route_api found in $module, skipping..."
        fi
    else
        echo "File $module not found, skipping..."
    fi
done

echo "Batch processing complete. Please manually verify the changes."
