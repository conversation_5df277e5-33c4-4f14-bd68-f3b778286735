# WiFi7 Mesh 功能实现文档

## 概述

本文档描述了基于WiFi6 son_topo实现的WiFi7设备mesh功能。该实现提供了完整的mesh网络解决方案，包括主AP(CAP)和从AP(RE)模式配置、服务管理、状态监控等功能。

## 架构设计

### 模块结构

```
WIFI7_Lua_Step1/
├── mesh_config.lua      # Mesh配置管理模块
├── mesh_service.lua     # Mesh服务控制模块
├── mesh_cap_api.lua     # 主AP(CAP)配置API
├── mesh_re_api.lua      # 从AP(RE)配置API
├── mesh_monitor.lua     # 状态监控模块
├── mesh_api.lua         # 主API入口点
└── README_MESH.md       # 本文档
```

### 技术栈

- **基础框架**: OpenWrt + LuCI
- **配置管理**: UCI (Unified Configuration Interface)
- **Mesh协议**: IEEE 1905.1 + WiFi SON
- **服务组件**: wsplcd, hyd, repacd
- **编程语言**: Lua
- **数据格式**: JSON

## 功能特性

### 1. 基础配置管理 (mesh_config.lua)

- UCI配置文件管理
- Mesh网络参数配置
- 配置验证和持久化
- 默认值管理

**主要API:**
```lua
-- 获取mesh配置
local config = mesh_config.get_config()

-- 设置mesh配置
local success = mesh_config.set_config({
    meshssid = "MyMeshNetwork",
    meshpass = "password123",
    mesh_enabled = true
})

-- 启用mesh模式
mesh_config.enable_mesh("ssid", "password", is_cap_mode, gateway_config)
```

### 2. 服务控制 (mesh_service.lua)

- Mesh服务启停管理
- 无线接口配置
- CAP/RE模式切换
- 网络参数配置

**主要API:**
```lua
-- 启动mesh服务
mesh_service.start_mesh_services()

-- 配置无线接口
mesh_service.configure_wireless_for_mesh(ssid, password, is_cap_mode)

-- 启用mesh模式
mesh_service.enable_mesh_mode(ssid, password, is_cap_mode, gateway_config)
```

### 3. 主AP配置 (mesh_cap_api.lua)

- 网关模式配置
- WAN连接设置(DHCP/静态IP/PPPoE)
- Mesh网络创建
- 网络状态监控

**配置示例:**
```lua
local cap_config = {
    mesh_ssid = "MyMeshNetwork",
    mesh_password = "password123",
    gateway_mode = true,
    wan_type = "dhcp"  -- 或 "static", "pppoe"
}

local success, msg = mesh_cap_api.configure_cap_mode(cap_config)
```

### 4. 从AP配置 (mesh_re_api.lua)

- Mesh网络扫描
- 自动连接配置
- 信号优化
- 拓扑信息获取

**配置示例:**
```lua
-- 扫描可用mesh网络
local networks = mesh_re_api.scan_mesh_networks()

-- 配置RE模式
local re_config = {
    mesh_ssid = "MyMeshNetwork",
    mesh_password = "password123"
}

local success, msg = mesh_re_api.configure_re_mode(re_config)
```

### 5. 状态监控 (mesh_monitor.lua)

- 系统资源监控
- 无线统计信息
- Mesh拓扑监控
- 健康评分计算
- 性能统计

**监控API:**
```lua
-- 获取完整状态报告
local report = mesh_monitor.get_full_status_report()

-- 获取mesh拓扑
local topology = mesh_monitor.get_mesh_topology()

-- 获取无线统计
local stats = mesh_monitor.get_wireless_stats()
```

## 部署指南

### 1. 文件部署

将所有Lua文件复制到设备的适当位置：

```bash
# 复制到LuCI模块目录(当前在/www/cgi-bin/3onedata)
cp *.lua /usr/lib/lua/luci/

# 或复制到自定义目录
cp *.lua /usr/lib/lua/mesh/
```

### 2. CGI配置(已默认支持,无需配置)

在uhttpd配置中添加CGI支持：

```bash
# 编辑 /etc/config/uhttpd
config uhttpd main
    option cgi_prefix '/cgi-bin'
    option lua_prefix '/lua'
    option lua_handler '/usr/lib/lua/mesh/mesh_api.lua'
```

### 3. 权限设置

```bash
chmod +x /usr/lib/lua/mesh/*.lua
chown root:root /usr/lib/lua/mesh/*.lua
```

## API使用指南

### 1. 主API入口 (mesh_api.lua)

**获取概览信息:**
```bash
curl "http://***********/lua/mesh_api.lua?action=overview"
```

**快速配置向导:**
```bash
curl -X POST "http://***********/lua/mesh_api.lua" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "quick_setup",
    "config": {
      "mesh_ssid": "MyMeshNetwork",
      "mesh_password": "password123",
      "mode": "CAP",
      "gateway_mode": true,
      "wan_type": "dhcp"
    }
  }'
```

### 2. 主AP配置API

**配置主AP:**
```bash
curl -X POST "http://***********/lua/mesh_cap_api.lua" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "configure",
    "config": {
      "mesh_ssid": "MyMeshNetwork",
      "mesh_password": "password123",
      "gateway_mode": true,
      "wan_type": "static",
      "wan_config": {
        "ipaddr": "***********00",
        "netmask": "*************",
        "gateway": "***********",
        "dns": "******* *******"
      }
    }
  }'
```

### 3. 从AP配置API

**扫描mesh网络:**
```bash
curl "http://***********/lua/mesh_re_api.lua?action=scan"
```

**配置从AP:**
```bash
curl -X POST "http://***********/lua/mesh_re_api.lua" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "configure",
    "config": {
      "mesh_ssid": "MyMeshNetwork",
      "mesh_password": "password123"
    }
  }'
```

### 4. 状态监控API

**获取完整状态:**
```bash
curl "http://***********/lua/mesh_monitor.lua?action=status"
```

**获取拓扑信息:**
```bash
curl "http://***********/lua/mesh_monitor.lua?action=topology"
```

## 配置文件说明

### UCI配置结构

**Mesh配置 (/etc/config/mesh):**
```
config wirelessmesh 'mesh'
    option workmode 'mesh_gw'
    option meshmode 'control'
    option meshssid 'wifison'
    option meshpass '1234567890'
    option meshdhcpproxy 'ap'
    option changeflag '0'
    option mesh_enabled '1'
    option cap_mode '1'
    option gateway_mode '1'
    option wan_type 'dhcp'
```

**无线配置示例:**
```
config wifi-device 'radio0'
    option disabled '0'
    option dbdc_enable '1'
    option htmode 'HT20'

config wifi-iface 'wlan0'
    option device 'radio0'
    option network 'lan'
    option mode 'ap'
    option ssid 'MyMeshNetwork'
    option encryption 'psk2+ccmp'
    option sae '1'
    option key 'password123'
    option backhaul '1'
    option wnm '1'
    option ieee80211w '1'
```

## 故障排除

### 1. 常见问题

**Mesh服务无法启动:**
```bash
# 检查服务状态
ps | grep -E "wsplcd|hyd|repacd"

# 检查配置文件
uci show mesh
uci show wireless
uci show network

# 查看日志
logread | grep mesh
```

**无线接口配置错误:**
```bash
# 检查无线接口
iwconfig
ifconfig

# 重启无线
wifi down && wifi up
```

### 2. 调试模式

启用详细日志记录：
```bash
# 修改日志级别
uci set system.@system[0].log_level='7'
uci commit system

# 重启日志服务
/etc/init.d/log restart
```

### 3. 重置配置

```bash
# 通过API重置
curl -X POST "http://***********/lua/mesh_api.lua" \
  -H "Content-Type: application/json" \
  -d '{"action": "factory_reset"}'

# 手动重置
rm -f /etc/config/mesh
/etc/init.d/network restart
```

## 开发说明

### 1. 模块扩展

添加新功能时，遵循现有模块结构：

```lua
#!/usr/bin/lua

local uci = require("uci")
local json = require("json")

local new_module = {}

-- 检测CGI环境
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- 日志函数
local function log_message(level, message)
    if is_cgi() then
        os.execute(string.format("logger -t new_module '[%s] %s'", level, message))
    else
        print(string.format("[%s] %s", level, message))
    end
end

-- 功能实现
function new_module.new_function()
    -- 实现代码
end

-- CGI处理
function new_module.handle_cgi()
    -- CGI处理代码
end

-- 如果作为CGI运行，处理请求
if is_cgi() then
    new_module.handle_cgi()
end

return new_module
```

### 2. 测试建议

**单元测试:**
```bash
# 在设备上测试模块
lua -e "
local mesh_config = require('mesh_config')
local config = mesh_config.get_config()
print(require('json').encode(config))
"
```

**集成测试:**
```bash
# 测试完整流程
curl -X POST "http://***********/lua/mesh_api.lua" \
  -H "Content-Type: application/json" \
  -d '{"action": "quick_setup", "config": {...}}'
```

## 版本历史

- **v1.0.0** (2025-01-07): 初始版本
  - 基础mesh配置管理
  - CAP/RE模式支持
  - 状态监控功能
  - 完整的API接口

## 技术支持

如有问题或建议，请联系WiFi7开发团队。