﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style>
.AlignLeft { text-align: left; }
.AlignCenter { text-align: center; }
.AlignRight { text-align: right; }
body { font-family: sans-serif; font-size: 11pt; }
img.AutoScale { max-width: 100%; max-height: 100%; }
td { vertical-align: top; padding-left: 4px; padding-right: 4px; }

tr.SectionGap td { font-size: 4px; border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionBegin td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionMiddle td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SubsectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
tr.SubsectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
table.fc { border-top: 1px solid Black; border-left: 1px solid Black; width: 100%; font-family: monospace; font-size: 10pt; }
td.TextItemInsigMod { color: #000000; background-color: #EEEEFF; }
td.TextItemInsigOrphan { color: #000000; background-color: #FAEEFF; }
td.TextItemNum { color: #696969; background-color: #F0F0F0; }
td.TextItemSame { color: #000000; background-color: #FFFFFF; }
td.TextItemSigMod { color: #000000; background-color: #FFE3E3; }
td.TextItemSigOrphan { color: #000000; background-color: #F1E3FF; }
.TextSegInsigDiff { color: #0000FF; }
.TextSegReplacedDiff { color: #0000FF; font-style: italic; }
.TextSegSigDiff { color: #FF0000; }
td.TextItemInsigAdd { color: #000000; background-color: #EEEEFF; }
td.TextItemInsigDel { color: #000000; background-color: #EEEEFF; text-decoration: line-through; }
td.TextItemSigAdd { color: #000000; background-color: #FFE3E3; }
td.TextItemSigDel { color: #000000; background-color: #FFE3E3; text-decoration: line-through; }
</style>
<title>vlan.lua比较</title>
</head>
<body>
vlan.lua比较<br/>
已产生: 2025/7/8 14:56:26<br/>
&nbsp; &nbsp;
<br/>
模式:&nbsp; 全部, 带上下文 &nbsp;
<br/>
左边文件: E:\QSDK_Code\2_step\WIFI7_Lua_Step1\vlan.lua &nbsp;
<br/>
右边文件: C:\Users\<USER>\Downloads\vlan_4.lua &nbsp;
<br/>
<table class="fc" cellspacing="0" cellpadding="0">
<tr class="SectionBegin">
<td class="TextItemSame">#!/usr/bin/lua</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">#!/usr/bin/lua</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Copyright (c) 2024 The Linux Foundation. All rights reserved.</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Copyright (c) 2024 The Linux Foundation. All rights reserved.</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Not a Contribution.</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Not a Contribution.</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Copyright 2024 xiayan &lt;<EMAIL>&gt;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Copyright 2024 xiayan &lt;<EMAIL>&gt;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Licensed to the public under the Apache License 2.0.</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Licensed to the public under the Apache License 2.0.</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Lua环境：此脚本在OpenWRT的Lua环境中运行，确保有必要的Lua库，如luci.sys, luci.jsonc和luci.uci</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Lua环境：此脚本在OpenWRT的Lua环境中运行，确保有必要的Lua库，如luci.sys, luci.jsonc和luci.uci</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- UCI：这个脚本使用了uci库来直接操作配置文件。这比直接读写文件更为安全和标准�?</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- UCI：这个脚本使用了uci库来直接操作配置文件。这比直接读写文件更为安全和标准�?</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- 安全性：确保处理用户输入时，进行了必要的验证和清理，以防止注入攻�?</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- 安全性：确保处理用户输入时，进行了必要的验证和清理，以防止注入攻�?</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- 性能：由于脚本每次都需要读取和写入配置文件，对于频繁调用的API，可以考虑优化，例如通过缓存减少IO操作�?</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- 性能：由于脚本每次都需要读取和写入配置文件，对于频繁调用的API，可以考虑优化，例如通过缓存减少IO操作�?</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- 权限：确保脚本以必要的权限运行，修改网络配置文件和重启网络服务需要root权限�?</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- 权限：确保脚本以必要的权限运行，修改网络配置文件和重启网络服务需要root权限�?</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- 引入模块</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- 引入模块</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local cjson = require(&quot;cjson.safe&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local cjson = require(&quot;cjson.safe&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local sys = require(&quot;luci.sys&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local sys = require(&quot;luci.sys&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local uci = require(&quot;luci.model.uci&quot;).cursor()</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local uci = require(&quot;luci.model.uci&quot;).cursor()</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- local config_apply_mode = require(&quot;config_apply_mode&quot;) -- Use the new module name</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- local config_apply_mode = require(&quot;config_apply_mode&quot;) -- Use the new module name</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local log_file = &quot;/tmp/vlan_config.log&quot; -- 日志文件路径</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local log_file = &quot;/tmp/vlan_config.log&quot; -- 日志文件路径</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- 日志写入函数</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- 日志写入函数</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local function write_log(message)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local function write_log(message)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local file = io.open(log_file, &quot;a&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local file = io.open(log_file, &quot;a&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if file then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if file then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; file:write(os.date(&quot;[%Y-%m-%d %H:%M:%S] &quot;), message, &quot;\n&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; file:write(os.date(&quot;[%Y-%m-%d %H:%M:%S] &quot;), message, &quot;\n&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; file:close()</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; file:close()</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; print(&quot;Failed to open log file: &quot; .. log_file)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; print(&quot;Failed to open log file: &quot; .. log_file)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- 获取当前应用模式的辅助函数</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- 获取当前应用模式的辅助函数</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local function get_current_apply_mode()</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local function get_current_apply_mode()</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local mode_file = &quot;/etc/config_apply_mode_status&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local mode_file = &quot;/etc/config_apply_mode_status&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local f = io.open(mode_file, &quot;r&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local f = io.open(mode_file, &quot;r&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if f then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if f then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local mode = f:read(&quot;*l&quot;) -- Read the first line</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local mode = f:read(&quot;*l&quot;) -- Read the first line</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; f:close()</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; f:close()</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; mode = mode and mode:gsub(&quot;^%s*(.-)%s*$&quot;, &quot;%1&quot;) -- Trim whitespace</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; mode = mode and mode:gsub(&quot;^%s*(.-)%s*$&quot;, &quot;%1&quot;) -- Trim whitespace</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if mode == &quot;immediate&quot; or mode == &quot;deferred&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if mode == &quot;immediate&quot; or mode == &quot;deferred&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Read apply_mode from &quot; .. mode_file .. &quot;: &quot; .. mode)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Read apply_mode from &quot; .. mode_file .. &quot;: &quot; .. mode)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return mode</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return mode</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Invalid content in &quot; .. mode_file .. &quot;: '&quot; .. (mode or &quot;nil&quot;) .. &quot;'. Defaulting to 'immediate'.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Invalid content in &quot; .. mode_file .. &quot;: '&quot; .. (mode or &quot;nil&quot;) .. &quot;'. Defaulting to 'immediate'.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return &quot;immediate&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return &quot;immediate&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Could not open &quot; .. mode_file .. &quot;. Defaulting to 'immediate'.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Could not open &quot; .. mode_file .. &quot;. Defaulting to 'immediate'.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; return &quot;immediate&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; return &quot;immediate&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">-+</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">设置</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">HTTP</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">响应?</span></td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">io.write(&quot;Content-type:</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">application/json\nPragma:</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">no-cache\n\n&quot;)</span></td>
</tr>
<tr class="SectionAll">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">-+</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">获取</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">数据长度</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POSTLength</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">tonumber(os.getenv(&quot;CONTENT_LENGTH&quot;))</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">or</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">0</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;&quot;</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">读取</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">数据</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POSTLength</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&gt;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">0</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">io.read(POSTLength)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(&quot;Received</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">data:</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">else</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(&quot;No</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">data</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">received</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">or</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">CONTENT_LENGTH</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">is</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">0&quot;)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">确保读取成功</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">not</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">or</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">==</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">error_message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;Failed</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">to</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">retrieve</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">data&quot;</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(error_message)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">io.write(cjson.encode({</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">module</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;vlan&quot;,</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">version</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;1.0&quot;,</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">errcode</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">1,</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">result</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">{</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">error_message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">}</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">}))</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">io.flush()</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">return</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">解析</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">数据�?JSON</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">requestData</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">cjson.decode(POST)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">not</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">requestData</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">error_message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;Invalid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">JSON</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">input&quot;</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(error_message)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">io.write(cjson.encode({</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">module</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;vlan&quot;,</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">version</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;1.0&quot;,</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">errcode</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">2,</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">result</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">{</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">error_message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">}</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">}))</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">io.flush()</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">return</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">检查请求格�?</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">not</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">requestData.version</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">or</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">not</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">requestData.sid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">or</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">not</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">requestData.module</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">or</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">not</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">requestData.api</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">error_message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;Invalid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">request</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">format&quot;</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(error_message)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">io.write(cjson.encode({</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">module</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;vlan&quot;,</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">version</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;1.0&quot;,</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">errcode</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">3,</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">result</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">{</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">error_message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">}</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">}))</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">io.flush()</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">return</span></td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Helper function to add an item to a UCI list (simulates uci:add_list if not available)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Helper function to add an item to a UCI list (simulates uci:add_list if not available)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local function add_uci_list_item(package, section_name, option_name, value_to_add)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local function add_uci_list_item(package, section_name, option_name, value_to_add)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_list = uci:get_list(package, section_name, option_name)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_list = uci:get_list(package, section_name, option_name)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local found = false</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local found = false</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, v in ipairs(current_list) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, v in ipairs(current_list) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if v == value_to_add then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if v == value_to_add then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; found = true</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; found = true</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; break</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; break</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not found then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not found then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.insert(current_list, value_to_add)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.insert(current_list, value_to_add)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local success, err = pcall(function() uci:set(package, section_name, option_name, current_list) end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local success, err = pcall(function() uci:set(package, section_name, option_name, current_list) end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if success then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if success then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Added '&quot; .. value_to_add .. &quot;' to &quot; .. package .. &quot;.&quot; .. section_name .. &quot;.&quot; .. option_name)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Added '&quot; .. value_to_add .. &quot;' to &quot; .. package .. &quot;.&quot; .. section_name .. &quot;.&quot; .. option_name)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Error adding '&quot; .. value_to_add .. &quot;' to &quot; .. package .. &quot;.&quot; .. section_name .. &quot;.&quot; .. option_name .. &quot;: &quot; .. (err or &quot;unknown error&quot;))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Error adding '&quot; .. value_to_add .. &quot;' to &quot; .. package .. &quot;.&quot; .. section_name .. &quot;.&quot; .. option_name .. &quot;: &quot; .. (err or &quot;unknown error&quot;))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Value '&quot; .. value_to_add .. &quot;' already exists in &quot; .. package .. &quot;.&quot; .. section_name .. &quot;.&quot; .. option_name .. &quot;. Skipping add.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Value '&quot; .. value_to_add .. &quot;' already exists in &quot; .. package .. &quot;.&quot; .. section_name .. &quot;.&quot; .. option_name .. &quot;. Skipping add.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Helper function to remove an item from a UCI list (simulates uci:del_list if not available)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Helper function to remove an item from a UCI list (simulates uci:del_list if not available)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local function del_uci_list_item(package, section_name, option_name, value_to_remove)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local function del_uci_list_item(package, section_name, option_name, value_to_remove)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_list = uci:get_list(package, section_name, option_name)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_list = uci:get_list(package, section_name, option_name)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local new_list = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local new_list = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local removed = false</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local removed = false</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, v in ipairs(current_list) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, v in ipairs(current_list) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if v == value_to_remove and not removed then -- Only remove the first occurrence for now</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if v == value_to_remove and not removed then -- Only remove the first occurrence for now</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; removed = true</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; removed = true</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_list, v)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_list, v)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if removed then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if removed then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local success, err</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local success, err</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if #new_list == 0 then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if #new_list == 0 then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- If the list becomes empty after removal, delete the option</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- If the list becomes empty after removal, delete the option</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; success, err = pcall(function() uci:delete(package, section_name, option_name) end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; success, err = pcall(function() uci:delete(package, section_name, option_name) end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if success then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if success then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Removed option &quot; .. option_name .. &quot; from &quot; .. package .. &quot;.&quot; .. section_name .. &quot; as list became empty after removing '&quot; .. value_to_remove .. &quot;'&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Removed option &quot; .. option_name .. &quot; from &quot; .. package .. &quot;.&quot; .. section_name .. &quot; as list became empty after removing '&quot; .. value_to_remove .. &quot;'&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Error deleting option &quot; .. option_name .. &quot; from &quot; .. package .. &quot;.&quot; .. section_name .. &quot;: &quot; .. (err or &quot;unknown error&quot;))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Error deleting option &quot; .. option_name .. &quot; from &quot; .. package .. &quot;.&quot; .. section_name .. &quot;: &quot; .. (err or &quot;unknown error&quot;))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Otherwise, set the updated list</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Otherwise, set the updated list</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; success, err = pcall(function() uci:set(package, section_name, option_name, new_list) end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; success, err = pcall(function() uci:set(package, section_name, option_name, new_list) end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if success then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if success then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Removed '&quot; .. value_to_remove .. &quot;' from &quot; .. package .. &quot;.&quot; .. section_name .. &quot;.&quot; .. option_name .. &quot;. New list: &quot; .. cjson.encode(new_list))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Removed '&quot; .. value_to_remove .. &quot;' from &quot; .. package .. &quot;.&quot; .. section_name .. &quot;.&quot; .. option_name .. &quot;. New list: &quot; .. cjson.encode(new_list))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Error setting new list for &quot; .. package .. &quot;.&quot; .. section_name .. &quot;.&quot; .. option_name .. &quot;: &quot; .. (err or &quot;unknown error&quot;))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Error setting new list for &quot; .. package .. &quot;.&quot; .. section_name .. &quot;.&quot; .. option_name .. &quot;: &quot; .. (err or &quot;unknown error&quot;))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Value '&quot; .. value_to_remove .. &quot;' not found in &quot; .. package .. &quot;.&quot; .. section_name .. &quot;.&quot; .. option_name .. &quot;. Skipping remove.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Value '&quot; .. value_to_remove .. &quot;' not found in &quot; .. package .. &quot;.&quot; .. section_name .. &quot;.&quot; .. option_name .. &quot;. Skipping remove.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Helper function to create or get a network.switch_vlan section</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Helper function to create or get a network.switch_vlan section</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local function ensure_switch_vlan(vid)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local function ensure_switch_vlan(vid)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local switch_vlan_section_name = nil</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local switch_vlan_section_name = nil</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;switch_vlan&quot;, function(section)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;switch_vlan&quot;, function(section)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if tonumber(section.vlan) == tonumber(vid) then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if tonumber(section.vlan) == tonumber(vid) then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; switch_vlan_section_name = section[&quot;.name&quot;]</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; switch_vlan_section_name = section[&quot;.name&quot;]</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return false -- Stop iteration</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return false -- Stop iteration</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not switch_vlan_section_name then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not switch_vlan_section_name then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; switch_vlan_section_name = uci:add(&quot;network&quot;, &quot;switch_vlan&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; switch_vlan_section_name = uci:add(&quot;network&quot;, &quot;switch_vlan&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, switch_vlan_section_name, &quot;device&quot;, &quot;switch1&quot;) -- Assuming switch1 is the device name</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, switch_vlan_section_name, &quot;device&quot;, &quot;switch1&quot;) -- Assuming switch1 is the device name</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, switch_vlan_section_name, &quot;vlan&quot;, vid)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, switch_vlan_section_name, &quot;vlan&quot;, vid)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Created new network.switch_vlan section for VID: &quot; .. vid .. &quot; (UCI name: &quot; .. switch_vlan_section_name .. &quot;)&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Created new network.switch_vlan section for VID: &quot; .. vid .. &quot; (UCI name: &quot; .. switch_vlan_section_name .. &quot;)&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; return switch_vlan_section_name</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; return switch_vlan_section_name</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Helper function to update ports in a network.switch_vlan section.</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Helper function to update ports in a network.switch_vlan section.</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local function update_switch_vlan_ports(vid, port_index, is_tagged)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local function update_switch_vlan_ports(vid, port_index, is_tagged)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local switch_vlan_section_name = ensure_switch_vlan(vid)&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local switch_vlan_section_name = ensure_switch_vlan(vid)&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_ports_str = uci:get(&quot;network&quot;, switch_vlan_section_name, &quot;ports&quot;) or &quot;&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_ports_str = uci:get(&quot;network&quot;, switch_vlan_section_name, &quot;ports&quot;) or &quot;&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_ports_list = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_ports_list = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for p in string.gmatch(current_ports_str, &quot;[^%s]+&quot;) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for p in string.gmatch(current_ports_str, &quot;[^%s]+&quot;) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.insert(current_ports_list, p)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.insert(current_ports_list, p)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local new_ports_list = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local new_ports_list = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local port_entry_to_add = tostring(port_index) .. (is_tagged and &quot;t&quot; or &quot;&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local port_entry_to_add = tostring(port_index) .. (is_tagged and &quot;t&quot; or &quot;&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local port_entry_to_remove_opposite = tostring(port_index) .. (is_tagged and &quot;&quot; or &quot;t&quot;)&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local port_entry_to_remove_opposite = tostring(port_index) .. (is_tagged and &quot;&quot; or &quot;t&quot;)&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local added = false</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local added = false</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, p in ipairs(current_ports_list) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, p in ipairs(current_ports_list) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if p == port_entry_to_remove_opposite then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if p == port_entry_to_remove_opposite then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Removing opposite port type from switch_vlan &quot; .. vid .. &quot;: &quot; .. p)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Removing opposite port type from switch_vlan &quot; .. vid .. &quot;: &quot; .. p)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; elseif p == port_entry_to_add then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; elseif p == port_entry_to_add then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_ports_list, p)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_ports_list, p)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; added = true</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; added = true</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_ports_list, p)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_ports_list, p)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not added then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not added then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_ports_list, port_entry_to_add)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_ports_list, port_entry_to_add)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Ensure '0t' (CPU port tagged) comes first, then sort others</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Ensure '0t' (CPU port tagged) comes first, then sort others</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local has_0t = false</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local has_0t = false</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local other_ports = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local other_ports = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, p in ipairs(new_ports_list) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, p in ipairs(new_ports_list) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if p == &quot;0t&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if p == &quot;0t&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; has_0t = true</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; has_0t = true</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(other_ports, p)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(other_ports, p)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; table.sort(other_ports, function(a, b) return tonumber(string.match(a, &quot;%d+&quot;)) &lt; tonumber(string.match(b, &quot;%d+&quot;)) end) -- Sort numerically by port index</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; table.sort(other_ports, function(a, b) return tonumber(string.match(a, &quot;%d+&quot;)) &lt; tonumber(string.match(b, &quot;%d+&quot;)) end) -- Sort numerically by port index</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local final_ports_str = has_0t and &quot;0t &quot; .. table.concat(other_ports, &quot; &quot;) or table.concat(other_ports, &quot; &quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local final_ports_str = has_0t and &quot;0t &quot; .. table.concat(other_ports, &quot; &quot;) or table.concat(other_ports, &quot; &quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; final_ports_str = final_ports_str:gsub(&quot;^[\\s]+&quot;, &quot;&quot;) -- Trim leading spaces</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; final_ports_str = final_ports_str:gsub(&quot;^[\\s]+&quot;, &quot;&quot;) -- Trim leading spaces</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:set(&quot;network&quot;, switch_vlan_section_name, &quot;ports&quot;, final_ports_str)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:set(&quot;network&quot;, switch_vlan_section_name, &quot;ports&quot;, final_ports_str)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Updated network.switch_vlan &quot; .. vid .. &quot; ports to: '&quot; .. final_ports_str .. &quot;'&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Updated network.switch_vlan &quot; .. vid .. &quot; ports to: '&quot; .. final_ports_str .. &quot;'&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Helper function to remove a specific port entry (tagged or untagged) from a network.switch_vlan section.</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Helper function to remove a specific port entry (tagged or untagged) from a network.switch_vlan section.</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local function remove_switch_vlan_port(vid, port_index, is_tagged)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local function remove_switch_vlan_port(vid, port_index, is_tagged)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local switch_vlan_section_name = uci:get_first(&quot;network&quot;, &quot;switch_vlan&quot;, { vlan = tostring(vid) })</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local switch_vlan_section_name = uci:get_first(&quot;network&quot;, &quot;switch_vlan&quot;, { vlan = tostring(vid) })</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not switch_vlan_section_name then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not switch_vlan_section_name then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Info: network.switch_vlan section for VID &quot; .. vid .. &quot; not found. Nothing to remove for port &quot; .. port_index .. &quot;.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Info: network.switch_vlan section for VID &quot; .. vid .. &quot; not found. Nothing to remove for port &quot; .. port_index .. &quot;.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; return</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; return</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_ports_str = uci:get(&quot;network&quot;, switch_vlan_section_name, &quot;ports&quot;) or &quot;&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_ports_str = uci:get(&quot;network&quot;, switch_vlan_section_name, &quot;ports&quot;) or &quot;&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_ports_list = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_ports_list = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for p in string.gmatch(current_ports_str, &quot;[^%s]+&quot;) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for p in string.gmatch(current_ports_str, &quot;[^%s]+&quot;) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.insert(current_ports_list, p)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.insert(current_ports_list, p)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local new_ports_list = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local new_ports_list = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local port_entry_to_remove = tostring(port_index) .. (is_tagged and &quot;t&quot; or &quot;&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local port_entry_to_remove = tostring(port_index) .. (is_tagged and &quot;t&quot; or &quot;&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local removed_count = 0</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local removed_count = 0</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, p in ipairs(current_ports_list) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, p in ipairs(current_ports_list) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if p == port_entry_to_remove then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if p == port_entry_to_remove then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; removed_count = removed_count + 1</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; removed_count = removed_count + 1</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_ports_list, p)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_ports_list, p)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if removed_count &gt; 0 then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if removed_count &gt; 0 then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local has_0t = false</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local has_0t = false</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local other_ports = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local other_ports = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; for _, p in ipairs(new_ports_list) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; for _, p in ipairs(new_ports_list) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if p == &quot;0t&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if p == &quot;0t&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; has_0t = true</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; has_0t = true</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(other_ports, p)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(other_ports, p)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.sort(other_ports, function(a, b) return tonumber(string.match(a, &quot;%d+&quot;)) &lt; tonumber(string.match(b, &quot;%d+&quot;)) end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.sort(other_ports, function(a, b) return tonumber(string.match(a, &quot;%d+&quot;)) &lt; tonumber(string.match(b, &quot;%d+&quot;)) end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local final_ports_str = has_0t and &quot;0t &quot; .. table.concat(other_ports, &quot; &quot;) or table.concat(other_ports, &quot; &quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local final_ports_str = has_0t and &quot;0t &quot; .. table.concat(other_ports, &quot; &quot;) or table.concat(other_ports, &quot; &quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; final_ports_str = final_ports_str:gsub(&quot;^[\\s]+&quot;, &quot;&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; final_ports_str = final_ports_str:gsub(&quot;^[\\s]+&quot;, &quot;&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, switch_vlan_section_name, &quot;ports&quot;, final_ports_str)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, switch_vlan_section_name, &quot;ports&quot;, final_ports_str)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Removed &quot; .. removed_count .. &quot; instances of port &quot; .. port_entry_to_remove .. &quot; from network.switch_vlan &quot; .. vid .. &quot;. New ports: '&quot; .. final_ports_str .. &quot;'&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Removed &quot; .. removed_count .. &quot; instances of port &quot; .. port_entry_to_remove .. &quot; from network.switch_vlan &quot; .. vid .. &quot;. New ports: '&quot; .. final_ports_str .. &quot;'&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Info: Port &quot; .. port_entry_to_remove .. &quot; not found in network.switch_vlan &quot; .. vid .. &quot;. Nothing to remove.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Info: Port &quot; .. port_entry_to_remove .. &quot; not found in network.switch_vlan &quot; .. vid .. &quot;. Nothing to remove.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Utility function to check if a value exists in a table (array)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Utility function to check if a value exists in a table (array)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">function table.contains(tab, val)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">function table.contains(tab, val)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, v in ipairs(tab) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, v in ipairs(tab) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if v == val then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if v == val then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return true</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return true</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; return false</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; return false</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Helper to get the radio band from a wifi-device UCI section</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Helper to get the radio band from a wifi-device UCI section</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local function get_radio_band(radio_uci_section_name)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local function get_radio_band(radio_uci_section_name)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local hwmode = uci:get(&quot;wireless&quot;, radio_uci_section_name, &quot;hwmode&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local hwmode = uci:get(&quot;wireless&quot;, radio_uci_section_name, &quot;hwmode&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if hwmode then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if hwmode then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if string.match(hwmode, &quot;a$&quot;) then -- e.g., &quot;11a&quot;, &quot;11ac&quot;, &quot;11axa&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if string.match(hwmode, &quot;a$&quot;) then -- e.g., &quot;11a&quot;, &quot;11ac&quot;, &quot;11axa&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return &quot;5G&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return &quot;5G&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; elseif string.match(hwmode, &quot;g$&quot;) then -- e.g., &quot;11g&quot;, &quot;11n&quot;, &quot;11axg&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; elseif string.match(hwmode, &quot;g$&quot;) then -- e.g., &quot;11g&quot;, &quot;11n&quot;, &quot;11axg&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return &quot;2.4G&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return &quot;2.4G&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; elseif string.match(hwmode, &quot;be$&quot;) then -- For WiFi 7 (802.11be) in 6GHz</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; elseif string.match(hwmode, &quot;be$&quot;) then -- For WiFi 7 (802.11be) in 6GHz</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return &quot;6G&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return &quot;6G&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; return &quot;Unknown Band&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; return &quot;Unknown Band&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Helper to get interface mappings (GUI name to internal details)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Helper to get interface mappings (GUI name to internal details)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local function get_interface_mappings()</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local function get_interface_mappings()</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local mappings = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local mappings = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_physical_ifname_base = &quot;eth1&quot;&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_physical_ifname_base = &quot;eth1&quot;&nbsp;</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_cpu_vlan_ifname_default = &quot;eth1.1&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_cpu_vlan_ifname_default = &quot;eth1.1&quot;</td>
</tr>
<tr class="SectionAll">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">-+</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_vlan_ifname_default=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;eth1.2&quot;</span></td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- NOTE: lan_physical_switch_port_index assumes eth1 is connected to switch port 1.&nbsp;</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- NOTE: lan_physical_switch_port_index assumes eth1 is connected to switch port 1.&nbsp;</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- This might need dynamic discovery if the mapping is not fixed across devices.</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- This might need dynamic discovery if the mapping is not fixed across devices.</td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSigMod">&nbsp;&nbsp;&nbsp; local lan_physical_switch_port_index = &quot;<span class="TextSegSigDiff">1</span>&quot;</td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="TextItemSigMod">&nbsp;&nbsp;&nbsp; local lan_physical_switch_port_index = &quot;<span class="TextSegSigDiff">4</span>&quot;</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;3&quot;</span></td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- LAN Port (GUI &quot;LAN1&quot; corresponds to physical switch port 1 and CPU's eth1.1)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- LAN Port (GUI &quot;LAN1&quot; corresponds to physical switch port 1 and CPU's eth1.1)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; mappings[&quot;LAN1&quot;] = {</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; mappings[&quot;LAN1&quot;] = {</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; type = &quot;lan_physical_port&quot;, -- Indicates this is a physical switch port</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; type = &quot;lan_physical_port&quot;, -- Indicates this is a physical switch port</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; cpu_vlan_ifname_default = lan_cpu_vlan_ifname_default, -- The default CPU-side VLAN interface used by 'lan' bridge</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; cpu_vlan_ifname_default = lan_cpu_vlan_ifname_default, -- The default CPU-side VLAN interface used by 'lan' bridge</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; physical_switch_port_index = lan_physical_switch_port_index,</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; physical_switch_port_index = lan_physical_switch_port_index,</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; description = &quot;Main LAN Port&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; description = &quot;Main LAN Port&quot;</td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">-+</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">}</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">mappings[&quot;LAN2&quot;]</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">{</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">type</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;lan2_physical_port&quot;,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">Indicates</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">this</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">is</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">a</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">physical</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">switch</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">port</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">lan_vlan2_ifname_default</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_vlan_ifname_default,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">The</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">default</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">CPU-side</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">VLAN</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">interface</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">used</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">by</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">'lan'</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">bridge</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">physical_lan2_switch_port_index</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">,</span></td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">description</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;Secondary</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">LAN</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">Port&quot;</span></td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; }</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; }</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Wireless Interfaces (dynamic discovery and descriptive naming)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Wireless Interfaces (dynamic discovery and descriptive naming)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;wireless&quot;, &quot;wifi-iface&quot;, function(section)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;wireless&quot;, &quot;wifi-iface&quot;, function(section)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local uci_section_name = section[&quot;.name&quot;]</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local uci_section_name = section[&quot;.name&quot;]</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local ifname = section.ifname</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local ifname = section.ifname</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local mode = section.mode or &quot;ap&quot; -- Default mode to AP if not specified</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local mode = section.mode or &quot;ap&quot; -- Default mode to AP if not specified</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local ssid = section.ssid or &quot;&quot;&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local ssid = section.ssid or &quot;&quot;&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local device_section_name = section.device -- e.g., &quot;wifi0&quot;, &quot;wifi1&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local device_section_name = section.device -- e.g., &quot;wifi0&quot;, &quot;wifi1&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local disabled = section.disabled or &quot;0&quot; -- Read disabled option</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local disabled = section.disabled or &quot;0&quot; -- Read disabled option</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not ifname then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not ifname then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Warning: wifi-iface section &quot; .. uci_section_name .. &quot; has no ifname. Skipping.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Warning: wifi-iface section &quot; .. uci_section_name .. &quot; has no ifname. Skipping.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return -- Skip this section if no ifname</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return -- Skip this section if no ifname</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if disabled == &quot;1&quot; then -- Filter out disabled interfaces</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if disabled == &quot;1&quot; then -- Filter out disabled interfaces</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Info: Skipping disabled wifi-iface section: &quot; .. uci_section_name .. &quot; (ifname: &quot; .. ifname .. &quot;)&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Info: Skipping disabled wifi-iface section: &quot; .. uci_section_name .. &quot; (ifname: &quot; .. ifname .. &quot;)&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local band_info = &quot;Unknown Band&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local band_info = &quot;Unknown Band&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if device_section_name then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if device_section_name then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; band_info = get_radio_band(device_section_name)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; band_info = get_radio_band(device_section_name)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local final_description = &quot;&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local final_description = &quot;&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local ap_designation = &quot;&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local ap_designation = &quot;&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if mode == &quot;ap&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if mode == &quot;ap&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local ap_number_match = string.match(uci_section_name, &quot;^wlan(%d+)$&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local ap_number_match = string.match(uci_section_name, &quot;^wlan(%d+)$&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local son_match = string.match(ifname, &quot;^son(%d+)$&quot;) -- Check if it's a 'son' interface</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local son_match = string.match(ifname, &quot;^son(%d+)$&quot;) -- Check if it's a 'son' interface</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if son_match then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if son_match then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- For son interfaces, it's a mesh AP. Naming: SSID (Band AP)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- For son interfaces, it's a mesh AP. Naming: SSID (Band AP)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = string.format(&quot;%s (%s AP)&quot;, ssid, band_info)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = string.format(&quot;%s (%s AP)&quot;, ssid, band_info)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; elseif ap_number_match then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; elseif ap_number_match then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local ap_index = tonumber(ap_number_match)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local ap_index = tonumber(ap_number_match)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Determine AP designation (AP1, AP2, etc.) based on index range</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Determine AP designation (AP1, AP2, etc.) based on index range</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if ap_index &gt;= 0 and ap_index &lt;= 3 then -- wlan0-wlan3 for 2.4G APs</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if ap_index &gt;= 0 and ap_index &lt;= 3 then -- wlan0-wlan3 for 2.4G APs</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ap_designation = &quot;AP&quot; .. (ap_index + 1)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ap_designation = &quot;AP&quot; .. (ap_index + 1)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; elseif ap_index &gt;= 10 and ap_index &lt;= 13 then -- wlan10-wlan13 for 5G APs</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; elseif ap_index &gt;= 10 and ap_index &lt;= 13 then -- wlan10-wlan13 for 5G APs</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ap_designation = &quot;AP&quot; .. (ap_index - 9)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ap_designation = &quot;AP&quot; .. (ap_index - 9)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; elseif ap_index &gt;= 30 and ap_index &lt;= 33 then -- wlan30-wlan33 for 6G APs</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; elseif ap_index &gt;= 30 and ap_index &lt;= 33 then -- wlan30-wlan33 for 6G APs</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ap_designation = &quot;AP&quot; .. (ap_index - 29)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ap_designation = &quot;AP&quot; .. (ap_index - 29)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if ap_designation ~= &quot;&quot; and band_info ~= &quot;Unknown Band&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if ap_designation ~= &quot;&quot; and band_info ~= &quot;Unknown Band&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- For regular APs with a clear designation: SSID (Band APX)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- For regular APs with a clear designation: SSID (Band APX)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = string.format(&quot;%s (%s %s)&quot;, ssid, band_info, ap_designation)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = string.format(&quot;%s (%s %s)&quot;, ssid, band_info, ap_designation)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Fallback for APs without a clear designation (e.g., new type or misconfiguration)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Fallback for APs without a clear designation (e.g., new type or misconfiguration)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if ssid ~= &quot;&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if ssid ~= &quot;&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = string.format(&quot;%s (%s AP)&quot;, ssid, band_info)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = string.format(&quot;%s (%s AP)&quot;, ssid, band_info)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = string.format(&quot;%s (%s AP)&quot;, ifname, band_info) -- Use ifname if SSID is empty</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = string.format(&quot;%s (%s AP)&quot;, ifname, band_info) -- Use ifname if SSID is empty</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; elseif mode == &quot;sta&quot; and band_info ~= &quot;Unknown Band&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; elseif mode == &quot;sta&quot; and band_info ~= &quot;Unknown Band&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- For STA interfaces: SSID (Band STA)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- For STA interfaces: SSID (Band STA)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = string.format(&quot;%s (%s STA)&quot;, ssid, band_info)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = string.format(&quot;%s (%s STA)&quot;, ssid, band_info)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Generic fallback if final_description is still empty (e.g., not a recognized AP/STA/son, or Unknown Band)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Generic fallback if final_description is still empty (e.g., not a recognized AP/STA/son, or Unknown Band)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if final_description == &quot;&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if final_description == &quot;&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if ssid ~= &quot;&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if ssid ~= &quot;&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = ssid</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = ssid</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if band_info ~= &quot;Unknown Band&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if band_info ~= &quot;Unknown Band&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = final_description .. &quot; (&quot; .. band_info .. &quot; &quot; .. string.upper(mode) .. &quot;)&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = final_description .. &quot; (&quot; .. band_info .. &quot; &quot; .. string.upper(mode) .. &quot;)&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = final_description .. &quot; (&quot; .. string.upper(mode) .. &quot;)&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = final_description .. &quot; (&quot; .. string.upper(mode) .. &quot;)&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = ifname .. &quot; (&quot; .. string.upper(mode) .. &quot;)&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = ifname .. &quot; (&quot; .. string.upper(mode) .. &quot;)&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if band_info ~= &quot;Unknown Band&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if band_info ~= &quot;Unknown Band&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = ifname .. &quot; (&quot; .. band_info .. &quot; &quot; .. string.upper(mode) .. &quot;)&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; final_description = ifname .. &quot; (&quot; .. band_info .. &quot; &quot; .. string.upper(mode) .. &quot;)&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; mappings[final_description] = {</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; mappings[final_description] = {</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; type = &quot;wifi&quot;,</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; type = &quot;wifi&quot;,</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci_section_name = uci_section_name,</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci_section_name = uci_section_name,</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ifname = ifname,</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ifname = ifname,</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; description = final_description -- This &quot;description&quot; is what will be used as the key in 'mappings'</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; description = final_description -- This &quot;description&quot; is what will be used as the key in 'mappings'</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; }</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; }</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Consider adding mappings for explicit eth0, eth1, etc. if they are not part of LAN1 mapping</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Consider adding mappings for explicit eth0, eth1, etc. if they are not part of LAN1 mapping</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- and need separate VLAN configuration (e.g., dedicated WAN port on eth0 with VLANs)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- and need separate VLAN configuration (e.g., dedicated WAN port on eth0 with VLANs)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- For now, relying on LAN1 mapping for primary LAN port</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- For now, relying on LAN1 mapping for primary LAN port</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; return mappings</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; return mappings</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Helper to create or ensure existence of a network.interface bridge and add a member</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Helper to create or ensure existence of a network.interface bridge and add a member</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local function ensure_interface_bridge(bridge_name, proto, type, ifname_to_add)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local function ensure_interface_bridge(bridge_name, proto, type, ifname_to_add)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local bridge_uci_section_name = nil</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local bridge_uci_section_name = nil</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;interface&quot;, function(section)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;interface&quot;, function(section)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if section.name == bridge_name then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if section.name == bridge_name then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; bridge_uci_section_name = section[&quot;.name&quot;]</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; bridge_uci_section_name = section[&quot;.name&quot;]</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return false -- Stop iteration</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return false -- Stop iteration</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not bridge_uci_section_name then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not bridge_uci_section_name then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; bridge_uci_section_name = uci:add(&quot;network&quot;, &quot;interface&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; bridge_uci_section_name = uci:add(&quot;network&quot;, &quot;interface&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, bridge_uci_section_name, &quot;name&quot;, bridge_name)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, bridge_uci_section_name, &quot;name&quot;, bridge_name)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, bridge_uci_section_name, &quot;proto&quot;, proto)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, bridge_uci_section_name, &quot;proto&quot;, proto)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, bridge_uci_section_name, &quot;type&quot;, type)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, bridge_uci_section_name, &quot;type&quot;, type)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Created new network.interface bridge: &quot; .. bridge_name .. &quot; (UCI: &quot; .. bridge_uci_section_name .. &quot;)&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Created new network.interface bridge: &quot; .. bridge_name .. &quot; (UCI: &quot; .. bridge_uci_section_name .. &quot;)&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_ifnames = uci:get_list(&quot;network&quot;, bridge_uci_section_name, &quot;ifname&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_ifnames = uci:get_list(&quot;network&quot;, bridge_uci_section_name, &quot;ifname&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local has_changed = false</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local has_changed = false</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not table.contains(current_ifnames, ifname_to_add) then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not table.contains(current_ifnames, ifname_to_add) then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.insert(current_ifnames, ifname_to_add)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.insert(current_ifnames, ifname_to_add)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; has_changed = true</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; has_changed = true</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Sort and deduplicate for consistent output, similar to lan bridge members</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Sort and deduplicate for consistent output, similar to lan bridge members</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local sorted_ifnames = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local sorted_ifnames = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local temp_seen = {} -- For deduplication during sort preparation</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local temp_seen = {} -- For deduplication during sort preparation</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, iface in ipairs(current_ifnames) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, iface in ipairs(current_ifnames) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not temp_seen[iface] then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not temp_seen[iface] then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(sorted_ifnames, iface)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(sorted_ifnames, iface)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; temp_seen[iface] = true</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; temp_seen[iface] = true</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; table.sort(sorted_ifnames, function(a, b)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; table.sort(sorted_ifnames, function(a, b)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Custom sort order for consistency: eth1.X first (numerically), then athX (numerically), then others</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Custom sort order for consistency: eth1.X first (numerically), then athX (numerically), then others</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local lan_physical_ifname_base = &quot;eth1&quot; -- Define locally as it's used in this scope</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local lan_physical_ifname_base = &quot;eth1&quot; -- Define locally as it's used in this scope</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local a_is_eth_vlan = string.match(a, &quot;^&quot; .. lan_physical_ifname_base .. &quot;%.(%d+)$&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local a_is_eth_vlan = string.match(a, &quot;^&quot; .. lan_physical_ifname_base .. &quot;%.(%d+)$&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local b_is_eth_vlan = string.match(b, &quot;^&quot; .. lan_physical_ifname_base .. &quot;%.(%d+)$&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local b_is_eth_vlan = string.match(b, &quot;^&quot; .. lan_physical_ifname_base .. &quot;%.(%d+)$&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local a_is_ath = string.match(a, &quot;^ath(%d+)$&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local a_is_ath = string.match(a, &quot;^ath(%d+)$&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local b_is_ath = string.match(b, &quot;^ath(%d+)$&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local b_is_ath = string.match(b, &quot;^ath(%d+)$&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Prioritize eth1.X interfaces</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Prioritize eth1.X interfaces</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if a_is_eth_vlan and not b_is_eth_vlan then return true end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if a_is_eth_vlan and not b_is_eth_vlan then return true end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not a_is_eth_vlan and b_is_eth_vlan then return false end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not a_is_eth_vlan and b_is_eth_vlan then return false end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Then athX interfaces</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Then athX interfaces</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if a_is_ath and not b_is_ath then return true end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if a_is_ath and not b_is_ath then return true end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not a_is_ath and b_is_ath then return false end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not a_is_ath and b_is_ath then return false end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- If both are eth1.X or both are athX, sort numerically</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- If both are eth1.X or both are athX, sort numerically</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if a_is_eth_vlan and b_is_eth_vlan then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if a_is_eth_vlan and b_is_eth_vlan then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return tonumber(a_is_eth_vlan) &lt; tonumber(b_is_eth_vlan)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return tonumber(a_is_eth_vlan) &lt; tonumber(b_is_eth_vlan)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; elseif a_is_ath and b_is_ath then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; elseif a_is_ath and b_is_ath then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return tonumber(a_is_ath) &lt; tonumber(b_is_ath)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return tonumber(a_is_ath) &lt; tonumber(b_is_ath)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; return a &lt; b -- Fallback to lexicographical sort for other cases</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; return a &lt; b -- Fallback to lexicographical sort for other cases</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local final_ifname_str = table.concat(sorted_ifnames, &quot; &quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local final_ifname_str = table.concat(sorted_ifnames, &quot; &quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:set(&quot;network&quot;, bridge_uci_section_name, &quot;ifname&quot;, final_ifname_str)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:set(&quot;network&quot;, bridge_uci_section_name, &quot;ifname&quot;, final_ifname_str)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Updated bridge '&quot; .. bridge_name .. &quot;' ifname to: '&quot; .. final_ifname_str .. &quot;'&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Updated bridge '&quot; .. bridge_name .. &quot;' ifname to: '&quot; .. final_ifname_str .. &quot;'&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; return bridge_uci_section_name</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; return bridge_uci_section_name</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- Helper to create or ensure existence of a network.device 802.1q subinterface (e.g., eth1.100)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- Helper to create or ensure existence of a network.device 802.1q subinterface (e.g., eth1.100)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">local function ensure_8021q_subinterface(vlan_ifname, base_ifname, vid)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">local function ensure_8021q_subinterface(vlan_ifname, base_ifname, vid)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local subiface_uci_section = uci:get_first(&quot;network&quot;, &quot;device&quot;, { name = vlan_ifname })</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local subiface_uci_section = uci:get_first(&quot;network&quot;, &quot;device&quot;, { name = vlan_ifname })</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not subiface_uci_section then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not subiface_uci_section then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; subiface_uci_section = uci:add(&quot;network&quot;, &quot;device&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; subiface_uci_section = uci:add(&quot;network&quot;, &quot;device&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, subiface_uci_section, &quot;name&quot;, vlan_ifname)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, subiface_uci_section, &quot;name&quot;, vlan_ifname)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:set(&quot;network&quot;, subiface_uci_section, &quot;.type&quot;, &quot;8021q&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:set(&quot;network&quot;, subiface_uci_section, &quot;.type&quot;, &quot;8021q&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:set(&quot;network&quot;, subiface_uci_section, &quot;ifname&quot;, base_ifname)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:set(&quot;network&quot;, subiface_uci_section, &quot;ifname&quot;, base_ifname)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:set(&quot;network&quot;, subiface_uci_section, &quot;vid&quot;, tostring(vid))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:set(&quot;network&quot;, subiface_uci_section, &quot;vid&quot;, tostring(vid))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Ensured 802.1q subinterface: &quot; .. vlan_ifname .. &quot; (UCI: &quot; .. subiface_uci_section .. &quot;)&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Ensured 802.1q subinterface: &quot; .. vlan_ifname .. &quot; (UCI: &quot; .. subiface_uci_section .. &quot;)&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; return subiface_uci_section</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; return subiface_uci_section</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">-- 路由到具体逻辑</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- 路由到具体逻辑</td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSigMod">function route_api()</td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span>function route_api()</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">设置</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">HTTP</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">响应头</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">io.write(&quot;Content-type:</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">application/json\nPragma:</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">no-cache\n\n&quot;)</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">获取</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">数据长度</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POSTLength</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">tonumber(os.getenv(&quot;CONTENT_LENGTH&quot;))</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">or</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">0</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;&quot;</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">读取</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">数据</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POSTLength</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&gt;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">0</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">io.read(POSTLength)</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(&quot;Received</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">data:</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">(POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">or</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;nil&quot;))</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">else</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(&quot;No</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">data</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">received</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">or</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">CONTENT_LENGTH</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">is</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">0&quot;)</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">确保读取成功</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">not</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">or</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">==</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">error_message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;Failed</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">to</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">retrieve</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">data&quot;</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(error_message)</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">io.write(cjson.encode({</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">module</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;vlan&quot;,</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">version</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;1.0&quot;,</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">errcode</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">1,</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">result</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">{</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">error_message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">}</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">}))</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">io.flush()</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">return</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">解析</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">POST</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">数据为JSON</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">requestData</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">cjson.decode(POST)</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">not</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">requestData</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">error_message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;Invalid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">JSON</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">input&quot;</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(error_message)</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">io.write(cjson.encode({</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">module</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;vlan&quot;,</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">version</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;1.0&quot;,</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">errcode</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">2,</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">result</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">{</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">error_message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">}</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">}))</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">io.flush()</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">return</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">检查请求格式</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">not</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">requestData.version</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">or</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">not</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">requestData.sid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">or</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">not</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">requestData.module</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">or</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">not</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">requestData.api</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">error_message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;Invalid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">request</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">format&quot;</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(error_message)</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">io.write(cjson.encode({</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">module</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;vlan&quot;,</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">version</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;1.0&quot;,</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">errcode</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">3,</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">result</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">{</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">error_message</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">}</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">}))</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">io.flush()</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">return</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemInsigMod">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if requestData.api == &quot;set&quot; then</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if requestData.api == &quot;set&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Calling set_vlan with data: &quot; .. cjson.encode(requestData))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Calling set_vlan with data: &quot; .. cjson.encode(requestData))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; set_vlan(requestData)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; set_vlan(requestData)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; elseif requestData.api == &quot;get&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; elseif requestData.api == &quot;get&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Calling get_vlan with data: &quot; .. cjson.encode(requestData))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Calling get_vlan with data: &quot; .. cjson.encode(requestData))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; get_vlan(requestData)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; get_vlan(requestData)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local error_message = &quot;Unknown API: &quot; .. requestData.api</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local error_message = &quot;Unknown API: &quot; .. requestData.api</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(error_message)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(error_message)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; io.write(cjson.encode({</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; io.write(cjson.encode({</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; module = &quot;vlan&quot;,</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; module = &quot;vlan&quot;,</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; version = &quot;1.0&quot;,</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; version = &quot;1.0&quot;,</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; errcode = 4,</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; errcode = 4,</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; result = { message = error_message }</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; result = { message = error_message }</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; }))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; }))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- set_vlan function (full replacement to support new JSON format and actual network config)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- set_vlan function (full replacement to support new JSON format and actual network config)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">function set_vlan(data)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">function set_vlan(data)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Debug: Type of uci: &quot; .. type(uci))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Debug: Type of uci: &quot; .. type(uci))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Debug: Type of uci.add_list: &quot; .. type(uci.add_list)) -- This will now correctly be nil</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Debug: Type of uci.add_list: &quot; .. type(uci.add_list)) -- This will now correctly be nil</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Debug: Type of uci.get_list: &quot; .. type(uci.get_list))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Debug: Type of uci.get_list: &quot; .. type(uci.get_list))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Debug: Type of uci.del_list: &quot; .. type(uci.del_list)) -- New debug log</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Debug: Type of uci.del_list: &quot; .. type(uci.del_list)) -- New debug log</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Starting set_vlan with updated logic for LAN/WAN config.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Starting set_vlan with updated logic for LAN/WAN config.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- 兼容AC下发旧格式模板</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- 兼容AC下发旧格式模板</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local param = data.params or {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local param = data.params or {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if type(param) == &quot;table&quot; and not param.interfaces and #param &gt; 0 then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if type(param) == &quot;table&quot; and not param.interfaces and #param &gt; 0 then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; param = { interfaces = param }</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; param = { interfaces = param }</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;兼容AC下发旧格式模板，已自动转换为新格式&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;兼容AC下发旧格式模板，已自动转换为新格式&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not param.interfaces then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not param.interfaces then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Error: 'interfaces' array is missing in set_vlan parameters.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Error: 'interfaces' array is missing in set_vlan parameters.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; io.write(cjson.encode({ module = &quot;vlan&quot;, version = &quot;1.0&quot;, errcode = 4, result = { message = &quot;'interfaces' array is missing.&quot; } }))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; io.write(cjson.encode({ module = &quot;vlan&quot;, version = &quot;1.0&quot;, errcode = 4, result = { message = &quot;'interfaces' array is missing.&quot; } }))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; return</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; return</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local interface_mappings = get_interface_mappings()</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local interface_mappings = get_interface_mappings()</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_physical_ifname_base = &quot;eth1&quot;&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_physical_ifname_base = &quot;eth1&quot;&nbsp;</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_physical_switch_port_index = interface_mappings[&quot;LAN1&quot;].physical_switch_port_index</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_physical_switch_port_index = interface_mappings[&quot;LAN1&quot;].physical_switch_port_index</td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSigMod">&nbsp;</td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">interface_mappings[&quot;LAN2&quot;].physical_lan2_switch_port_index</span></td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp;</span></td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local management_vlan_id = tonumber(param.management_vlan_id)</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local management_vlan_id = tonumber(param.management_vlan_id)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if management_vlan_id and (management_vlan_id &lt; 1 or management_vlan_id &gt; 4094) then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if management_vlan_id and (management_vlan_id &lt; 1 or management_vlan_id &gt; 4094) then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Warning: Invalid management_vlan_id: &quot; .. management_vlan_id .. &quot;. Ignoring.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Warning: Invalid management_vlan_id: &quot; .. management_vlan_id .. &quot;. Ignoring.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; management_vlan_id = nil</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; management_vlan_id = nil</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Step 1: Cleanup Existing Dynamic VLAN Configurations ---</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Step 1: Cleanup Existing Dynamic VLAN Configurations ---</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Clearing existing dynamic VLAN bridges and 802.1q devices before reconfiguring.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Clearing existing dynamic VLAN bridges and 802.1q devices before reconfiguring.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Delete any br-vlanXXX bridges (excluding 'lan' bridge, as its ifname will be updated)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Delete any br-vlanXXX bridges (excluding 'lan' bridge, as its ifname will be updated)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;interface&quot;, function(section)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;interface&quot;, function(section)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local name = section.name or &quot;&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local name = section.name or &quot;&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Only delete bridges that were dynamically created (e.g., br-vlanXXX) and are not the 'lan' bridge itself</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Only delete bridges that were dynamically created (e.g., br-vlanXXX) and are not the 'lan' bridge itself</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if string.match(name, &quot;^br%-vlan(%d+)$&quot;) and name ~= &quot;lan&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if string.match(name, &quot;^br%-vlan(%d+)$&quot;) and name ~= &quot;lan&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Deleting old dynamic bridge: &quot; .. name .. &quot; (UCI: &quot; .. section[&quot;.name&quot;] .. &quot;)&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Deleting old dynamic bridge: &quot; .. name .. &quot; (UCI: &quot; .. section[&quot;.name&quot;] .. &quot;)&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:delete(&quot;network&quot;, section[&quot;.name&quot;])</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:delete(&quot;network&quot;, section[&quot;.name&quot;])</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Delete any eth1.XXX devices created dynamically (excluding eth1.1 and eth1.2)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Delete any eth1.XXX devices created dynamically (excluding eth1.1 and eth1.2)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;device&quot;, function(section)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;device&quot;, function(section)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local name = section.name or &quot;&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local name = section.name or &quot;&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if string.match(name, &quot;^&quot; .. lan_physical_ifname_base .. &quot;%.(%d+)$&quot;) and name ~= &quot;eth1.1&quot; and name ~= &quot;eth1.2&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if string.match(name, &quot;^&quot; .. lan_physical_ifname_base .. &quot;%.(%d+)$&quot;) and name ~= &quot;eth1.1&quot; and name ~= &quot;eth1.2&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Deleting old dynamic 802.1q device: &quot; .. name .. &quot; (UCI: &quot; .. section[&quot;.name&quot;] .. &quot;)&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Deleting old dynamic 802.1q device: &quot; .. name .. &quot; (UCI: &quot; .. section[&quot;.name&quot;] .. &quot;)&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:delete(&quot;network&quot;, section[&quot;.name&quot;])</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:delete(&quot;network&quot;, section[&quot;.name&quot;])</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Delete all existing switch_vlan entries for a clean slate</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Delete all existing switch_vlan entries for a clean slate</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;switch_vlan&quot;, function(section)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;switch_vlan&quot;, function(section)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Deleting old switch_vlan entry: &quot; .. section[&quot;.name&quot;] .. &quot; (VLAN ID: &quot; .. (section.vlan or &quot;N/A&quot;) .. &quot;)&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Deleting old switch_vlan entry: &quot; .. section[&quot;.name&quot;] .. &quot; (VLAN ID: &quot; .. (section.vlan or &quot;N/A&quot;) .. &quot;)&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:delete(&quot;network&quot;, section[&quot;.name&quot;])</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:delete(&quot;network&quot;, section[&quot;.name&quot;])</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Cleaned up all existing switch_vlan entries.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Cleaned up all existing switch_vlan entries.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Step 2: Configure Management VLAN (Affects 'lan' interface) ---</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Step 2: Configure Management VLAN (Affects 'lan' interface) ---</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_uci_section = nil</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_uci_section = nil</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;interface&quot;, function(section)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;interface&quot;, function(section)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if section[&quot;.name&quot;] == &quot;lan&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if section[&quot;.name&quot;] == &quot;lan&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; lan_uci_section = section</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; lan_uci_section = section</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: Found lan_uci_section in set_vlan via foreach: &quot; .. section[&quot;.name&quot;])</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: Found lan_uci_section in set_vlan via foreach: &quot; .. section[&quot;.name&quot;])</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return false -- Stop iteration after finding it</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return false -- Stop iteration after finding it</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not lan_uci_section then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if not lan_uci_section then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Error: 'lan' interface section not found in set_vlan.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Error: 'lan' interface section not found in set_vlan.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; io.write(cjson.encode({</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; io.write(cjson.encode({</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; module = &quot;vlan&quot;, version = &quot;1.0&quot;, errcode = 1, result = { message = &quot;Failed to find 'lan' interface section.&quot; }</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; module = &quot;vlan&quot;, version = &quot;1.0&quot;, errcode = 1, result = { message = &quot;Failed to find 'lan' interface section.&quot; }</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; }))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; }))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; return</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; return</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_uci_section_name = lan_uci_section[&quot;.name&quot;]</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_uci_section_name = lan_uci_section[&quot;.name&quot;]</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_lan_ifnames_str = uci:get(&quot;network&quot;, lan_uci_section_name, &quot;ifname&quot;) or &quot;&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local current_lan_ifnames_str = uci:get(&quot;network&quot;, lan_uci_section_name, &quot;ifname&quot;) or &quot;&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Debug: Original lan bridge ifnames string for set_vlan: '&quot; .. current_lan_ifnames_str .. &quot;'&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Debug: Original lan bridge ifnames string for set_vlan: '&quot; .. current_lan_ifnames_str .. &quot;'&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local new_lan_bridge_members = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local new_lan_bridge_members = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local mgmt_cpu_vlan_ifname_to_use = nil</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local mgmt_cpu_vlan_ifname_to_use = nil</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local eth1_1_default_ifname = interface_mappings[&quot;LAN1&quot;].cpu_vlan_ifname_default -- &quot;eth1.1&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local eth1_1_default_ifname = interface_mappings[&quot;LAN1&quot;].cpu_vlan_ifname_default -- &quot;eth1.1&quot;</td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSigMod">&nbsp;</td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">eth1_2_default_ifname</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">interface_mappings[&quot;LAN2&quot;].lan_vlan2_ifname_default</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;eth1.2&quot;</span></td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp;</span></td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if management_vlan_id then</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if management_vlan_id then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Configuring management VLAN with ID: &quot; .. management_vlan_id)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Configuring management VLAN with ID: &quot; .. management_vlan_id)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; mgmt_cpu_vlan_ifname_to_use = lan_physical_ifname_base .. &quot;.&quot; .. management_vlan_id</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; mgmt_cpu_vlan_ifname_to_use = lan_physical_ifname_base .. &quot;.&quot; .. management_vlan_id</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Set LAN interface IP and default ifname for new management VLAN</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Set LAN interface IP and default ifname for new management VLAN</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, lan_uci_section_name, &quot;ipaddr&quot;, &quot;192.168.&quot; .. management_vlan_id .. &quot;.254&quot;) -- Example IP: ************** for VLAN 10</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, lan_uci_section_name, &quot;ipaddr&quot;, &quot;192.168.&quot; .. management_vlan_id .. &quot;.254&quot;) -- Example IP: ************** for VLAN 10</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, lan_uci_section_name, &quot;def_ifname&quot;, mgmt_cpu_vlan_ifname_to_use)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, lan_uci_section_name, &quot;def_ifname&quot;, mgmt_cpu_vlan_ifname_to_use)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Set 'lan' interface ipaddr to 192.168.&quot; .. management_vlan_id .. &quot;.254 and def_ifname to &quot; .. mgmt_cpu_vlan_ifname_to_use .. &quot;.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Set 'lan' interface ipaddr to 192.168.&quot; .. management_vlan_id .. &quot;.254 and def_ifname to &quot; .. mgmt_cpu_vlan_ifname_to_use .. &quot;.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Ensure the 802.1q subinterface for management VLAN exists</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Ensure the 802.1q subinterface for management VLAN exists</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; ensure_8021q_subinterface(mgmt_cpu_vlan_ifname_to_use, lan_physical_ifname_base, management_vlan_id)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; ensure_8021q_subinterface(mgmt_cpu_vlan_ifname_to_use, lan_physical_ifname_base, management_vlan_id)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Add the new management VLAN interface to the list of bridge members</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Add the new management VLAN interface to the list of bridge members</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_lan_bridge_members, mgmt_cpu_vlan_ifname_to_use)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_lan_bridge_members, mgmt_cpu_vlan_ifname_to_use)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Configure the physical switch port (LAN1) as tagged for the management VLAN</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Configure the physical switch port (LAN1) as tagged for the management VLAN</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; ensure_switch_vlan(management_vlan_id)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; ensure_switch_vlan(management_vlan_id)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; update_switch_vlan_ports(management_vlan_id, lan_physical_switch_port_index, true) -- Tagged for LAN1</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; update_switch_vlan_ports(management_vlan_id, lan_physical_switch_port_index, true) -- Tagged for LAN1</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; update_switch_vlan_ports(management_vlan_id, &quot;0&quot;, true) -- CPU port '0' tagged ('0t') for management VLAN</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; update_switch_vlan_ports(management_vlan_id, &quot;0&quot;, true) -- CPU port '0' tagged ('0t') for management VLAN</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Configured physical LAN port &quot; .. lan_physical_switch_port_index .. &quot; as tagged for management VLAN &quot; .. management_vlan_id .. &quot; in switch_vlan.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Configured physical LAN port &quot; .. lan_physical_switch_port_index .. &quot; as tagged for management VLAN &quot; .. management_vlan_id .. &quot; in switch_vlan.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- 自动补全ath*接口到管理VLAN bridge成员</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- 自动补全ath*接口到管理VLAN bridge成员</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:foreach(&quot;wireless&quot;, &quot;wifi-iface&quot;, function(section)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:foreach(&quot;wireless&quot;, &quot;wifi-iface&quot;, function(section)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local ifname = section.ifname</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local ifname = section.ifname</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local disabled = section.disabled or &quot;0&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local disabled = section.disabled or &quot;0&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if ifname and disabled ~= &quot;1&quot; and string.match(ifname, &quot;^ath%d+$&quot;) then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if ifname and disabled ~= &quot;1&quot; and string.match(ifname, &quot;^ath%d+$&quot;) then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if not table.contains(new_lan_bridge_members, ifname) then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if not table.contains(new_lan_bridge_members, ifname) then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_lan_bridge_members, ifname)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_lan_bridge_members, ifname)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;自动将 &quot; .. ifname .. &quot; 加入管理VLAN bridge成员&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;自动将 &quot; .. ifname .. &quot; 加入管理VLAN bridge成员&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;No management VLAN ID provided or invalid. Reverting 'lan' interface to default eth1.1 based bridge.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;No management VLAN ID provided or invalid. Reverting 'lan' interface to default eth1.1 based bridge.&quot;)</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; mgmt_cpu_vlan_ifname_to_use = eth1_1_default_ifname</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; mgmt_cpu_vlan_ifname_to_use = eth1_1_default_ifname</td>
</tr>
<tr class="SectionAll">
<td class="TextItemSigMod">&nbsp;</td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">mgmt_cpu_vlan2_ifname_to_use</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">eth1_2_default_ifname</span></td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Revert LAN interface IP and default ifname to default</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Revert LAN interface IP and default ifname to default</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, lan_uci_section_name, &quot;ipaddr&quot;, &quot;*************&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, lan_uci_section_name, &quot;ipaddr&quot;, &quot;*************&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, lan_uci_section_name, &quot;def_ifname&quot;, eth1_1_default_ifname)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;network&quot;, lan_uci_section_name, &quot;def_ifname&quot;, eth1_1_default_ifname)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Reverted 'lan' interface ipaddr to ************* and def_ifname to &quot; .. eth1_1_default_ifname .. &quot;.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Reverted 'lan' interface ipaddr to ************* and def_ifname to &quot; .. eth1_1_default_ifname .. &quot;.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Add the default eth1.1 interface to the list of bridge members</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Add the default eth1.1 interface to the list of bridge members</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_lan_bridge_members, mgmt_cpu_vlan_ifname_to_use)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_lan_bridge_members, mgmt_cpu_vlan_ifname_to_use)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Ensure switch port 1 is untagged for VLAN 1 if management VLAN is removed</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Ensure switch port 1 is untagged for VLAN 1 if management VLAN is removed</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; ensure_switch_vlan(1) -- Ensure VLAN 1 entry exists</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; ensure_switch_vlan(1) -- Ensure VLAN 1 entry exists</td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSigMod">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; update_switch_vlan_ports(1, lan_physical_switch_port_index, false) -- Set as untagged for LAN1</td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="TextItemSigMod">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; update_switch_vlan_ports(1, lan_physical_switch_port_index, false) -- Set as untagged for LAN1<span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">and</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">LAN2</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">update_switch_vlan_ports(1,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">false)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; remove_switch_vlan_port(1, lan_physical_switch_port_index, true) -- Ensure not tagged for VLAN 1</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; remove_switch_vlan_port(1, lan_physical_switch_port_index, true) -- Ensure not tagged for VLAN 1<span class="TextSegInsigDiff">&nbsp;</span></td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">remove_switch_vlan_port(1,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">true)</span></td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Also ensure CPU port 0 is tagged for VLAN 1</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Also ensure CPU port 0 is tagged for VLAN 1</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; update_switch_vlan_ports(1, &quot;0&quot;, true) -- CPU port '0' tagged ('0t') for VLAN 1</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; update_switch_vlan_ports(1, &quot;0&quot;, true) -- CPU port '0' tagged ('0t') for VLAN 1</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Populate existing_lan_members for efficient lookup</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Populate existing_lan_members for efficient lookup</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local existing_lan_members = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local existing_lan_members = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for iface in string.gmatch(current_lan_ifnames_str, &quot;[^%s]+&quot;) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for iface in string.gmatch(current_lan_ifnames_str, &quot;[^%s]+&quot;) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; existing_lan_members[iface] = true</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; existing_lan_members[iface] = true</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Iterate through original interfaces, adding non-management eth1.X and ath* interfaces</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Iterate through original interfaces, adding non-management eth1.X and ath* interfaces</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for iface_name, _ in pairs(existing_lan_members) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for iface_name, _ in pairs(existing_lan_members) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- If it's an eth1.X interface and it's not the new/default management one, skip it.</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- If it's an eth1.X interface and it's not the new/default management one, skip it.</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- This handles cases where user had a different management VLAN set previously, or eth1.2 should be kept.</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- This handles cases where user had a different management VLAN set previously, or eth1.2 should be kept.</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local is_eth1_vlan_iface = string.match(iface_name, &quot;^&quot; .. lan_physical_ifname_base .. &quot;%.%d+$&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local is_eth1_vlan_iface = string.match(iface_name, &quot;^&quot; .. lan_physical_ifname_base .. &quot;%.%d+$&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if is_eth1_vlan_iface then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if is_eth1_vlan_iface then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if iface_name ~= mgmt_cpu_vlan_ifname_to_use then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if iface_name ~= mgmt_cpu_vlan_ifname_to_use then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- If it's eth1.2 and was in original config, add it back explicitly if not already there</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- If it's eth1.2 and was in original config, add it back explicitly if not already there</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if iface_name == &quot;eth1.2&quot; and not table.contains(new_lan_bridge_members, &quot;eth1.2&quot;) then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if iface_name == &quot;eth1.2&quot; and not table.contains(new_lan_bridge_members, &quot;eth1.2&quot;) then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_lan_bridge_members, &quot;eth1.2&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_lan_bridge_members, &quot;eth1.2&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- All other eth1.X are considered old management VLANs and will be excluded.</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- All other eth1.X are considered old management VLANs and will be excluded.</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Add all other non-eth1.X interfaces (ath*, etc.) if not already in the list</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Add all other non-eth1.X interfaces (ath*, etc.) if not already in the list</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if not table.contains(new_lan_bridge_members, iface_name) then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if not table.contains(new_lan_bridge_members, iface_name) then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_lan_bridge_members, iface_name)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(new_lan_bridge_members, iface_name)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Final cleanup and sorting for consistent output</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Final cleanup and sorting for consistent output</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local sorted_final_lan_ifnames = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local sorted_final_lan_ifnames = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local temp_seen = {} -- For deduplication during sort preparation</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local temp_seen = {} -- For deduplication during sort preparation</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, iface in ipairs(new_lan_bridge_members) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, iface in ipairs(new_lan_bridge_members) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not temp_seen[iface] then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not temp_seen[iface] then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(sorted_final_lan_ifnames, iface)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(sorted_final_lan_ifnames, iface)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; temp_seen[iface] = true</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; temp_seen[iface] = true</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Custom sort order for consistency: eth1.X first (numerically), then athX (numerically), then others</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Custom sort order for consistency: eth1.X first (numerically), then athX (numerically), then others</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; table.sort(sorted_final_lan_ifnames, function(a, b)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; table.sort(sorted_final_lan_ifnames, function(a, b)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local a_is_eth_vlan = string.match(a, &quot;^&quot; .. lan_physical_ifname_base .. &quot;%.(%d+)$&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local a_is_eth_vlan = string.match(a, &quot;^&quot; .. lan_physical_ifname_base .. &quot;%.(%d+)$&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local b_is_eth_vlan = string.match(b, &quot;^&quot; .. lan_physical_ifname_base .. &quot;%.(%d+)$&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local b_is_eth_vlan = string.match(b, &quot;^&quot; .. lan_physical_ifname_base .. &quot;%.(%d+)$&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local a_is_ath = string.match(a, &quot;^ath(%d+)$&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local a_is_ath = string.match(a, &quot;^ath(%d+)$&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local b_is_ath = string.match(b, &quot;^ath(%d+)$&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local b_is_ath = string.match(b, &quot;^ath(%d+)$&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Prioritize eth1.X interfaces</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Prioritize eth1.X interfaces</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if a_is_eth_vlan and not b_is_eth_vlan then return true end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if a_is_eth_vlan and not b_is_eth_vlan then return true end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not a_is_eth_vlan and b_is_eth_vlan then return false end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not a_is_eth_vlan and b_is_eth_vlan then return false end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Then athX interfaces</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Then athX interfaces</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if a_is_ath and not b_is_ath then return true end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if a_is_ath and not b_is_ath then return true end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not a_is_ath and b_is_ath then return false end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not a_is_ath and b_is_ath then return false end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- If both are eth1.X or both are athX, sort numerically</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- If both are eth1.X or both are athX, sort numerically</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if a_is_eth_vlan and b_is_eth_vlan then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if a_is_eth_vlan and b_is_eth_vlan then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return tonumber(a_is_eth_vlan) &lt; tonumber(b_is_eth_vlan)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return tonumber(a_is_eth_vlan) &lt; tonumber(b_is_eth_vlan)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; elseif a_is_ath and b_is_ath then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; elseif a_is_ath and b_is_ath then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return tonumber(a_is_ath) &lt; tonumber(b_is_ath)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return tonumber(a_is_ath) &lt; tonumber(b_is_ath)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; return a &lt; b -- Fallback to lexicographical sort for other cases</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; return a &lt; b -- Fallback to lexicographical sort for other cases</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local final_lan_ifname_str = table.concat(sorted_final_lan_ifnames, &quot; &quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local final_lan_ifname_str = table.concat(sorted_final_lan_ifnames, &quot; &quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:set(&quot;network&quot;, lan_uci_section[&quot;.name&quot;], &quot;ifname&quot;, final_lan_ifname_str)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:set(&quot;network&quot;, lan_uci_section[&quot;.name&quot;], &quot;ifname&quot;, final_lan_ifname_str)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Set 'lan' interface ifname to: &quot; .. final_lan_ifname_str)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Set 'lan' interface ifname to: &quot; .. final_lan_ifname_str)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Step 3: Configure Per-Interface VLANs (PVID and Tagged VLANs) ---</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Step 3: Configure Per-Interface VLANs (PVID and Tagged VLANs) ---</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local configured_interfaces = {} -- Track interfaces that were explicitly configured in this request</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local configured_interfaces = {} -- Track interfaces that were explicitly configured in this request</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, iface_config in ipairs(param.interfaces) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for _, iface_config in ipairs(param.interfaces) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local logical_name = iface_config.name</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local logical_name = iface_config.name</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local pvid = tonumber(iface_config.pvid)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local pvid = tonumber(iface_config.pvid)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local tagged_vlans = iface_config.tagged_vlans or {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local tagged_vlans = iface_config.tagged_vlans or {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not logical_name then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if not logical_name then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Warning: Interface name is missing for an interface configuration. Skipping.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Warning: Interface name is missing for an interface configuration. Skipping.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Validate VLAN IDs</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Validate VLAN IDs</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if pvid and (pvid &lt; 0 or pvid &gt; 4094) then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if pvid and (pvid &lt; 0 or pvid &gt; 4094) then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Warning: Invalid PVID for &quot; .. logical_name .. &quot;: &quot; .. pvid .. &quot;. Ignoring PVID.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Warning: Invalid PVID for &quot; .. logical_name .. &quot;: &quot; .. pvid .. &quot;. Ignoring PVID.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; pvid = nil</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; pvid = nil</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local valid_tagged_vlans = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local valid_tagged_vlans = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for _, vid in ipairs(tagged_vlans) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for _, vid in ipairs(tagged_vlans) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local num_vid = tonumber(vid)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local num_vid = tonumber(vid)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if num_vid and num_vid &gt;= 1 and num_vid &lt;= 4094 then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if num_vid and num_vid &gt;= 1 and num_vid &lt;= 4094 then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(valid_tagged_vlans, num_vid)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(valid_tagged_vlans, num_vid)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Warning: Invalid tagged VLAN ID for &quot; .. logical_name .. &quot;: &quot; .. vid .. &quot;. Ignoring.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Warning: Invalid tagged VLAN ID for &quot; .. logical_name .. &quot;: &quot; .. vid .. &quot;. Ignoring.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; tagged_vlans = valid_tagged_vlans</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; tagged_vlans = valid_tagged_vlans</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Processing interface: &quot; .. logical_name .. &quot;, PVID: &quot; .. (pvid or &quot;none&quot;) .. &quot;, Tagged: &quot; .. cjson.encode(tagged_vlans))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Processing interface: &quot; .. logical_name .. &quot;, PVID: &quot; .. (pvid or &quot;none&quot;) .. &quot;, Tagged: &quot; .. cjson.encode(tagged_vlans))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local iface_details = interface_mappings[logical_name]</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local iface_details = interface_mappings[logical_name]</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if not iface_details then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if not iface_details then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Warning: Unknown logical interface name: &quot; .. logical_name .. &quot;. Skipping configuration.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Warning: Unknown logical interface name: &quot; .. logical_name .. &quot;. Skipping configuration.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local actual_ifname = iface_details.ifname -- For wireless interfaces</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local actual_ifname = iface_details.ifname -- For wireless interfaces</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local uci_wifi_iface_section = iface_details.uci_section_name</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local uci_wifi_iface_section = iface_details.uci_section_name</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local is_lan_physical_gui_entry = (iface_details.type == &quot;lan_physical_port&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local is_lan_physical_gui_entry = (iface_details.type == &quot;lan_physical_port&quot;)</td>
</tr>
<tr class="SectionAll">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">-+</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">is_lan2_physical_gui_entry</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">(iface_details.type</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">==</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;lan2_physical_port&quot;)</span></td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local is_wifi_iface = (iface_details.type == &quot;wifi&quot;)</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local is_wifi_iface = (iface_details.type == &quot;wifi&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; configured_interfaces[logical_name] = true -- Mark as configured</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; configured_interfaces[logical_name] = true -- Mark as configured</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- --- Handle Physical LAN Port (LAN1 GUI entry) VLANs via switch_vlan ---</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- --- Handle Physical LAN Port (LAN1 GUI entry) VLANs via switch_vlan ---</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if is_lan_physical_gui_entry then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if is_lan_physical_gui_entry then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Configure PVID on switch_vlan for physical port 1</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Configure PVID on switch_vlan for physical port 1</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if pvid then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if pvid then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ensure_switch_vlan(pvid)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ensure_switch_vlan(pvid)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; update_switch_vlan_ports(pvid, lan_physical_switch_port_index, false) -- Set as untagged</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; update_switch_vlan_ports(pvid, lan_physical_switch_port_index, false) -- Set as untagged</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Ensure it's not tagged for this VID</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Ensure it's not tagged for this VID</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; remove_switch_vlan_port(pvid, lan_physical_switch_port_index, true)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; remove_switch_vlan_port(pvid, lan_physical_switch_port_index, true)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Set switch port &quot; .. lan_physical_switch_port_index .. &quot; as untagged for VID &quot; .. pvid .. &quot; (LAN1 PVID).&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Set switch port &quot; .. lan_physical_switch_port_index .. &quot; as untagged for VID &quot; .. pvid .. &quot; (LAN1 PVID).&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- If no PVID, remove untagged from this port for any VLAN</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- If no PVID, remove untagged from this port for any VLAN</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;switch_vlan&quot;, function(section)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;switch_vlan&quot;, function(section)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local vid_on_switch = tonumber(section.vlan)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local vid_on_switch = tonumber(section.vlan)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if vid_on_switch then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if vid_on_switch then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; remove_switch_vlan_port(vid_on_switch, lan_physical_switch_port_index, false)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; remove_switch_vlan_port(vid_on_switch, lan_physical_switch_port_index, false)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Removed any untagged VLAN from switch port &quot; .. lan_physical_switch_port_index .. &quot; (LAN1 PVID removed).&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Removed any untagged VLAN from switch port &quot; .. lan_physical_switch_port_index .. &quot; (LAN1 PVID removed).&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Configure Tagged VLANs on switch_vlan for physical port 1</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Configure Tagged VLANs on switch_vlan for physical port 1</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for _, vid in ipairs(tagged_vlans) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for _, vid in ipairs(tagged_vlans) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ensure_switch_vlan(vid)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ensure_switch_vlan(vid)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; update_switch_vlan_ports(vid, lan_physical_switch_port_index, true) -- Set as tagged</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; update_switch_vlan_ports(vid, lan_physical_switch_port_index, true) -- Set as tagged</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Ensure it's not untagged for this VID</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Ensure it's not untagged for this VID</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; remove_switch_vlan_port(vid, lan_physical_switch_port_index, false)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; remove_switch_vlan_port(vid, lan_physical_switch_port_index, false)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Set switch port &quot; .. lan_physical_switch_port_index .. &quot; as tagged for VID &quot; .. vid .. &quot; (LAN1 Tagged).&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Set switch port &quot; .. lan_physical_switch_port_index .. &quot; as tagged for VID &quot; .. vid .. &quot; (LAN1 Tagged).&quot;)</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">is_lan2_physical_gui_entry</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">pvid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">ensure_switch_vlan(pvid)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">update_switch_vlan_ports(pvid,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">false)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">remove_switch_vlan_port(pvid,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">true)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(&quot;Set</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">switch</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">port</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">as</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">untagged</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">for</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">VID</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">pvid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">(LAN2</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">PVID).&quot;)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">else</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">uci:foreach(&quot;network&quot;,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;switch_vlan&quot;,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">function(section)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">vid_on_switch</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">tonumber(section.vlan)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">vid_on_switch</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">remove_switch_vlan_port(vid_on_switch,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">false)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(&quot;Removed</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">any</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">untagged</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">VLAN</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">from</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">switch</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">port</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">(LAN2</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">PVID</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">removed).&quot;)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">for</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">_,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">vid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">in</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">ipairs(tagged_vlans)</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">do</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">ensure_switch_vlan(vid)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">update_switch_vlan_ports(vid,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">true)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">remove_switch_vlan_port(vid,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">false)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(&quot;Set</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">switch</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">port</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">as</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">tagged</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">for</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">VID</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">vid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">(LAN2</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">Tagged).&quot;)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSigMod">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; <span class="TextSegSigDiff">else</span>if is_wifi_iface then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if is_wifi_iface then</td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Handle wireless interface network assignment and vlan_tag option</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Handle wireless interface network assignment and vlan_tag option</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Configuring wireless interface: &quot; .. actual_ifname .. &quot; (UCI section: &quot; .. uci_wifi_iface_section .. &quot;)&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Configuring wireless interface: &quot; .. actual_ifname .. &quot; (UCI section: &quot; .. uci_wifi_iface_section .. &quot;)&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if pvid or #tagged_vlans &gt; 0 then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if pvid or #tagged_vlans &gt; 0 then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- If wireless interface has any VLAN config, ensure vlan_tag is set to 1</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- If wireless interface has any VLAN config, ensure vlan_tag is set to 1</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;wireless&quot;, uci_wifi_iface_section, &quot;vlan_tag&quot;, &quot;1&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;wireless&quot;, uci_wifi_iface_section, &quot;vlan_tag&quot;, &quot;1&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Set wireless.&quot; .. uci_wifi_iface_section .. &quot;.vlan_tag=1&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Set wireless.&quot; .. uci_wifi_iface_section .. &quot;.vlan_tag=1&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- If it has specific VLANs, ensure it's not tied to 'lan' or 'lan0' directly</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- If it has specific VLANs, ensure it's not tied to 'lan' or 'lan0' directly</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- It will be part of the specific VLAN bridge</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- It will be part of the specific VLAN bridge</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:delete(&quot;wireless&quot;, uci_wifi_iface_section, &quot;network&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:delete(&quot;wireless&quot;, uci_wifi_iface_section, &quot;network&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Deleted wireless.&quot; .. uci_wifi_iface_section .. &quot;.network as specific VLANs are configured.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Deleted wireless.&quot; .. uci_wifi_iface_section .. &quot;.network as specific VLANs are configured.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- If no specific VLANs, but management VLAN is active, assign to 'lan0'</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- If no specific VLANs, but management VLAN is active, assign to 'lan0'</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if management_vlan_id then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if management_vlan_id then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;wireless&quot;, uci_wifi_iface_section, &quot;network&quot;, &quot;lan0&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;wireless&quot;, uci_wifi_iface_section, &quot;network&quot;, &quot;lan0&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Set wireless.&quot; .. uci_wifi_iface_section .. &quot;.network=lan0 due to management VLAN.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Set wireless.&quot; .. uci_wifi_iface_section .. &quot;.network=lan0 due to management VLAN.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;wireless&quot;, uci_wifi_iface_section, &quot;vlan_tag&quot;, &quot;1&quot;) -- Still enable vlan_tag</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;wireless&quot;, uci_wifi_iface_section, &quot;vlan_tag&quot;, &quot;1&quot;) -- Still enable vlan_tag</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Otherwise, set to default 'lan' if it wasn't specified with VLANs</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Otherwise, set to default 'lan' if it wasn't specified with VLANs</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;wireless&quot;, uci_wifi_iface_section, &quot;network&quot;, &quot;lan&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;wireless&quot;, uci_wifi_iface_section, &quot;network&quot;, &quot;lan&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Set wireless.&quot; .. uci_wifi_iface_section .. &quot;.network=lan as no specific VLANs or management VLAN.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Set wireless.&quot; .. uci_wifi_iface_section .. &quot;.network=lan as no specific VLANs or management VLAN.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:delete(&quot;wireless&quot;, uci_wifi_iface_section, &quot;vlan_tag&quot;) -- Disable vlan_tag</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:delete(&quot;wireless&quot;, uci_wifi_iface_section, &quot;vlan_tag&quot;) -- Disable vlan_tag</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- For PVID on wireless interfaces, set the 'network' option to the VLAN bridge.</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- For PVID on wireless interfaces, set the 'network' option to the VLAN bridge.</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Note: wireless PVID is typically handled by setting 'network' to the VLAN bridge name,</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Note: wireless PVID is typically handled by setting 'network' to the VLAN bridge name,</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- not via switch_vlan ports.</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- not via switch_vlan ports.</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if pvid then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if pvid then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local vlan_bridge_name = &quot;vlan&quot; .. pvid</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local vlan_bridge_name = &quot;vlan&quot; .. pvid</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;wireless&quot;, uci_wifi_iface_section, &quot;network&quot;, vlan_bridge_name)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;wireless&quot;, uci_wifi_iface_section, &quot;network&quot;, vlan_bridge_name)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Set wireless.&quot; .. uci_wifi_iface_section .. &quot;.network=&quot; .. vlan_bridge_name .. &quot; for PVID.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Set wireless.&quot; .. uci_wifi_iface_section .. &quot;.network=&quot; .. vlan_bridge_name .. &quot; for PVID.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Ensure the wireless interface is added to the bridge's ifname list as well</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Ensure the wireless interface is added to the bridge's ifname list as well</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ensure_interface_bridge(vlan_bridge_name, &quot;none&quot;, &quot;bridge&quot;, actual_ifname) -- Add the wireless interface itself to the bridge</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ensure_interface_bridge(vlan_bridge_name, &quot;none&quot;, &quot;bridge&quot;, actual_ifname) -- Add the wireless interface itself to the bridge</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Added wireless interface '&quot; .. actual_ifname .. &quot;' to bridge '&quot; .. vlan_bridge_name .. &quot;' for PVID.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Added wireless interface '&quot; .. actual_ifname .. &quot;' to bridge '&quot; .. vlan_bridge_name .. &quot;' for PVID.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- For tagged_vlans on wireless interfaces, these are usually handled by the</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- For tagged_vlans on wireless interfaces, these are usually handled by the</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- wireless driver itself or by the bridge. We mainly ensure the 802.1q subinterface</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- wireless driver itself or by the bridge. We mainly ensure the 802.1q subinterface</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- is created and part of the correct bridge.</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- is created and part of the correct bridge.</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for _, vid in ipairs(tagged_vlans) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for _, vid in ipairs(tagged_vlans) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local vlan_device_name = actual_ifname .. &quot;.&quot; .. vid</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local vlan_device_name = actual_ifname .. &quot;.&quot; .. vid</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local vlan_bridge_name = &quot;vlan&quot; .. vid</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local vlan_bridge_name = &quot;vlan&quot; .. vid</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ensure_8021q_subinterface(vlan_device_name, actual_ifname, vid)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ensure_8021q_subinterface(vlan_device_name, actual_ifname, vid)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ensure_interface_bridge(vlan_bridge_name, &quot;none&quot;, &quot;bridge&quot;, vlan_device_name)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; ensure_interface_bridge(vlan_bridge_name, &quot;none&quot;, &quot;bridge&quot;, vlan_device_name)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Ensured wireless 802.1q subinterface &quot; .. vlan_device_name .. &quot; and bridge &quot; .. vlan_bridge_name .. &quot; for tagged VLAN &quot; .. vid .. &quot;.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Ensured wireless 802.1q subinterface &quot; .. vlan_device_name .. &quot; and bridge &quot; .. vlan_bridge_name .. &quot; for tagged VLAN &quot; .. vid .. &quot;.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end -- End for loop over interfaces in request</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end -- End for loop over interfaces in request</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Step 4: Cleanup Unconfigured Wireless Interfaces ---</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Step 4: Cleanup Unconfigured Wireless Interfaces ---</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Revert wireless interfaces not explicitly configured to 'lan' bridge and remove from other dynamic VLAN bridges</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Revert wireless interfaces not explicitly configured to 'lan' bridge and remove from other dynamic VLAN bridges</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for logical_name, details in pairs(interface_mappings) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for logical_name, details in pairs(interface_mappings) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if details.type == &quot;wifi&quot; and not configured_interfaces[logical_name] then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if details.type == &quot;wifi&quot; and not configured_interfaces[logical_name] then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local uci_wifi_iface_section = details.uci_section_name</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local uci_wifi_iface_section = details.uci_section_name</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local actual_ifname = details.ifname</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local actual_ifname = details.ifname</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if uci_wifi_iface_section and actual_ifname then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if uci_wifi_iface_section and actual_ifname then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local current_network = uci:get(&quot;wireless&quot;, uci_wifi_iface_section, &quot;network&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local current_network = uci:get(&quot;wireless&quot;, uci_wifi_iface_section, &quot;network&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if current_network and string.match(current_network, &quot;^br%-vlan(%d+)$&quot;) then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if current_network and string.match(current_network, &quot;^br%-vlan(%d+)$&quot;) then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;wireless&quot;, uci_wifi_iface_section, &quot;network&quot;, &quot;lan&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:set(&quot;wireless&quot;, uci_wifi_iface_section, &quot;network&quot;, &quot;lan&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Cleaned up wireless interface &quot; .. logical_name .. &quot; by reverting network to 'lan'.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Cleaned up wireless interface &quot; .. logical_name .. &quot; by reverting network to 'lan'.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Also remove from any br-vlanXXX bridges it might still be a member of</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Also remove from any br-vlanXXX bridges it might still be a member of</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;interface&quot;, function(section)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;interface&quot;, function(section)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if section.type == &quot;bridge&quot; and string.match(section.name or &quot;&quot;, &quot;^br%-vlan(%d+)$&quot;) then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if section.type == &quot;bridge&quot; and string.match(section.name or &quot;&quot;, &quot;^br%-vlan(%d+)$&quot;) then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug (cleanup unconfigured): Processing bridge: &quot; .. (section.name or &quot;nil_name&quot;) .. &quot; for iface: &quot; .. (actual_ifname or &quot;nil&quot;))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug (cleanup unconfigured): Processing bridge: &quot; .. (section.name or &quot;nil_name&quot;) .. &quot; for iface: &quot; .. (actual_ifname or &quot;nil&quot;))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Use our custom del_uci_list_item function</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Use our custom del_uci_list_item function</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; del_uci_list_item(&quot;network&quot;, section[&quot;.name&quot;], &quot;bridge_ports&quot;, actual_ifname)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; del_uci_list_item(&quot;network&quot;, section[&quot;.name&quot;], &quot;bridge_ports&quot;, actual_ifname)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Removed &quot; .. actual_ifname .. &quot; from bridge &quot; .. (section.name or &quot;N/A&quot;) .. &quot; (cleanup for unconfigured wireless).&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Removed &quot; .. actual_ifname .. &quot; from bridge &quot; .. (section.name or &quot;N/A&quot;) .. &quot; (cleanup for unconfigured wireless).&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Step 5: Commit and Restart ---</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Step 5: Commit and Restart ---</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:commit(&quot;network&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:commit(&quot;network&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Network UCI configuration committed.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Network UCI configuration committed.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:commit(&quot;wireless&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:commit(&quot;wireless&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Wireless UCI configuration committed.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Wireless UCI configuration committed.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Send response to Web interface immediately</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Send response to Web interface immediately</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; io.write(cjson.encode({</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; io.write(cjson.encode({</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; module = &quot;vlan&quot;, version = &quot;1.0&quot;, errcode = 0, sid = data.sid, result = { message = &quot;VLAN configuration applied successfully. Services are restarting...&quot; }</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; module = &quot;vlan&quot;, version = &quot;1.0&quot;, errcode = 0, sid = data.sid, result = { message = &quot;VLAN configuration applied successfully. Services are restarting...&quot; }</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; }))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; }))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Response sent to Web interface.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Response sent to Web interface.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local apply_mode = get_current_apply_mode()</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local apply_mode = get_current_apply_mode()</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Current apply mode: &quot; .. apply_mode)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Current apply mode: &quot; .. apply_mode)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if apply_mode == &quot;immediate&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if apply_mode == &quot;immediate&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Applying configuration immediately: restarting services.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Applying configuration immediately: restarting services.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Define the path for the temporary restart script</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Define the path for the temporary restart script</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local restart_script_path = &quot;/tmp/vlan_restart.sh&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local restart_script_path = &quot;/tmp/vlan_restart.sh&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Define the content of the restart script</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Define the content of the restart script</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local restart_script_content = [[#!/bin/sh</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local restart_script_content = [[#!/bin/sh</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">sleep 2</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">sleep 2</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">/etc/init.d/network restart</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">/etc/init.d/network restart</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">wifi</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">wifi</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">rm -f &quot;$0&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">rm -f &quot;$0&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">]]</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">]]</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Escape newlines and single quotes for echo -e</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Escape newlines and single quotes for echo -e</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local escaped_content = string.gsub(restart_script_content, &quot;'&quot;, &quot;'\''&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local escaped_content = string.gsub(restart_script_content, &quot;'&quot;, &quot;'\''&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; escaped_content = string.gsub(escaped_content, &quot;\n&quot;, &quot;\\n&quot;) -- Escape backslashes for lua string</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; escaped_content = string.gsub(escaped_content, &quot;\n&quot;, &quot;\\n&quot;) -- Escape backslashes for lua string</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Create the temporary restart script using /bin/echo -e</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Create the temporary restart script using /bin/echo -e</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Creating temporary restart script: &quot; .. restart_script_path .. &quot; using /bin/echo -e.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Creating temporary restart script: &quot; .. restart_script_path .. &quot; using /bin/echo -e.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local create_script_command = &quot;/bin/echo -e '&quot; .. escaped_content .. &quot;' &gt; &quot; .. restart_script_path</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local create_script_command = &quot;/bin/echo -e '&quot; .. escaped_content .. &quot;' &gt; &quot; .. restart_script_path</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local status_create, reason_create = sys.call(create_script_command)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local status_create, reason_create = sys.call(create_script_command)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if status_create ~= 0 then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if status_create ~= 0 then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Failed to create temporary restart script using /bin/echo -e. Status: &quot; .. status_create .. &quot;, Reason: &quot; .. (reason_create or &quot;N/A&quot;))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Failed to create temporary restart script using /bin/echo -e. Status: &quot; .. status_create .. &quot;, Reason: &quot; .. (reason_create or &quot;N/A&quot;))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Make the script executable</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Make the script executable</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Making script executable: &quot; .. restart_script_path)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Making script executable: &quot; .. restart_script_path)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local chmod_command = &quot;/bin/chmod +x &quot; .. restart_script_path</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local chmod_command = &quot;/bin/chmod +x &quot; .. restart_script_path</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local status_chmod, reason_chmod = sys.call(chmod_command)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local status_chmod, reason_chmod = sys.call(chmod_command)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if status_chmod ~= 0 then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if status_chmod ~= 0 then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Failed to make script executable. Status: &quot; .. status_chmod .. &quot;, Reason: &quot; .. (reason_chmod or &quot;N/A&quot;))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Failed to make script executable. Status: &quot; .. status_chmod .. &quot;, Reason: &quot; .. (reason_chmod or &quot;N/A&quot;))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Finally, execute the script in the background</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Finally, execute the script in the background</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Scheduling background restart: &quot; .. restart_script_path .. &quot; &amp;&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Scheduling background restart: &quot; .. restart_script_path .. &quot; &amp;&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local execute_script_command = restart_script_path .. &quot; &amp;&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local execute_script_command = restart_script_path .. &quot; &amp;&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local status_execute, reason_execute = sys.call(execute_script_command)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local status_execute, reason_execute = sys.call(execute_script_command)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if status_execute ~= 0 then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if status_execute ~= 0 then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Failed to schedule background service restarts. Status: &quot; .. status_execute .. &quot;, Reason: &quot; .. (reason_execute or &quot;N/A&quot;))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Failed to schedule background service restarts. Status: &quot; .. status_execute .. &quot;, Reason: &quot; .. (reason_execute or &quot;N/A&quot;))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Background service restarts scheduled successfully.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Background service restarts scheduled successfully.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Apply mode is deferred. Services will not be restarted automatically.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Apply mode is deferred. Services will not be restarted automatically.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Update the message to reflect deferred application</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- Update the message to reflect deferred application</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; io.write(cjson.encode({</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; io.write(cjson.encode({</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; module = &quot;vlan&quot;, version = &quot;1.0&quot;, errcode = 0, sid = data.sid, result = { message = &quot;VLAN configuration saved. Apply deferred.&quot; }</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; module = &quot;vlan&quot;, version = &quot;1.0&quot;, errcode = 0, sid = data.sid, result = { message = &quot;VLAN configuration saved. Apply deferred.&quot; }</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; }))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; }))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; io.flush()</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; io.flush()</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">-- get_vlan function (updated to support new JSON format and actual network config)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">-- get_vlan function (updated to support new JSON format and actual network config)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">function get_vlan(data)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">function get_vlan(data)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;DEBUG: get_vlan function entered.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;DEBUG: get_vlan function entered.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Starting get_vlan (updated logic).&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Starting get_vlan (updated logic).&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local result = {</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local result = {</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; management_vlan_id = nil,</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; management_vlan_id = nil,</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; interfaces = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; interfaces = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; }</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; }</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local interface_mappings = get_interface_mappings()</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local interface_mappings = get_interface_mappings()</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_physical_ifname_base = &quot;eth1&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_physical_ifname_base = &quot;eth1&quot;</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_physical_switch_port_index = interface_mappings[&quot;LAN1&quot;].physical_switch_port_index</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_physical_switch_port_index = interface_mappings[&quot;LAN1&quot;].physical_switch_port_index</td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSigMod">&nbsp;</td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">interface_mappings[&quot;LAN2&quot;].physical_lan2_switch_port_index</span></td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(&quot;DEBUG:</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">interface_mappings</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">cjson.encode(interface_mappings))</span></td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Infer Management VLAN ID ---</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Infer Management VLAN ID ---</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Check if 'lan' interface's ifname points to an eth1.VLANID</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- Check if 'lan' interface's ifname points to an eth1.VLANID</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_uci_section = nil</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local lan_uci_section = nil</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;interface&quot;, function(section)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;interface&quot;, function(section)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: Foreach processing section.type: &quot; .. (section[&quot;.type&quot;] or &quot;nil_type&quot;) .. &quot;, section['.name']: '&quot; .. (section[&quot;.name&quot;] or &quot;nil_name_internal&quot;) .. &quot;', section.name: '&quot; .. (section.name or &quot;nil_name_option&quot;) .. &quot;'&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: Foreach processing section.type: &quot; .. (section[&quot;.type&quot;] or &quot;nil_type&quot;) .. &quot;, section['.name']: '&quot; .. (section[&quot;.name&quot;] or &quot;nil_name_internal&quot;) .. &quot;', section.name: '&quot; .. (section.name or &quot;nil_name_option&quot;) .. &quot;'&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if section[&quot;.name&quot;] == &quot;lan&quot; then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if section[&quot;.name&quot;] == &quot;lan&quot; then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; lan_uci_section = section</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; lan_uci_section = section</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: Found lan_uci_section via foreach (using .name): &quot; .. section[&quot;.name&quot;])</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: Found lan_uci_section via foreach (using .name): &quot; .. section[&quot;.name&quot;])</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return false -- Stop iteration after finding it</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; return false -- Stop iteration after finding it</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Debug: lan_uci_section result: &quot; .. (lan_uci_section and &quot;found&quot; or &quot;nil&quot;))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Debug: lan_uci_section result: &quot; .. (lan_uci_section and &quot;found&quot; or &quot;nil&quot;))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if lan_uci_section then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; if lan_uci_section then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local lan_ifname_list_str = uci:get(&quot;network&quot;, lan_uci_section[&quot;.name&quot;], &quot;ifname&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local lan_ifname_list_str = uci:get(&quot;network&quot;, lan_uci_section[&quot;.name&quot;], &quot;ifname&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: lan_ifname_list_str = '&quot; .. (lan_ifname_list_str or &quot;nil&quot;) .. &quot;'&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: lan_ifname_list_str = '&quot; .. (lan_ifname_list_str or &quot;nil&quot;) .. &quot;'&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if lan_ifname_list_str then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if lan_ifname_list_str then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Split the ifname string into individual interfaces and check for eth1.VLANID</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Split the ifname string into individual interfaces and check for eth1.VLANID</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for iface_entry in string.gmatch(lan_ifname_list_str, &quot;[^%s]+&quot;) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for iface_entry in string.gmatch(lan_ifname_list_str, &quot;[^%s]+&quot;) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: Processing iface_entry = '&quot; .. iface_entry .. &quot;'&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: Processing iface_entry = '&quot; .. iface_entry .. &quot;'&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local mgmt_vid_str = string.match(iface_entry, &quot;^&quot; .. lan_physical_ifname_base .. &quot;%.(%d+)$&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local mgmt_vid_str = string.match(iface_entry, &quot;^&quot; .. lan_physical_ifname_base .. &quot;%.(%d+)$&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: string.match result for &quot; .. iface_entry .. &quot; = '&quot; .. (mgmt_vid_str or &quot;nil&quot;) .. &quot;'&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: string.match result for &quot; .. iface_entry .. &quot; = '&quot; .. (mgmt_vid_str or &quot;nil&quot;) .. &quot;'&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local mgmt_vid = tonumber(mgmt_vid_str)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local mgmt_vid = tonumber(mgmt_vid_str)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: tonumber result for &quot; .. iface_entry .. &quot; = &quot; .. (mgmt_vid or &quot;nil&quot;))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: tonumber result for &quot; .. iface_entry .. &quot; = &quot; .. (mgmt_vid or &quot;nil&quot;))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if mgmt_vid then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if mgmt_vid then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; result.management_vlan_id = mgmt_vid</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; result.management_vlan_id = mgmt_vid</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Inferred management VLAN ID: &quot; .. mgmt_vid .. &quot; from 'lan' interface ifname: &quot; .. iface_entry)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Inferred management VLAN ID: &quot; .. mgmt_vid .. &quot; from 'lan' interface ifname: &quot; .. iface_entry)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; break -- Found it, no need to check further</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; break -- Found it, no need to check further</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: lan_ifname_list_str is nil or empty.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: lan_ifname_list_str is nil or empty.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: lan_uci_section for 'lan' interface not found.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Debug: lan_uci_section for 'lan' interface not found.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Determined Management VLAN ID: &quot; .. (result.management_vlan_id or &quot;None&quot;))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Determined Management VLAN ID: &quot; .. (result.management_vlan_id or &quot;None&quot;))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Populate vlan_config_from_switch ---</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Populate vlan_config_from_switch ---</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local vlan_config_from_switch = {} -- { vid = { tagged = {port_idx, ...}, untagged = {port_idx, ...} } }</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; local vlan_config_from_switch = {} -- { vid = { tagged = {port_idx, ...}, untagged = {port_idx, ...} } }</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;switch_vlan&quot;, function(section)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;switch_vlan&quot;, function(section)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local vid = tonumber(section.vlan)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local vid = tonumber(section.vlan)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local ports_str = section.ports or &quot;&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local ports_str = section.ports or &quot;&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if vid then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if vid then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; vlan_config_from_switch[vid] = { tagged = {}, untagged = {} }</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; vlan_config_from_switch[vid] = { tagged = {}, untagged = {} }</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; for port_entry in string.gmatch(ports_str, &quot;[^%s]+&quot;) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; for port_entry in string.gmatch(ports_str, &quot;[^%s]+&quot;) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; if string.match(port_entry, &quot;t$&quot;) then -- Tagged port (e.g., '1t', '0t')</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; if string.match(port_entry, &quot;t$&quot;) then -- Tagged port (e.g., '1t', '0t')</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; table.insert(vlan_config_from_switch[vid].tagged, string.sub(port_entry, 1, -2))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; table.insert(vlan_config_from_switch[vid].tagged, string.sub(port_entry, 1, -2))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; else -- Untagged port (eg., '1')</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; else -- Untagged port (eg., '1')</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; table.insert(vlan_config_from_switch[vid].untagged, port_entry)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; table.insert(vlan_config_from_switch[vid].untagged, port_entry)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;VLAN config from switch_vlan: &quot; .. cjson.encode(vlan_config_from_switch))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;VLAN config from switch_vlan: &quot; .. cjson.encode(vlan_config_from_switch))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Infer Per-Interface VLANs ---</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; -- --- Infer Per-Interface VLANs ---</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for logical_name, iface_details in pairs(interface_mappings) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; for logical_name, iface_details in pairs(interface_mappings) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local current_pvid = nil</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local current_pvid = nil</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local current_tagged_vlans = {}</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local current_tagged_vlans = {}</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local iface_config_found = false</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local iface_config_found = false</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local is_lan_physical_gui_entry = (iface_details.type == &quot;lan_physical_port&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local is_lan_physical_gui_entry = (iface_details.type == &quot;lan_physical_port&quot;)</td>
</tr>
<tr class="SectionAll">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">-+</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">is_lan2_physical_gui_entry</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">(iface_details.type</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">==</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;lan2_physical_port&quot;)</span></td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local is_wifi_iface = (iface_details.type == &quot;wifi&quot;)</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local is_wifi_iface = (iface_details.type == &quot;wifi&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local actual_ifname = iface_details.ifname -- For wireless interfaces</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local actual_ifname = iface_details.ifname -- For wireless interfaces</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local uci_wifi_iface_section = iface_details.uci_section_name</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; local uci_wifi_iface_section = iface_details.uci_section_name</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Inferring VLAN for logical interface: &quot; .. logical_name)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Inferring VLAN for logical interface: &quot; .. logical_name)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- --- Infer for Physical LAN Port (LAN1 GUI entry) ---</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- --- Infer for Physical LAN Port (LAN1 GUI entry) ---</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if is_lan_physical_gui_entry then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if is_lan_physical_gui_entry then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Infer PVID from switch_vlan for physical port 1</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Infer PVID from switch_vlan for physical port 1</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for vid, config in pairs(vlan_config_from_switch) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for vid, config in pairs(vlan_config_from_switch) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if config and config.untagged then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if config and config.untagged then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for _, port_idx in ipairs(config.untagged) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for _, port_idx in ipairs(config.untagged) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if port_idx == lan_physical_switch_port_index then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if port_idx == lan_physical_switch_port_index then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; current_pvid = vid</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; current_pvid = vid</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Inferred PVID &quot; .. current_pvid .. &quot; for &quot; .. logical_name .. &quot; from switch_vlan (untagged).&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Inferred PVID &quot; .. current_pvid .. &quot; for &quot; .. logical_name .. &quot; from switch_vlan (untagged).&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; break</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; break</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if current_pvid then break end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if current_pvid then break end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Infer Tagged VLANs from switch_vlan for physical port 1</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Infer Tagged VLANs from switch_vlan for physical port 1</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for vid, config in pairs(vlan_config_from_switch) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for vid, config in pairs(vlan_config_from_switch) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if config and config.tagged then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if config and config.tagged then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for _, port_idx in ipairs(config.tagged) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for _, port_idx in ipairs(config.tagged) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if port_idx == lan_physical_switch_port_index then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if port_idx == lan_physical_switch_port_index then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if vid ~= current_pvid then -- Exclude PVID from tagged list</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if vid ~= current_pvid then -- Exclude PVID from tagged list</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(current_tagged_vlans, vid)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(current_tagged_vlans, vid)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; break</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; break</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.sort(current_tagged_vlans)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.sort(current_tagged_vlans)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; iface_config_found = true</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; iface_config_found = true</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">-+</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">is_lan2_physical_gui_entry</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">Infer</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">PVID</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">from</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">switch_vlan</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">for</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">physical</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">port</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">1</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">for</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">vid,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">config</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">in</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">pairs(vlan_config_from_switch)</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">do</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">config</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">and</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">config.untagged</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">for</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">_,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">port_idx</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">in</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">ipairs(config.untagged)</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">do</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">port_idx</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">==</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">current_pvid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">vid</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(&quot;Inferred</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">PVID</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">current_pvid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">for</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">logical_name</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">from</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">switch_vlan</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">(untagged).&quot;)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">break</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">current_pvid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">break</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">Infer</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">Tagged</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">VLANs</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">from</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">switch_vlan</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">for</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">physical</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">port</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">1</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">for</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">vid,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">config</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">in</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">pairs(vlan_config_from_switch)</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">do</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">config</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">and</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">config.tagged</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">for</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">_,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">port_idx</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">in</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">ipairs(config.tagged)</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">do</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">port_idx</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">==</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">lan2_physical_switch_port_index</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">vid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">~=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">current_pvid</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">Exclude</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">PVID</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">from</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">tagged</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">list</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">table.insert(current_tagged_vlans,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">vid)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">break</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">table.sort(current_tagged_vlans)</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">iface_config_found</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">true</span></td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">end</span></td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- --- Infer for Wireless and Mesh Interfaces ---</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; -- --- Infer for Wireless and Mesh Interfaces ---</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if is_wifi_iface and uci_wifi_iface_section and actual_ifname then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if is_wifi_iface and uci_wifi_iface_section and actual_ifname then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local network_option = uci:get(&quot;wireless&quot;, uci_wifi_iface_section, &quot;network&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local network_option = uci:get(&quot;wireless&quot;, uci_wifi_iface_section, &quot;network&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if network_option then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if network_option then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local pvid_from_network = tonumber(string.match(network_option, &quot;^br%-vlan(%d+)$&quot;))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local pvid_from_network = tonumber(string.match(network_option, &quot;^br%-vlan(%d+)$&quot;))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if pvid_from_network then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if pvid_from_network then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; current_pvid = pvid_from_network</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; current_pvid = pvid_from_network</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Inferred PVID &quot; .. current_pvid .. &quot; for &quot; .. logical_name .. &quot; from wireless.network option.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;Inferred PVID &quot; .. current_pvid .. &quot; for &quot; .. logical_name .. &quot; from wireless.network option.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Infer tagged VLANs from bridge memberships (br-vlanXXX)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Infer tagged VLANs from bridge memberships (br-vlanXXX)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;interface&quot;, function(section)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; uci:foreach(&quot;network&quot;, &quot;interface&quot;, function(section)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local bridge_name = section.name or &quot;&quot;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local bridge_name = section.name or &quot;&quot;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if section.type == &quot;bridge&quot; and string.match(bridge_name, &quot;^br%-vlan(%d+)$&quot;) then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if section.type == &quot;bridge&quot; and string.match(bridge_name, &quot;^br%-vlan(%d+)$&quot;) then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local vid = tonumber(string.match(bridge_name, &quot;^br%-vlan(%d+)$&quot;))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local vid = tonumber(string.match(bridge_name, &quot;^br%-vlan(%d+)$&quot;))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if vid then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if vid then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Check if the actual wireless interface is a member of this bridge</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; -- Check if the actual wireless interface is a member of this bridge</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local bridge_ports = uci:get_list(&quot;network&quot;, section[&quot;.name&quot;], &quot;bridge_ports&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; local bridge_ports = uci:get_list(&quot;network&quot;, section[&quot;.name&quot;], &quot;bridge_ports&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for _, port_member in ipairs(bridge_ports) do</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; for _, port_member in ipairs(bridge_ports) do</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if port_member == actual_ifname then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if port_member == actual_ifname then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if vid ~= current_pvid then -- Exclude PVID from tagged list</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; if vid ~= current_pvid then -- Exclude PVID from tagged list</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(current_tagged_vlans, vid)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(current_tagged_vlans, vid)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; break</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; break</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; end)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.sort(current_tagged_vlans)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.sort(current_tagged_vlans)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; iface_config_found = true</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; iface_config_found = true</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if iface_config_found then</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; if iface_config_found then</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(result.interfaces, {</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; table.insert(result.interfaces, {</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; name = logical_name,</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; name = logical_name,</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; pvid = current_pvid,</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; pvid = current_pvid,</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; tagged_vlans = current_tagged_vlans</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; tagged_vlans = current_tagged_vlans</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; })</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; })</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; else</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;No VLAN configuration found for &quot; .. logical_name .. &quot;. Skipping addition to result.&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; write_log(&quot;No VLAN configuration found for &quot; .. logical_name .. &quot;. Skipping addition to result.&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Final VLAN configuration retrieved: &quot; .. cjson.encode(result))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; write_log(&quot;Final VLAN configuration retrieved: &quot; .. cjson.encode(result))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; io.write(cjson.encode({</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; io.write(cjson.encode({</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; module = &quot;vlan&quot;, version = &quot;1.0&quot;, errcode = 0, sid = data.sid, result = result</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; module = &quot;vlan&quot;, version = &quot;1.0&quot;, errcode = 0, sid = data.sid, result = result</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; }))</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;&nbsp;&nbsp; }))</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">end</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionBegin">
<td class="TextItemSigMod"><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">检查是否在CGI环境中运行</span></td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">function</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">is_cgi()</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">return</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">os.getenv(&quot;REQUEST_METHOD&quot;)</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">~=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">nil</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegSigDiff">end</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">Execute</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">the</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">API</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">route</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">AC</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">调用接口</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">M</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">{}</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSigMod"><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">显式调用入口函数</span></td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegSigDiff">function</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">M.set_config_from_ac(payload)</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(&quot;[AC]</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">set_config_from_ac</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">called:</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">..</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">cjson.encode(payload))</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">调用现有的set_vlan函数</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">data</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">{</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">params</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">payload</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">}</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">set_vlan(data)</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(&quot;[AC]</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">VLAN</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">configuration</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">applied&quot;)</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">return</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">true,</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">&quot;VLAN</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">configuration</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">applied</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">by</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">AC&quot;</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegSigDiff">end</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegSigDiff">function</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">M.get_config_for_ac()</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">local</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">config</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">{</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">vlans</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">get_vlan(),</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; </span><span class="TextSegSigDiff">interface_mappings</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">=</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">get_interface_mappings()</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">}</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">write_log(&quot;[AC]</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">get_config_for_ac:</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">retrieved</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">VLAN</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">config&quot;)</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span><span class="TextSegSigDiff">return</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">config</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegSigDiff">end</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegSigDiff">--</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">仅在明确作为</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">CGI</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">脚本运行时执行</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSigMod"><span class="TextSegSigDiff">if</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">arg</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">and</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">arg[0]</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">and</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">arg[0]:match(&quot;vlan%.lua&quot;)</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">and</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">is_cgi()</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">then</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span>local function run()</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">local function run()</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod">&nbsp;&nbsp; &nbsp;<span class="TextSegInsigDiff">&nbsp; &nbsp; </span>write_log(&quot;VLAN API started&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;&nbsp;&nbsp; write_log(&quot;VLAN API started&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod">&nbsp;&nbsp; &nbsp;<span class="TextSegInsigDiff">&nbsp; &nbsp; </span>route_api()</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;&nbsp;&nbsp; route_api()</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod">&nbsp;&nbsp; &nbsp;<span class="TextSegInsigDiff">&nbsp; &nbsp; </span>write_log(&quot;VLAN API finished&quot;)</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;&nbsp;&nbsp; write_log(&quot;VLAN API finished&quot;)</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span>end</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">end</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">&nbsp;</td>
</tr>
<tr class="SectionMiddle">
<td class="TextItemInsigMod"><span class="TextSegInsigDiff">&nbsp;&nbsp;&nbsp; </span>run()</td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemInsigMod">run()</td>
</tr>
<tr class="SectionEnd">
<td class="TextItemSigMod"><span class="TextSegSigDiff">end</span></td>
<td class="AlignCenter">&nbsp;</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionAll">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionAll">
<td class="TextItemSigMod"><span class="TextSegSigDiff">return</span><span class="TextSegInsigDiff"> </span><span class="TextSegSigDiff">M</span></td>
<td class="AlignCenter">+-</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionAll">
<td class="TextItemSame">&nbsp;</td>
<td class="AlignCenter">=</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
<tr class="SectionAll">
<td class="TextItemInsigMod">&nbsp;</td>
<td class="AlignCenter">+-</td>
<td class="TextItemSame">&nbsp;</td>
</tr>
</table>
<br/>
</body>
</html>
