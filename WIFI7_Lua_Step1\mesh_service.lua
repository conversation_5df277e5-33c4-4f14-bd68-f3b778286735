#!/usr/bin/lua

--[[
WiFi7 Mesh Service Control Module
基于WiFi6 son_topo实现，为WiFi7设备提供mesh服务控制功能
参考: package/son_topo/files/cap.sh 和 re.sh
作者: WiFi7开发团队
日期: 2025-07-07
]]

local uci = require("uci")
local json = require("json")

local mesh_service = {}

-- 检测是否为CGI环境
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- 日志函数
local function log_message(level, message)
    if is_cgi() then
        os.execute(string.format("logger -t mesh_service '[%s] %s'", level, message))
    else
        print(string.format("[%s] %s", level, message))
    end
end

-- 执行系统命令并返回结果
local function execute_command(cmd)
    log_message("DEBUG", "Executing: " .. cmd)
    local handle = io.popen(cmd .. " 2>&1")
    local result = handle:read("*a")
    local success = handle:close()
    log_message("DEBUG", "Command result: " .. (result or ""))
    return success, result
end

-- 检查服务状态
local function check_service_status(service_name)
    local cmd = string.format("ps | grep -v grep | grep %s", service_name)
    local success, result = execute_command(cmd)
    return success and result and #result > 0
end

-- 停止mesh相关服务
function mesh_service.stop_mesh_services()
    log_message("INFO", "Stopping mesh services")

    local services = {"wsplcd", "hyd", "repacd", "son_topo"}
    local results = {}

    for _, service in ipairs(services) do
        local cmd = string.format("/etc/init.d/%s stop", service)
        local success, output = execute_command(cmd)
        results[service] = {success = success, output = output}

        -- 强制杀死进程
        if service == "son_topo" then
            execute_command("killall -9 son_topo 2>/dev/null")
        end
    end

    -- 停止无线
    execute_command("wifi down")

    return results
end

-- 启动mesh相关服务
function mesh_service.start_mesh_services()
    log_message("INFO", "Starting mesh services")

    local services = {"wsplcd", "hyd", "repacd"}
    local results = {}

    for _, service in ipairs(services) do
        local cmd = string.format("/etc/init.d/%s start", service)
        local success, output = execute_command(cmd)
        results[service] = {success = success, output = output}
    end

    return results
end

-- 重启mesh相关服务
function mesh_service.restart_mesh_services()
    log_message("INFO", "Restarting mesh services")

    mesh_service.stop_mesh_services()
    -- 等待服务完全停止
    os.execute("sleep 2")
    return mesh_service.start_mesh_services()
end

-- 配置无线接口为mesh模式
function mesh_service.configure_wireless_for_mesh(mesh_ssid, mesh_password, is_cap_mode)
    log_message("INFO", "Configuring wireless for mesh mode")

    local cursor = uci.cursor()

    -- 删除现有的无线接口配置
    local interfaces_to_delete = {
        "wlan1", "wlan2", "wlan3", "wlan4", "wlan5", "wlan6", "wlan7",
        "wlan9", "wlan10", "wlan11", "wlan12", "wlan13", "wlan14", "wlan15",
        "wwan", "wifi_roam", "vif_monitor", "vif_monitor_5g"
    }

    for _, iface in ipairs(interfaces_to_delete) do
        cursor:delete("wireless", iface)
    end

    -- 启用radio
    cursor:set("wireless", "radio0", "disabled", "0")
    cursor:set("wireless", "radio1", "disabled", "0")
    cursor:set("wireless", "radio0", "dbdc_enable", "1")
    cursor:set("wireless", "radio1", "dbdc_enable", "1")

    -- 检测芯片类型并设置相应配置
    local firmware_check = io.popen("ls /lib/firmware/ 2>/dev/null")
    local firmware_list = firmware_check:read("*a")
    firmware_check:close()

    if string.find(firmware_list, "IPQ6018") then
        cursor:set("wireless", "radio0", "primaryradio", "1")
        cursor:set("wireless", "radio0", "htmode", "VHT80")
        cursor:set("wireless", "radio1", "htmode", "HT20")
    elseif string.find(firmware_list, "IPQ5018") then
        cursor:set("wireless", "radio1", "primaryradio", "1")
        cursor:set("wireless", "radio0", "htmode", "HT20")
        cursor:set("wireless", "radio1", "htmode", "VHT80")
    end

    if is_cap_mode then
        -- 主AP模式配置
        mesh_service.configure_cap_mode(cursor, mesh_ssid, mesh_password)
    else
        -- 从AP模式配置
        mesh_service.configure_re_mode(cursor)
    end

    cursor:commit("wireless")
    return true
end

-- 配置主AP(CAP)模式
function mesh_service.configure_cap_mode(cursor, mesh_ssid, mesh_password)
    log_message("INFO", "Configuring CAP mode")

    -- 配置2.4G接口 (wlan0)
    cursor:set("wireless", "wlan0", "ssid", mesh_ssid)
    cursor:set("wireless", "wlan0", "hidden", "0")
    cursor:set("wireless", "wlan0", "encryption", "psk2+ccmp")
    cursor:set("wireless", "wlan0", "sae", "1")
    cursor:set("wireless", "wlan0", "key", mesh_password)
    cursor:set("wireless", "wlan0", "force_invalid_group", "1")
    cursor:set("wireless", "wlan0", "sae_confirm_immediate", "1")
    cursor:set("wireless", "wlan0", "ieee80211w", "1")
    cursor:set("wireless", "wlan0", "backhaul", "1")
    cursor:set("wireless", "wlan0", "wnm", "1")

    -- 配置5G接口 (wlan8)
    cursor:set("wireless", "wlan8", "ssid", mesh_ssid)
    cursor:set("wireless", "wlan8", "hidden", "0")
    cursor:set("wireless", "wlan8", "encryption", "psk2+ccmp")
    cursor:set("wireless", "wlan8", "sae", "1")
    cursor:set("wireless", "wlan8", "key", mesh_password)
    cursor:set("wireless", "wlan8", "force_invalid_group", "1")
    cursor:set("wireless", "wlan8", "sae_confirm_immediate", "1")
    cursor:set("wireless", "wlan8", "ieee80211w", "1")
    cursor:set("wireless", "wlan8", "backhaul", "1")
    cursor:set("wireless", "wlan8", "wnm", "1")

    -- 删除不需要的配置项
    local items_to_delete = {"ifname", "ieee80211r", "wpa_group_rekey", "wpa_pair_rekey", "wpa_master_rekey"}
    for _, item in ipairs(items_to_delete) do
        cursor:delete("wireless", "wlan0", item)
        cursor:delete("wireless", "wlan8", item)
    end
end

-- 配置从AP(RE)模式
function mesh_service.configure_re_mode(cursor)
    log_message("INFO", "Configuring RE mode")

    -- 删除所有wlan接口
    local wlan_interfaces = {"wlan0", "wlan1", "wlan2", "wlan3", "wlan4", "wlan5", "wlan6", "wlan7", "wlan8"}
    for _, iface in ipairs(wlan_interfaces) do
        cursor:delete("wireless", iface)
    end

    -- 设置自动信道选择
    cursor:set("wireless", "radio0", "channel", "auto")
    cursor:set("wireless", "radio1", "channel", "auto")
end

-- 配置mesh相关服务
function mesh_service.configure_mesh_services(is_cap_mode)
    log_message("INFO", "Configuring mesh services")

    local cursor = uci.cursor()

    -- 配置wsplcd
    cursor:set("wsplcd", "config", "HyFiSecurity", "1")
    cursor:set("wsplcd", "config", "WriteDebugLogToFile", "NONE")
    cursor:set("wsplcd", "config", "DebugLevel", "DEBUG")
    cursor:commit("wsplcd")

    -- 配置hyd
    cursor:set("hyd", "config", "Enable", "1")
    cursor:set("hyd", "Topology", "ENABLE_NOTIFICATION_UNICAST", "1")
    cursor:set("hyd", "IEEE1905Settings", "AvoidDupRenew", "1")
    cursor:set("hyd", "IEEE1905Settings", "AvoidDupTopologyNotification", "1")
    if not is_cap_mode then
        cursor:set("hyd", "Topology", "PERIODIC_QUERY_INTERVAL", "15")
    end
    cursor:commit("hyd")

    -- 配置repacd
    cursor:set("repacd", "repacd", "Enable", "1")
    cursor:set("repacd", "repacd", "ConfigREMode", "son")
    cursor:set("repacd", "repacd", "DefaultREMode", "son")
    cursor:set("repacd", "WiFiLink", "DaisyChain", "1")

    if is_cap_mode then
        cursor:set("repacd", "repacd", "GatewayConnectedMode", "CAP")
    else
        cursor:set("repacd", "WiFiLink", "ManageVAPInd", "1")
    end

    cursor:commit("repacd")

    return true
end

-- 启用mesh模式
function mesh_service.enable_mesh_mode(mesh_ssid, mesh_password, is_cap_mode, gateway_config)
    log_message("INFO", "Enabling mesh mode")

    -- 停止现有服务
    mesh_service.stop_mesh_services()

    -- 配置无线接口
    mesh_service.configure_wireless_for_mesh(mesh_ssid, mesh_password, is_cap_mode)

    -- 配置mesh服务
    mesh_service.configure_mesh_services(is_cap_mode)

    -- 如果是主AP模式，配置网络
    if is_cap_mode and gateway_config then
        mesh_service.configure_gateway_network(gateway_config)
    end

    -- 启动mesh服务
    mesh_service.start_mesh_services()

    return true
end

-- 禁用mesh模式
function mesh_service.disable_mesh_mode()
    log_message("INFO", "Disabling mesh mode")

    -- 停止mesh服务
    mesh_service.stop_mesh_services()

    -- 恢复默认无线配置
    local cursor = uci.cursor()

    -- 检查是否有备份配置
    local backup_exists = os.execute("test -f /etc/wireless.tmp") == 0
    if backup_exists then
        log_message("INFO", "Restoring wireless configuration from backup")
        os.execute("cp /etc/wireless.tmp /etc/config/wireless")
    end

    cursor:commit("wireless")

    return true
end

-- 配置网关网络
function mesh_service.configure_gateway_network(gateway_config)
    log_message("INFO", "Configuring gateway network")

    local cursor = uci.cursor()

    if gateway_config.wan_type == "dhcp" then
        cursor:set("network", "wan", "proto", "dhcp")
        cursor:delete("network", "wan", "ipaddr")
        cursor:delete("network", "wan", "netmask")
        cursor:delete("network", "wan", "gateway")
        cursor:delete("network", "wan", "dns")

    elseif gateway_config.wan_type == "static" then
        cursor:set("network", "wan", "proto", "static")
        cursor:set("network", "wan", "ipaddr", gateway_config.ipaddr or "*************")
        cursor:set("network", "wan", "netmask", gateway_config.netmask or "*************")
        cursor:set("network", "wan", "gateway", gateway_config.gateway or "***********")
        cursor:set("network", "wan", "dns", gateway_config.dns or "******* *******")

    elseif gateway_config.wan_type == "pppoe" then
        cursor:set("network", "wan", "proto", "pppoe")
        cursor:set("network", "wan", "username", gateway_config.username or "")
        cursor:set("network", "wan", "password", gateway_config.password or "")
        cursor:delete("network", "wan", "ipaddr")
        cursor:delete("network", "wan", "netmask")
        cursor:delete("network", "wan", "gateway")
    end

    cursor:commit("network")

    return true
end

-- 获取mesh服务状态
function mesh_service.get_service_status()
    local status = {}
    local services = {"wsplcd", "hyd", "repacd", "son_topo"}

    for _, service in ipairs(services) do
        status[service] = check_service_status(service)
    end

    return status
end

-- CGI接口处理
function mesh_service.handle_cgi()
    if not is_cgi() then
        return
    end

    -- 设置HTTP头
    print("Content-Type: application/json")
    print("Cache-Control: no-cache")
    print("")

    local method = os.getenv("REQUEST_METHOD")
    local query_string = os.getenv("QUERY_STRING") or ""
    local response = {success = false, message = "Unknown error"}

    if method == "GET" then
        if string.find(query_string, "action=status") then
            -- 获取服务状态
            local status = mesh_service.get_service_status()
            response = {success = true, data = status}
        else
            response = {success = false, message = "Invalid action"}
        end

    elseif method == "POST" then
        -- 处理mesh服务控制
        local content_length = tonumber(os.getenv("CONTENT_LENGTH")) or 0
        if content_length > 0 then
            local post_data = io.read(content_length)
            local success, data = pcall(json.decode, post_data)

            if success and data then
                if data.action == "enable" then
                    local result = mesh_service.enable_mesh_mode(
                        data.mesh_ssid, data.mesh_password,
                        data.is_cap_mode, data.gateway_config
                    )
                    response = {success = result, message = result and "Mesh enabled" or "Failed to enable mesh"}

                elseif data.action == "disable" then
                    local result = mesh_service.disable_mesh_mode()
                    response = {success = result, message = result and "Mesh disabled" or "Failed to disable mesh"}

                elseif data.action == "restart" then
                    local result = mesh_service.restart_mesh_services()
                    response = {success = true, data = result, message = "Services restarted"}

                else
                    response = {success = false, message = "Invalid action"}
                end
            else
                response = {success = false, message = "Invalid JSON data"}
            end
        else
            response = {success = false, message = "No data provided"}
        end
    end

    print(json.encode(response))
end

-- 如果作为CGI运行，处理请求
if is_cgi() then
    mesh_service.handle_cgi()
end

return mesh_service