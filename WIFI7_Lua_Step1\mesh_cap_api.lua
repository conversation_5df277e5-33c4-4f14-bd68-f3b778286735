#!/usr/bin/lua

--[[
WiFi7 Mesh CAP (Controller Access Point) API
基于WiFi6 son_topo实现，为WiFi7设备提供主AP配置API
参考: package/son_topo/files/cap.sh
作者: WiFi7开发团队
日期: 2025-01-07
]]

local uci = require("uci")
local json = require("json")

-- 引入mesh相关模块
local mesh_config = require("mesh_config")
local mesh_service = require("mesh_service")

local mesh_cap_api = {}

-- 检测是否为CGI环境
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- 日志函数
local function log_message(level, message)
    if is_cgi() then
        os.execute(string.format("logger -t mesh_cap_api '[%s] %s'", level, message))
    else
        print(string.format("[%s] %s", level, message))
    end
end

-- 验证主AP配置参数
local function validate_cap_config(config)
    if not config or type(config) ~= "table" then
        return false, "Configuration must be a table"
    end

    -- 验证mesh SSID
    if not config.mesh_ssid or type(config.mesh_ssid) ~= "string" or
       #config.mesh_ssid < 1 or #config.mesh_ssid > 32 then
        return false, "Mesh SSID must be 1-32 characters"
    end

    -- 验证mesh密码
    if not config.mesh_password or type(config.mesh_password) ~= "string" or
       #config.mesh_password < 8 or #config.mesh_password > 63 then
        return false, "Mesh password must be 8-63 characters"
    end

    -- 验证网关模式配置
    if config.gateway_mode then
        if config.wan_type then
            local valid_wan_types = {dhcp = true, static = true, pppoe = true}
            if not valid_wan_types[config.wan_type] then
                return false, "Invalid WAN type"
            end

            -- 验证静态IP配置
            if config.wan_type == "static" then
                if not config.wan_config or not config.wan_config.ipaddr or
                   not config.wan_config.netmask or not config.wan_config.gateway then
                    return false, "Static IP configuration incomplete"
                end
            end

            -- 验证PPPoE配置
            if config.wan_type == "pppoe" then
                if not config.wan_config or not config.wan_config.username or
                   not config.wan_config.password then
                    return false, "PPPoE configuration incomplete"
                end
            end
        end
    end

    return true
end

-- 配置主AP模式
function mesh_cap_api.configure_cap_mode(config)
    log_message("INFO", "Configuring CAP mode with config: " .. json.encode(config))

    -- 验证配置
    local valid, err_msg = validate_cap_config(config)
    if not valid then
        log_message("ERROR", "Invalid CAP configuration: " .. err_msg)
        return false, err_msg
    end

    -- 设置mesh配置
    local mesh_cfg = {
        mesh_enabled = true,
        meshssid = config.mesh_ssid,
        meshpass = config.mesh_password,
        cap_mode = true,
        workmode = "mesh_gw",
        gateway_mode = config.gateway_mode or true,
        wan_type = config.wan_type or "dhcp"
    }

    local success, msg = mesh_config.set_config(mesh_cfg)
    if not success then
        log_message("ERROR", "Failed to set mesh config: " .. (msg or "unknown error"))
        return false, msg
    end

    -- 准备网关配置
    local gateway_config = nil
    if config.gateway_mode and config.wan_config then
        gateway_config = {
            wan_type = config.wan_type,
            ipaddr = config.wan_config.ipaddr,
            netmask = config.wan_config.netmask,
            gateway = config.wan_config.gateway,
            dns = config.wan_config.dns,
            username = config.wan_config.username,
            password = config.wan_config.password
        }
    end

    -- 启用mesh模式
    local result = mesh_service.enable_mesh_mode(
        config.mesh_ssid,
        config.mesh_password,
        true, -- is_cap_mode
        gateway_config
    )

    if result then
        log_message("INFO", "CAP mode configured successfully")
        return true, "CAP mode configured successfully"
    else
        log_message("ERROR", "Failed to enable mesh mode")
        return false, "Failed to enable mesh mode"
    end
end

-- 获取主AP配置
function mesh_cap_api.get_cap_config()
    local config = mesh_config.get_config()
    local cursor = uci.cursor()

    -- 获取WAN配置
    local wan_config = {}
    local wan_proto = cursor:get("network", "wan", "proto") or "dhcp"
    wan_config.wan_type = wan_proto

    if wan_proto == "static" then
        wan_config.ipaddr = cursor:get("network", "wan", "ipaddr") or ""
        wan_config.netmask = cursor:get("network", "wan", "netmask") or ""
        wan_config.gateway = cursor:get("network", "wan", "gateway") or ""
        wan_config.dns = cursor:get("network", "wan", "dns") or ""
    elseif wan_proto == "pppoe" then
        wan_config.username = cursor:get("network", "wan", "username") or ""
        wan_config.password = cursor:get("network", "wan", "password") or ""
    end

    local cap_config = {
        mesh_enabled = config.mesh_enabled or false,
        mesh_ssid = config.meshssid or "",
        mesh_password = config.meshpass or "",
        is_cap_mode = config.cap_mode or false,
        gateway_mode = config.gateway_mode or false,
        wan_config = wan_config,
        radio_status = {
            radio0_enabled = config.radio0_enabled or false,
            radio1_enabled = config.radio1_enabled or false
        }
    }

    return cap_config
end

-- 获取主AP状态
function mesh_cap_api.get_cap_status()
    local mesh_status = mesh_config.get_status()
    local service_status = mesh_service.get_service_status()

    local status = {
        mesh_enabled = mesh_status.enabled,
        is_cap_mode = mesh_status.is_cap,
        mesh_ssid = mesh_status.ssid,
        services = service_status,
        radio_status = mesh_status.radio_status
    }

    return status
end

-- 重置主AP配置
function mesh_cap_api.reset_cap_config()
    log_message("INFO", "Resetting CAP configuration")

    -- 禁用mesh模式
    local result = mesh_service.disable_mesh_mode()
    if result then
        -- 重置mesh配置
        mesh_config.disable_mesh()
        log_message("INFO", "CAP configuration reset successfully")
        return true, "CAP configuration reset successfully"
    else
        log_message("ERROR", "Failed to reset CAP configuration")
        return false, "Failed to reset CAP configuration"
    end
end

-- 测试网络连接
function mesh_cap_api.test_network_connection(target_host)
    target_host = target_host or "*******"

    local cmd = string.format("ping -c 3 -W 5 %s >/dev/null 2>&1", target_host)
    local result = os.execute(cmd)

    return result == 0
end

-- 获取网络接口状态
function mesh_cap_api.get_network_interfaces()
    local interfaces = {}

    -- 获取WAN接口状态
    local wan_status_cmd = "ifconfig wan 2>/dev/null | grep 'inet addr' | awk '{print $2}' | cut -d: -f2"
    local wan_ip_handle = io.popen(wan_status_cmd)
    local wan_ip = wan_ip_handle:read("*line")
    wan_ip_handle:close()

    interfaces.wan = {
        ip = wan_ip or "",
        connected = wan_ip and #wan_ip > 0
    }

    -- 获取LAN接口状态
    local lan_status_cmd = "ifconfig br-lan 2>/dev/null | grep 'inet addr' | awk '{print $2}' | cut -d: -f2"
    local lan_ip_handle = io.popen(lan_status_cmd)
    local lan_ip = lan_ip_handle:read("*line")
    lan_ip_handle:close()

    interfaces.lan = {
        ip = lan_ip or "***********"
    }

    return interfaces
end

-- CGI接口处理
function mesh_cap_api.handle_cgi()
    if not is_cgi() then
        return
    end

    -- 设置HTTP头
    print("Content-Type: application/json")
    print("Cache-Control: no-cache")
    print("")

    local method = os.getenv("REQUEST_METHOD")
    local query_string = os.getenv("QUERY_STRING") or ""
    local response = {success = false, message = "Unknown error"}

    if method == "GET" then
        if string.find(query_string, "action=config") then
            -- 获取主AP配置
            local config = mesh_cap_api.get_cap_config()
            response = {success = true, data = config}

        elseif string.find(query_string, "action=status") then
            -- 获取主AP状态
            local status = mesh_cap_api.get_cap_status()
            response = {success = true, data = status}

        elseif string.find(query_string, "action=interfaces") then
            -- 获取网络接口状态
            local interfaces = mesh_cap_api.get_network_interfaces()
            response = {success = true, data = interfaces}

        elseif string.find(query_string, "action=test_network") then
            -- 测试网络连接
            local target = string.match(query_string, "target=([^&]+)")
            local connected = mesh_cap_api.test_network_connection(target)
            response = {success = true, data = {connected = connected}}

        else
            response = {success = false, message = "Invalid action"}
        end

    elseif method == "POST" then
        -- 处理主AP配置
        local content_length = tonumber(os.getenv("CONTENT_LENGTH")) or 0
        if content_length > 0 then
            local post_data = io.read(content_length)
            local success, data = pcall(json.decode, post_data)

            if success and data then
                if data.action == "configure" then
                    local result, msg = mesh_cap_api.configure_cap_mode(data.config)
                    response = {success = result, message = msg}

                elseif data.action == "reset" then
                    local result, msg = mesh_cap_api.reset_cap_config()
                    response = {success = result, message = msg}

                else
                    response = {success = false, message = "Invalid action"}
                end
            else
                response = {success = false, message = "Invalid JSON data"}
            end
        else
            response = {success = false, message = "No data provided"}
        end
    end

    print(json.encode(response))
end

-- 如果作为CGI运行，处理请求
if is_cgi() then
    mesh_cap_api.handle_cgi()
end

return mesh_cap_api