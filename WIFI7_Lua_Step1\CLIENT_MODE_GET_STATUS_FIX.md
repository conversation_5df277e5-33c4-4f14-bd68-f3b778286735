# Client Mode GET Status API 修复总结

## 问题描述

修改后的client_mode.lua在执行GET请求 `?action=get_status` 时返回 "Bad Gateway" 错误，并且没有产生任何日志输出。

## 问题分析

### 1. 根本原因
原始的client_mode.lua只支持POST请求，不支持GET请求。当使用GET请求访问时：
- 脚本检查`CONTENT_LENGTH`环境变量，GET请求没有这个变量
- 脚本认为没有POST数据就直接返回错误并退出
- 导致"Bad Gateway"错误，因为CGI脚本没有正常完成

### 2. 具体问题点
```lua
-- 原来的代码只处理POST请求
local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
if POSTLength == 0 then
    -- 直接返回错误，不处理GET请求
    return
end
```

### 3. 函数作用域问题
在修改过程中发现`get_sta_status_api`函数被定义为`local function`，但在其他地方被调用，这也会导致运行时错误。

## 修复方案

### 1. 添加GET请求支持
```lua
-- 检查请求方法
local request_method = os.getenv("REQUEST_METHOD") or "GET"

if request_method == "POST" then
    -- 处理POST请求的原有逻辑
elseif request_method == "GET" then
    -- 新增GET请求处理逻辑
    local query_string = os.getenv("QUERY_STRING") or ""
    local action = query_string:match("action=([^&]*)")
    
    if action == "get_status" then
        requestData = {
            module = "client_mode",
            api = "get_sta_status",
            sid = "get_status_request",
            param = { band = "2.4G" }
        }
    end
end
```

### 2. 修复函数作用域
```lua
-- 修复前
local function get_sta_status_api(data)

-- 修复后  
function get_sta_status_api(data)
```

## 支持的GET请求格式

### 1. 获取配置
```
GET /cgi-bin/client_mode.lua?action=get
```

### 2. 获取STA状态
```
GET /cgi-bin/client_mode.lua?action=get_status
```

## 响应格式

### 1. GET Status 成功响应
```json
{
    "module": "client_mode",
    "version": "1.0",
    "api": "get_sta_status",
    "errcode": 0,
    "sid": "get_status_request",
    "result": {
        "band": "2.4G",
        "ifname": "ath5",
        "status": {
            "connected": true,
            "ssid": "UpstreamAP_SSID",
            "signal": "-45",
            "ap": "aa:bb:cc:dd:ee:ff"
        }
    }
}
```

### 2. GET 配置成功响应
```json
{
    "module": "client_mode",
    "version": "1.0",
    "api": "get",
    "errcode": 0,
    "sid": "get_request",
    "result": {
        "roaming_settings": {},
        "lan_settings": {
            "ip_allocation": "static",
            "ip": "***********",
            "netmask": "*************"
        },
        "wan_settings": {
            "mode": "dhcp"
        },
        "connect_mode": "-"
    }
}
```

### 3. 错误响应
```json
{
    "module": "client_mode",
    "version": "1.0",
    "errcode": 3,
    "result": {
        "message": "Unknown GET action: invalid_action"
    }
}
```

## 测试方法

### 1. 使用curl测试
```bash
# 测试获取状态
curl "http://***********/cgi-bin/client_mode.lua?action=get_status"

# 测试获取配置
curl "http://***********/cgi-bin/client_mode.lua?action=get"
```

### 2. 使用测试脚本
```bash
# 运行提供的测试脚本
chmod +x test_client_mode.sh
./test_client_mode.sh
```

### 3. 检查日志
```bash
# 查看详细日志
tail -f /tmp/client_mode.log
```

## 兼容性说明

### 1. 向后兼容
- 原有的POST请求功能完全保留
- 所有现有的API调用方式不受影响

### 2. 新增功能
- 支持GET请求获取状态和配置
- 自动解析查询参数
- 提供默认参数（如默认频段为2.4G）

## 错误码说明

- **0**: 成功
- **1**: POST数据获取失败
- **2**: JSON解析失败
- **3**: 未知的GET action
- **4**: 不支持的请求方法
- **10**: 无效的频段参数

## 调试建议

### 1. 检查环境变量
```bash
echo "REQUEST_METHOD: $REQUEST_METHOD"
echo "QUERY_STRING: $QUERY_STRING"
echo "CONTENT_LENGTH: $CONTENT_LENGTH"
```

### 2. 检查日志输出
```bash
# 实时查看日志
tail -f /tmp/client_mode.log

# 查看最近的错误
grep -i error /tmp/client_mode.log
```

### 3. 验证脚本语法
```bash
# 检查Lua语法
lua -c client_mode.lua
```

## 部署建议

### 1. 备份原文件
```bash
cp client_mode.lua client_mode.lua.backup
```

### 2. 部署新文件
```bash
# 复制修复后的文件到设备
scp client_mode.lua root@***********:/www/cgi-bin/
```

### 3. 设置权限
```bash
chmod +x /www/cgi-bin/client_mode.lua
```

### 4. 测试验证
```bash
# 测试GET请求
curl "http://***********/cgi-bin/client_mode.lua?action=get_status"
```

## 总结

通过添加GET请求支持和修复函数作用域问题，client_mode.lua现在可以正确处理：
1. POST请求（原有功能）
2. GET请求获取状态
3. GET请求获取配置

修复后的脚本提供了更好的兼容性和易用性，同时保持了原有功能的完整性。
