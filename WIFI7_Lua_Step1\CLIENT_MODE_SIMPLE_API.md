# Client Mode API 简化请求格式指南

## 概述

client_mode.lua 现在支持简化的请求格式，大部分字段都是可选的，系统会自动提供默认值。

## 字段说明

### 必需字段
- `api`: API操作类型

### 可选字段 (系统自动提供默认值)
- `module`: 默认为 "client_mode"
- `version`: 默认为 "1.0"  
- `sid`: 默认为 "default_session"

## 简化请求格式示例

### 1. 扫描SSID (最简格式)

**2.4G频段扫描:**
```json
{
    "api": "scan_ssid",
    "param": {
        "band": "2.4G"
    }
}
```

**5G频段扫描:**
```json
{
    "api": "scan_ssid", 
    "param": {
        "band": "5G"
    }
}
```

**6G频段扫描:**
```json
{
    "api": "scan_ssid",
    "param": {
        "band": "6G"
    }
}
```

### 2. 设置客户端模式 (最简格式)

```json
{
    "api": "set",
    "param": {
        "mode": "client",
        "connection": {
            "band": "5G",
            "ssid": "UpstreamAP_SSID",
            "password": "wifi_password",
            "encryption": "psk2"
        },
        "wan": {
            "mode": "dhcp"
        }
    }
}
```

### 3. GET请求 (无需JSON)

**获取配置:**
```
GET /cgi-bin/client_mode.lua?action=get
```

**获取状态:**
```
GET /cgi-bin/client_mode.lua?action=get_status
```

## 测试命令

### 使用curl测试扫描

```bash
curl -X POST http://192.168.1.254/cgi-bin/3onedata/client_mode.lua \
  -H "Content-Type: application/json" \
  -d '{"api":"scan_ssid","param":{"band":"5G"}}'
```

### 使用echo测试 (在设备上)

```bash
echo '{"api":"scan_ssid","param":{"band":"5G"}}' | lua client_mode.lua
```

## 错误处理

如果缺少必需的`api`字段，会返回错误：

```json
{
    "module": "client_mode",
    "version": "1.0", 
    "errcode": 3,
    "result": {
        "message": "Missing required field: api"
    }
}
```

## 兼容性

- 新的简化格式与原有完整格式完全兼容
- 现有的完整格式请求仍然正常工作
- 推荐使用简化格式以减少请求数据量

## 网络助手测试格式

在网络助手中使用以下JSON格式进行测试：

```json
{
    "api": "scan_ssid",
    "param": {
        "band": "5G"
    }
}
```

这个格式比之前的完整格式更简洁，减少了不必要的字段。
