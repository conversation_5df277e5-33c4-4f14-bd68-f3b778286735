#!/usr/bin/env lua

-- 最小化的client_mode测试版本
-- 用于快速验证GET请求支持

-- 引入模块
local cjson = require("cjson.safe")
local log_file = "/tmp/client_mode_minimal.log"

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("%Y-%m-%d %H:%M:%S") .. " " .. message .. "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end

-- 设置 HTTP 响应头
io.write("Content-type: application/json\nPragma: no-cache\n\n")

write_log("=== Client Mode Minimal Test Started ===")

-- 检查请求方法
local request_method = os.getenv("REQUEST_METHOD") or "GET"
write_log("Request method: " .. request_method)

local requestData = {}

if request_method == "POST" then
    write_log("Processing POST request")
    -- 获取 POST 数据
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""
    if POSTLength > 0 then
        POST = io.read(POSTLength)
        write_log("Received POST data: " .. POST)
    else
        write_log("No POST data received")
    end

    if not POST or POST == "" then
        local error_message = "Failed to retrieve POST data"
        write_log(error_message)
        io.write(cjson.encode({
            module = "client_mode_minimal",
            version = "1.0",
            errcode = 1,
            result = { message = error_message }
        }))
        io.flush()
        return
    end

    -- 解析 POST 数据为 JSON
    requestData = cjson.decode(POST)
    if not requestData then
        local error_message = "Invalid JSON input"
        write_log(error_message)
        io.write(cjson.encode({
            module = "client_mode_minimal",
            version = "1.0",
            errcode = 2,
            result = { message = error_message }
        }))
        io.flush()
        return
    end
elseif request_method == "GET" then
    write_log("Processing GET request")
    -- 处理 GET 请求
    local query_string = os.getenv("QUERY_STRING") or ""
    write_log("Query string: " .. query_string)
    
    -- 解析查询参数
    local action = query_string:match("action=([^&]*)")
    write_log("Parsed action: " .. (action or "none"))
    
    if action == "get" then
        requestData = {
            module = "client_mode_minimal",
            api = "get",
            sid = "get_request"
        }
    elseif action == "get_status" then
        requestData = {
            module = "client_mode_minimal",
            api = "get_sta_status",
            sid = "get_status_request",
            param = { band = "2.4G" }
        }
    else
        local error_message = "Unknown GET action: " .. (action or "none")
        write_log(error_message)
        io.write(cjson.encode({
            module = "client_mode_minimal",
            version = "1.0",
            errcode = 3,
            result = { message = error_message }
        }))
        io.flush()
        return
    end
else
    local error_message = "Unsupported request method: " .. request_method
    write_log(error_message)
    io.write(cjson.encode({
        module = "client_mode_minimal",
        version = "1.0",
        errcode = 4,
        result = { message = error_message }
    }))
    io.flush()
    return
end

write_log("Request data: " .. cjson.encode(requestData))

-- 处理API请求
if requestData.api == "get" then
    write_log("Processing get API")
    io.write(cjson.encode({
        module = "client_mode_minimal",
        version = "1.0",
        api = "get",
        errcode = 0,
        sid = requestData.sid,
        result = {
            message = "GET request successful",
            test_data = {
                lan_settings = { ip = "***********", netmask = "*************" },
                wan_settings = { mode = "dhcp" }
            }
        }
    }))
elseif requestData.api == "get_sta_status" then
    write_log("Processing get_sta_status API")
    io.write(cjson.encode({
        module = "client_mode_minimal",
        version = "1.0",
        api = "get_sta_status",
        errcode = 0,
        sid = requestData.sid,
        result = {
            band = "2.4G",
            ifname = "ath5",
            status = {
                connected = true,
                ssid = "TestAP",
                signal = "-45",
                ap = "aa:bb:cc:dd:ee:ff"
            }
        }
    }))
else
    write_log("Unknown API: " .. (requestData.api or "none"))
    io.write(cjson.encode({
        module = "client_mode_minimal",
        version = "1.0",
        errcode = 5,
        result = { message = "Unknown API: " .. (requestData.api or "none") }
    }))
end

io.flush()
write_log("=== Client Mode Minimal Test Completed ===")
