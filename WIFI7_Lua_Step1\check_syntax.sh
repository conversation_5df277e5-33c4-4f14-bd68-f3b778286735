#!/bin/bash

echo "=== Lua 语法检查 ==="

# 检查client_mode.lua语法
echo "检查 client_mode.lua 语法:"
lua -c client_mode.lua
if [ $? -eq 0 ]; then
    echo "✓ client_mode.lua 语法正确"
else
    echo "✗ client_mode.lua 语法错误"
    exit 1
fi

# 检查client_mode_minimal.lua语法（如果存在）
if [ -f "client_mode_minimal.lua" ]; then
    echo ""
    echo "检查 client_mode_minimal.lua 语法:"
    lua -c client_mode_minimal.lua
    if [ $? -eq 0 ]; then
        echo "✓ client_mode_minimal.lua 语法正确"
    else
        echo "✗ client_mode_minimal.lua 语法错误"
    fi
fi

echo ""
echo "=== 语法检查完成 ==="
