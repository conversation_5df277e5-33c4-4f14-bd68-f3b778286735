#!/usr/bin/lua

-- 修复CGI模块的脚本
-- 这个脚本会检查所有模块文件，确保CGI代码只在is_cgi()检查内执行

local modules_to_fix = {
    "bridge_mode.lua",
    "vlan.lua", 
    "user_setup.lua",
    "snmp.lua",
    "qos.lua",
    "scheduled_reboot.lua",
    "current_device.lua",
    "mac_access_control.lua"
}

print("Checking modules for CGI execution issues...")

for _, module in ipairs(modules_to_fix) do
    local file = io.open(module, "r")
    if file then
        local content = file:read("*all")
        file:close()
        
        -- 检查是否有Content-type输出在is_cgi检查之外
        if content:match('io%.write%("Content%-type:') and not content:match('if.*is_cgi%(%).*then.*io%.write%("Content%-type:') then
            print("ISSUE FOUND: " .. module .. " has CGI code outside is_cgi() check")
        else
            print("OK: " .. module .. " appears to be properly structured")
        end
    else
        print("WARNING: Could not open " .. module)
    end
end

print("Check complete. Please manually fix any issues found.")
