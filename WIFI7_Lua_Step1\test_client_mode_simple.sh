#!/bin/sh

echo "=== 测试 client_mode.lua 简化请求格式 ==="

# 测试1: 简化格式的扫描请求
echo "1. 测试简化格式扫描请求:"
echo '{"api":"scan_ssid","param":{"band":"5G"}}' | lua client_mode.lua
echo ""

# 测试2: 完整格式的扫描请求  
echo "2. 测试完整格式扫描请求:"
echo '{"module":"client_mode","api":"scan_ssid","sid":"test_session","version":"1.0","param":{"band":"5G"}}' | lua client_mode.lua
echo ""

# 测试3: 缺少api字段的请求
echo "3. 测试缺少api字段的请求:"
echo '{"param":{"band":"5G"}}' | lua client_mode.lua
echo ""

# 测试4: GET请求测试
echo "4. 测试GET请求:"
export REQUEST_METHOD=GET
export QUERY_STRING="action=get_status"
lua client_mode.lua
echo ""

echo "=== 测试完成 ==="
