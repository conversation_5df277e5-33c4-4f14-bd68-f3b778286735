#!/usr/bin/lua

-- 测试ac_comm_wrap的状态

print("=== 测试 ac_comm_wrap 状态 ===")
print("")

-- 1. 加载模块
local status, ac_comm = pcall(require, "ac_comm_wrap")
if not status then
    print("❌ 无法加载 ac_comm_wrap: " .. tostring(ac_comm))
    return
end

print("✅ ac_comm_wrap 加载成功")

-- 2. 检查初始化状态
print("初始化状态: " .. tostring(ac_comm._inited))
print("AP端口: " .. tostring(ac_comm._ap_port))
print("本机IP: " .. tostring(ac_comm._my_ip))
print("")

-- 3. 如果没有初始化，尝试初始化
if not ac_comm._inited then
    print("尝试初始化 ac_comm_wrap...")
    local ok, err = ac_comm.init({local_port = 50001})
    print("初始化结果: " .. tostring(ok))
    if not ok then
        print("初始化错误: " .. tostring(err))
        return
    end
    print("初始化后状态: " .. tostring(ac_comm._inited))
    print("")
end

-- 4. 测试单次接收
print("4. 测试单次接收 (3秒超时):")
print("请在3秒内使用网络助手发送数据到端口50001...")
print("数据: 41 (字母A的十六进制)")
print("")

local data, ip, port = ac_comm.receive(3)
if data then
    print("✅ 收到数据:")
    print("  来源: " .. tostring(ip) .. ":" .. tostring(port))
    print("  长度: " .. #data .. " 字节")
    print("  内容: " .. data)
    print("  十六进制: " .. (data:gsub('.', function(c) return string.format('%02X ', string.byte(c)) end)))
else
    print("❌ 没有收到数据")
    print("  错误: " .. tostring(ip))
    
    if ip == "not_inited" then
        print("  原因: ac_comm_wrap 没有初始化")
    elseif ip == "timeout" then
        print("  原因: 接收超时")
    elseif ip == "filtered_self_broadcast" then
        print("  原因: 过滤了自己的广播包")
    end
end
print("")

-- 5. 检查C库状态
print("5. 检查底层C库状态:")
local status, ac_comm_so = pcall(require, "ac_comm")
if status then
    print("✅ C库 ac_comm.so 可用")
    
    -- 直接测试C库接收
    print("直接测试C库接收 (2秒超时):")
    print("请在2秒内使用网络助手发送数据到端口50001...")
    
    local data, ip, port = ac_comm_so.recv(2)
    if data then
        print("✅ C库直接收到数据:")
        print("  来源: " .. tostring(ip) .. ":" .. tostring(port))
        print("  长度: " .. #data .. " 字节")
        print("  内容: " .. data)
    else
        print("❌ C库没有收到数据")
        print("  错误: " .. tostring(ip))
    end
else
    print("❌ C库 ac_comm.so 不可用: " .. tostring(ac_comm_so))
end
print("")

print("=== 测试完成 ===")
print("")
print("如果C库能收到数据但ac_comm_wrap收不到，说明包装层有问题")
print("如果都收不到数据，说明网络连接有问题")
print("")
print("网络助手设置:")
print("- 协议: UDP")
print("- 目标IP: 设备IP")
print("- 目标端口: 50001")
print("- 数据格式: HEX")
print("- 发送数据: 41")
