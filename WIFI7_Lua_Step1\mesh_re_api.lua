#!/usr/bin/lua

--[[
WiFi7 Mesh RE (Range Extender) API
基于WiFi6 son_topo实现，为WiFi7设备提供从AP配置API
参考: package/son_topo/files/re.sh
作者: WiFi7开发团队
日期: 2025-07-07
]]

local uci = require("uci")
local json = require("json")

-- 引入mesh相关模块
local mesh_config = require("mesh_config")
local mesh_service = require("mesh_service")

local mesh_re_api = {}

-- 检测是否为CGI环境
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- 日志函数
local function log_message(level, message)
    if is_cgi() then
        os.execute(string.format("logger -t mesh_re_api '[%s] %s'", level, message))
    else
        print(string.format("[%s] %s", level, message))
    end
end

-- 扫描可用的mesh网络
function mesh_re_api.scan_mesh_networks()
    log_message("INFO", "Scanning for mesh networks")

    local networks = {}

    -- 执行WiFi扫描
    local scan_cmd = "iwlist scan 2>/dev/null | grep -E 'ESSID|Quality|Encryption'"
    local scan_handle = io.popen(scan_cmd)
    local scan_result = scan_handle:read("*a")
    scan_handle:close()

    if scan_result and #scan_result > 0 then
        -- 解析扫描结果
        local current_network = {}
        for line in scan_result:gmatch("[^\r\n]+") do
            if string.find(line, "ESSID:") then
                local ssid = string.match(line, 'ESSID:"([^"]*)"')
                if ssid and #ssid > 0 then
                    current_network.ssid = ssid
                end
            elseif string.find(line, "Quality") then
                local quality = string.match(line, "Quality=(%d+/%d+)")
                if quality then
                    current_network.quality = quality
                end
            elseif string.find(line, "Encryption") then
                local encryption = string.match(line, "Encryption key:(%w+)")
                current_network.encrypted = (encryption == "on")

                -- 如果有完整的网络信息，添加到列表
                if current_network.ssid then
                    table.insert(networks, current_network)
                end
                current_network = {}
            end
        end
    end

    return networks
end

-- 配置从AP模式
function mesh_re_api.configure_re_mode(config)
    log_message("INFO", "Configuring RE mode with config: " .. json.encode(config))

    -- 验证配置
    if not config or type(config) ~= "table" then
        return false, "Configuration must be a table"
    end

    if config.mesh_ssid and (not config.mesh_ssid or type(config.mesh_ssid) ~= "string" or
       #config.mesh_ssid < 1 or #config.mesh_ssid > 32) then
        return false, "Mesh SSID must be 1-32 characters"
    end

    if config.mesh_password and (not config.mesh_password or type(config.mesh_password) ~= "string" or
       #config.mesh_password < 8 or #config.mesh_password > 63) then
        return false, "Mesh password must be 8-63 characters"
    end

    -- 设置mesh配置
    local mesh_cfg = {
        mesh_enabled = true,
        meshssid = config.mesh_ssid or "",
        meshpass = config.mesh_password or "",
        cap_mode = false,
        workmode = "mesh",
        gateway_mode = false
    }

    local success, msg = mesh_config.set_config(mesh_cfg)
    if not success then
        log_message("ERROR", "Failed to set mesh config: " .. (msg or "unknown error"))
        return false, msg
    end

    -- 启用mesh模式
    local result = mesh_service.enable_mesh_mode(
        config.mesh_ssid or "",
        config.mesh_password or "",
        false -- is_cap_mode
    )

    if result then
        log_message("INFO", "RE mode configured successfully")
        return true, "RE mode configured successfully"
    else
        log_message("ERROR", "Failed to enable mesh mode")
        return false, "Failed to enable mesh mode"
    end
end

-- 自动配置从AP模式（通过扫描连接到主AP）
function mesh_re_api.auto_configure_re_mode()
    log_message("INFO", "Auto-configuring RE mode")

    -- 扫描mesh网络
    local networks = mesh_re_api.scan_mesh_networks()

    if #networks == 0 then
        return false, "No mesh networks found"
    end

    -- 选择信号最强的网络
    local best_network = networks[1]
    for _, network in ipairs(networks) do
        if network.quality and best_network.quality then
            local current_quality = tonumber(string.match(network.quality, "(%d+)"))
            local best_quality = tonumber(string.match(best_network.quality, "(%d+)"))
            if current_quality and best_quality and current_quality > best_quality then
                best_network = network
            end
        end
    end

    -- 配置连接到最佳网络
    local config = {
        mesh_ssid = best_network.ssid
        -- 注意：密码需要用户提供或从其他方式获取
    }

    return mesh_re_api.configure_re_mode(config)
end

-- 获取从AP配置
function mesh_re_api.get_re_config()
    local config = mesh_config.get_config()

    local re_config = {
        mesh_enabled = config.mesh_enabled or false,
        mesh_ssid = config.meshssid or "",
        mesh_password = config.meshpass or "",
        is_re_mode = not (config.cap_mode or false),
        radio_status = {
            radio0_enabled = config.radio0_enabled or false,
            radio1_enabled = config.radio1_enabled or false
        }
    }

    return re_config
end

-- 获取从AP状态
function mesh_re_api.get_re_status()
    local mesh_status = mesh_config.get_status()
    local service_status = mesh_service.get_service_status()

    -- 获取连接状态
    local connected_to_cap = false
    local cap_info = {}

    -- 检查是否连接到主AP
    local iwconfig_cmd = "iwconfig 2>/dev/null | grep -E 'Access Point|ESSID'"
    local iwconfig_handle = io.popen(iwconfig_cmd)
    local iwconfig_result = iwconfig_handle:read("*a")
    iwconfig_handle:close()

    if iwconfig_result then
        local essid = string.match(iwconfig_result, 'ESSID:"([^"]*)"')
        local ap_mac = string.match(iwconfig_result, "Access Point: ([%w:]+)")

        if essid and ap_mac and ap_mac ~= "Not-Associated" then
            connected_to_cap = true
            cap_info = {
                ssid = essid,
                mac = ap_mac
            }
        end
    end

    local status = {
        mesh_enabled = mesh_status.enabled,
        is_re_mode = not mesh_status.is_cap,
        mesh_ssid = mesh_status.ssid,
        connected_to_cap = connected_to_cap,
        cap_info = cap_info,
        services = service_status,
        radio_status = mesh_status.radio_status
    }

    return status
end

-- 重置从AP配置
function mesh_re_api.reset_re_config()
    log_message("INFO", "Resetting RE configuration")

    -- 禁用mesh模式
    local result = mesh_service.disable_mesh_mode()
    if result then
        -- 重置mesh配置
        mesh_config.disable_mesh()
        log_message("INFO", "RE configuration reset successfully")
        return true, "RE configuration reset successfully"
    else
        log_message("ERROR", "Failed to reset RE configuration")
        return false, "Failed to reset RE configuration"
    end
end

-- 获取信号强度信息
function mesh_re_api.get_signal_info()
    local signal_info = {}

    -- 获取无线接口信号强度
    local interfaces = {"wlan0", "wlan8"}

    for _, iface in ipairs(interfaces) do
        local cmd = string.format("iwconfig %s 2>/dev/null | grep -E 'Signal level|Link Quality'", iface)
        local handle = io.popen(cmd)
        local result = handle:read("*a")
        handle:close()

        if result then
            local quality = string.match(result, "Link Quality=(%d+/%d+)")
            local signal = string.match(result, "Signal level=([-%d]+)")

            signal_info[iface] = {
                quality = quality or "0/0",
                signal_level = signal and tonumber(signal) or -100
            }
        else
            signal_info[iface] = {
                quality = "0/0",
                signal_level = -100
            }
        end
    end

    return signal_info
end

-- 获取mesh拓扑信息
function mesh_re_api.get_mesh_topology()
    local topology = {
        nodes = {},
        connections = {}
    }

    -- 读取mesh拓扑文件
    local topo_file = "/tmp/mac_mesh"
    local file = io.open(topo_file, "r")

    if file then
        local content = file:read("*a")
        file:close()

        if content and #content > 0 then
            -- 解析拓扑信息
            for line in content:gmatch("[^\r\n]+") do
                local mac, role, signal = string.match(line, "([%w:]+)%s+(%w+)%s+([-%d]+)")
                if mac and role then
                    table.insert(topology.nodes, {
                        mac = mac,
                        role = role,
                        signal = tonumber(signal) or -100
                    })
                end
            end
        end
    end

    return topology
end

-- 执行mesh网络优化
function mesh_re_api.optimize_mesh_connection()
    log_message("INFO", "Optimizing mesh connection")

    -- 获取当前信号强度
    local signal_info = mesh_re_api.get_signal_info()

    -- 检查是否需要优化
    local need_optimization = false
    for iface, info in pairs(signal_info) do
        if info.signal_level < -70 then
            need_optimization = true
            break
        end
    end

    if not need_optimization then
        return true, "Connection already optimal"
    end

    -- 重新扫描并选择最佳连接
    local networks = mesh_re_api.scan_mesh_networks()
    if #networks > 0 then
        -- 重启mesh服务以重新连接
        mesh_service.restart_mesh_services()
        return true, "Mesh connection optimized"
    else
        return false, "No better mesh networks found"
    end
end

-- CGI接口处理
function mesh_re_api.handle_cgi()
    if not is_cgi() then
        return
    end

    -- 设置HTTP头
    print("Content-Type: application/json")
    print("Cache-Control: no-cache")
    print("")

    local method = os.getenv("REQUEST_METHOD")
    local query_string = os.getenv("QUERY_STRING") or ""
    local response = {success = false, message = "Unknown error"}

    if method == "GET" then
        if string.find(query_string, "action=config") then
            -- 获取从AP配置
            local config = mesh_re_api.get_re_config()
            response = {success = true, data = config}

        elseif string.find(query_string, "action=status") then
            -- 获取从AP状态
            local status = mesh_re_api.get_re_status()
            response = {success = true, data = status}

        elseif string.find(query_string, "action=scan") then
            -- 扫描mesh网络
            local networks = mesh_re_api.scan_mesh_networks()
            response = {success = true, data = networks}

        elseif string.find(query_string, "action=signal") then
            -- 获取信号强度
            local signal_info = mesh_re_api.get_signal_info()
            response = {success = true, data = signal_info}

        elseif string.find(query_string, "action=topology") then
            -- 获取mesh拓扑
            local topology = mesh_re_api.get_mesh_topology()
            response = {success = true, data = topology}

        else
            response = {success = false, message = "Invalid action"}
        end

    elseif method == "POST" then
        -- 处理从AP配置
        local content_length = tonumber(os.getenv("CONTENT_LENGTH")) or 0
        if content_length > 0 then
            local post_data = io.read(content_length)
            local success, data = pcall(json.decode, post_data)

            if success and data then
                if data.action == "configure" then
                    local result, msg = mesh_re_api.configure_re_mode(data.config)
                    response = {success = result, message = msg}

                elseif data.action == "auto_configure" then
                    local result, msg = mesh_re_api.auto_configure_re_mode()
                    response = {success = result, message = msg}

                elseif data.action == "reset" then
                    local result, msg = mesh_re_api.reset_re_config()
                    response = {success = result, message = msg}

                elseif data.action == "optimize" then
                    local result, msg = mesh_re_api.optimize_mesh_connection()
                    response = {success = result, message = msg}

                else
                    response = {success = false, message = "Invalid action"}
                end
            else
                response = {success = false, message = "Invalid JSON data"}
            end
        else
            response = {success = false, message = "No data provided"}
        end
    end

    print(json.encode(response))
end

-- 如果作为CGI运行，处理请求
if is_cgi() then
    mesh_re_api.handle_cgi()
end

return mesh_re_api