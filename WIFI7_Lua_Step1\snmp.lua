#!/usr/bin/lua

-- Copyright (c) 2024 The Linux Foundation. All rights reserved.
-- Not a Contribution.

-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

-- Lua环境：此脚本在OpenWRT的Lua环境中运行，确保有必要的Lua库，如luci.sys, luci.jsonc和luci.uci

-- UCI：这个脚本使用了uci库来直接操作配置文件。这比直接读写文件更为安全和标准。

-- SNMP 配置项：
-- 开关：启用/禁用 SNMP 服务（snmp_enable）。
-- Trap：启用/禁用 Trap 消息（trap_enable），依赖 SNMP 服务开启。
-- Trap IP：发送 Trap 消息的目标 IP 地址（trap_ip）。
-- 重传次数：Trap消息重传次数（trap_retries），范围0-100。
-- 时间间隔：Trap消息发送间隔（trap_interval），范围 1-2100 秒。

-- UCI配置：
-- 使用/etc/config/snmpd存储配置，创建一个名为settings的snmp段。
-- 配置项：
-- option snmp_enable '0'或'1'
-- 选项 trap_enable '0'或'1'
-- 选项 trap_ip '*************'
-- 选项 trap_retries '2'
-- 选项 trap_interval '30'
-- 服务控制：
-- SNMP服务通过/etc/init.d/snmpd控制（假设设备已安装snmpd包）。
-- snmp_enable="1"时启用并重启服务，否则停止并禁用。
-- 参数验证：
-- snmp_enable和trap_enable只接受"0"或"1"。
-- trap_ip必须是合法IP或空字符串。
-- trap_retries范围 0-100，trap_interval范围 1-2100。
-- Trap 启用时要求 SNMP 服务已启用。

-- 说明
-- 使用/etc/config/snmpd存储配置（OpenWrt标准SNMP配置）。
-- 通过uci管理配置，控制snmpd服务和Trap设置。

-- 检查配置
-- cat /etc/config/snmpinform
-- cat /etc/snmp/snmpd.conf.trap

-- 测试陷阱 是否发送（需触发事件，例如网络状态变化）
-- snmptrap -v 2c -c public ************* "" .*******.*******

-- 当前snmpd不直接支持retrytime和report_interval的配置（这些是snmpinform的自定义字段）。
-- 添加一个后台脚本（如/usr/bin/snmp-trap.sh）读取snmpinform并定期发送Trap：
--#!/bin/sh
--while true; do
--    enable=$(uci get snmpinform.settings.enable)
--    server=$(uci get snmpinform.settings.server)
--    retries=$(uci get snmpinform.settings.retrytime)
--    interval=$(uci get snmpinform.settings.report_interval)
--    if [ "$enable" = "1" ] && [ -n "$server" ]; then
--        i=0
--        while [ $i -le $retries ]; do
--            snmptrap -v 2c -c public "$server" "" .*******.*******
--            sleep "$interval"
--            i=$((i + 1))
--        done
--    fi
--    sleep 60  # 每分钟检查一次
--done

--启动脚本
--chmod +x /usr/bin/snmp-trap.sh
--/etc/init.d/snmp-trap.sh enable

-- 引入模块
local cjson = require("cjson.safe")
local sys = require("luci.sys")
local uci = require("luci.model.uci").cursor()
local log_file = "/tmp/snmp.log" -- 日志文件路径

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end



-- 验证参数函数
local function is_valid_ip(ip)
    return ip and ip:match("^%d+%.%d+%.%d+%.%d+$") ~= nil
end

local function is_valid_retries(retries)
    local r = tonumber(retries)
    return r and r >= 0 and r <= 100
end

local function is_valid_interval(interval)
    local i = tonumber(interval)
    return i and i >= 1 and i <= 2100
end

-- 更新 snmpd 的 Trap 配置
local function update_trap_config()
    local trap_enable = uci:get("snmpinform", "settings", "enable") or "0"
    local trap_ip = uci:get("snmpinform", "settings", "server") or ""
    -- 使用独立的 include 文件
    local trap_conf_file = "/etc/snmp/trap.conf"

    if trap_enable == "1" and trap_ip ~= "" then
        local trap_line = "trapsink " .. trap_ip .. " public" -- 假设社区字符串是 public
        -- 覆盖写入 trap 配置文件
        sys.call("echo '" .. trap_line .. "' > " .. trap_conf_file)
        write_log("Updated SNMP trap sink config in " .. trap_conf_file .. ": " .. trap_line)
    else
        -- 如果禁用或IP为空，则清空或删除 trap 配置文件
        sys.call("rm -f " .. trap_conf_file)
        write_log("Removed SNMP trap sink config file: " .. trap_conf_file)
    end
    -- 注意：修改 include 文件后，snmpd 可能需要 reload 或 restart 才能生效
    -- 可以在 snmp.lua 控制 snmpd 重启的地方确保这一点
end


-- 路由到具体逻辑
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 获取 POST 数据
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""
    if POSTLength > 0 then
        POST = io.read(POSTLength)
        write_log("Received POST data: " .. (POST or "nil"))
    else
        write_log("No POST data received or CONTENT_LENGTH is 0")
    end

    if not POST or POST == "" then
        local error_message = "Failed to retrieve POST data"
        write_log(error_message)
        io.write(cjson.encode({
            module = "snmp",
            version = "1.0",
            errcode = 1,
            result = { message = error_message }
        }))
        return
    end

    -- 解析 POST 数据为 JSON
    local requestData = cjson.decode(POST)
    if not requestData then
        local error_message = "Invalid JSON input"
        write_log(error_message)
        io.write(cjson.encode({
            module = "snmp",
            version = "1.0",
            errcode = 2,
            result = { message = error_message }
        }))
        return
    end

    write_log("Parsed request data: " .. cjson.encode(requestData))

    -- 检查请求格式
    if not requestData.version or not requestData.sid or not requestData.module or not requestData.api then
        local error_message = "Invalid request format"
        write_log(error_message)
        io.write(cjson.encode({
            module = "snmp",
            version = "1.0",
            errcode = 3,
            result = { message = error_message }
        }))
        return
    end

    if requestData.api == "set" then
        write_log("Calling set_snmp_settings with data: " .. cjson.encode(requestData))
        set_snmp_settings(requestData)
    elseif requestData.api == "get" then
        write_log("Calling get_snmp_settings with data: " .. cjson.encode(requestData))
        get_snmp_settings(requestData)
    else
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "snmp",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
    end
end

-- 确保 snmpinform 配置文件和 section 存在
local function ensure_snmpinform_section()
    -- 如果文件不存在，先创建
    local config_path = "/etc/config/snmpinform"
    local f = io.open(config_path, "r")
    if not f then
        local fw = io.open(config_path, "w")
        if fw then
            fw:write("config settings\n")
            fw:close()
            write_log("Created /etc/config/snmpinform file with section settings")
        else
            write_log("Failed to create /etc/config/snmpinform file")
        end
    else
        f:close()
    end
    -- 再用 uci 检查 section
    local section_found = false
    uci:foreach("snmpinform", "settings", function(s)
        section_found = true
    end)
    if not section_found then
        uci:add("snmpinform", "settings")
        uci:commit("snmpinform")
        write_log("Created section settings in /etc/config/snmpinform")
    end
end

-- 设置 SNMP 配置
-- 设置 SNMP 配置 (重点修改)
function set_snmp_settings(data)
    ensure_snmpinform_section()
    local param = data.param or {}

    -- 获取参数, 提供默认值
    local snmp_enable = param.snmp_enable or uci:get("snmpinform", "@settings[0]", "snmpdenable") or "0"
    local trap_enable = param.trap_enable or uci:get("snmpinform", "@settings[0]", "enable") or "0"
    local trap_ip = param.trap_ip or uci:get("snmpinform", "@settings[0]", "server") or ""
    local trap_retries = param.trap_retries or uci:get("snmpinform", "@settings[0]", "retrytime") or "0"
    local trap_interval = param.trap_interval or uci:get("snmpinform", "@settings[0]", "report_interval") or "10"

    -- 参数验证 (保持不变)
    -- ... (代码同前) ...

    -- 保存配置到 UCI
    local ok, err = pcall(function()
        -- 写入 /etc/config/snmpinform
        uci:set("snmpinform", "@settings[0]", "snmpdenable", snmp_enable)
        uci:set("snmpinform", "@settings[0]", "enable", trap_enable)
        uci:set("snmpinform", "@settings[0]", "server", trap_ip)
        uci:set("snmpinform", "@settings[0]", "retrytime", trap_retries)
        uci:set("snmpinform", "@settings[0]", "report_interval", trap_interval)
        uci:commit("snmpinform")
        write_log("Saved settings to /etc/config/snmpinform")

        -- 同时写入 /etc/config/snmpd 的启用状态 (用于 init 脚本)
        uci:set("snmpd", "general", "enabled", snmp_enable)
        uci:commit("snmpd")
        write_log("Saved enable status to /etc/config/snmpd general.enabled")
    end)
    if not ok then
        write_log("Failed to save SNMP config: " .. tostring(err))
        io.write(cjson.encode({ module = "snmp", version = "1.0", api = "set", errcode = 11, sid = data.sid, result = { message = "Failed to save SNMP config: " .. tostring(err) } }))
        return
    end
    write_log("SNMP settings saved: snmpdenable=" .. snmp_enable .. ", enable=" .. trap_enable .. ", server=" .. trap_ip .. ", retrytime=" .. trap_retries .. ", report_interval=" .. trap_interval)

    -- 控制服务
    local snmpd_exists = sys.call("ls /etc/init.d/snmpd >/dev/null 2>&1") == 0
    local snmp_trapd_exists = sys.call("ls /etc/init.d/snmp-trapd >/dev/null 2>&1") == 0

    if snmpd_exists then
        if snmp_enable == "1" then
            -- 启用并重启 snmpd (init 脚本会处理 trap sink)
            sys.call("/etc/init.d/snmpd enable && /etc/init.d/snmpd restart >/dev/null 2>&1 &")
            write_log("SNMP service (snmpd) enabled and restarted")

            -- 控制 snmp-trapd 服务
            if snmp_trapd_exists then
                if trap_enable == "1" then
                    sys.call("/etc/init.d/snmp-trapd enable && /etc/init.d/snmp-trapd start >/dev/null 2>&1 &")
                    write_log("SNMP Trap sender service (snmp-trapd) enabled and started")
                else -- trap_enable == "0"
                    sys.call("/etc/init.d/snmp-trapd stop && /etc/init.d/snmp-trapd disable >/dev/null 2>&1 &")
                    write_log("SNMP Trap sender service (snmp-trapd) stopped and disabled")
                end
            end

        else -- snmp_enable == "0"
            -- 停止并禁用 snmpd
            sys.call("/etc/init.d/snmpd stop && /etc/init.d/snmpd disable >/dev/null 2>&1 &")
            write_log("SNMP service (snmpd) stopped and disabled")

            -- 同时停止并禁用 snmp-trapd 服务
            if snmp_trapd_exists then
                sys.call("/etc/init.d/snmp-trapd stop && /etc/init.d/snmp-trapd disable >/dev/null 2>&1 &")
                write_log("SNMP Trap sender service (snmp-trapd) stopped and disabled as SNMP service is off")
            end
        end
    else
        write_log("SNMP service (snmpd) not installed.")
    end

    -- 返回成功响应 (保持不变)
    write_log("SNMP settings updated successfully")
    io.write(cjson.encode({ module = "snmp", version = "1.0", api = "set", errcode = 0, sid = data.sid, result = { message = "SNMP settings updated successfully" } }))
    io.flush()
end

-- 获取 SNMP 配置
function get_snmp_settings(data)
    ensure_snmpinform_section()
    local snmp_enable = uci:get("snmpinform", "@settings[0]", "snmpdenable") or "0"
    local trap_enable = uci:get("snmpinform", "@settings[0]", "enable") or "0"
    local trap_ip = uci:get("snmpinform", "@settings[0]", "server") or ""
    local trap_retries = uci:get("snmpinform", "@settings[0]", "retrytime") or "0"
    local trap_interval = uci:get("snmpinform", "@settings[0]", "report_interval") or "10"

    write_log("SNMP settings retrieved: snmpdenable=" .. snmp_enable .. ", enable=" .. trap_enable .. ", server=" .. trap_ip .. ", retrytime=" .. trap_retries .. ", report_interval=" .. trap_interval)
    io.write(cjson.encode({
        module = "snmp",
        version = "1.0",
        api = "get",
        errcode = 0,
        sid = data.sid,
        result = {
            snmp_enable = snmp_enable,
            trap_enable = trap_enable,
            trap_ip = trap_ip,
            trap_retries = trap_retries,
            trap_interval = trap_interval
        }
    }))
    io.flush()
end

-- 检查是否在CGI环境中运行
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- AC 调用接口
local M = {}

function M.set_config_from_ac(payload)
    write_log("[AC] set_config_from_ac called: " .. cjson.encode(payload))

    -- 调用现有的set_snmp函数
    local data = { param = payload }
    set_snmp(data)

    write_log("[AC] SNMP configuration applied")
    return true, "SNMP configuration applied by AC"
end

function M.get_config_for_ac()
    local config = {
        snmp_enable = uci:get("snmpinform", "settings", "snmp_enable") or "0",
        trap_enable = uci:get("snmpinform", "settings", "trap_enable") or "0",
        trap_ip = uci:get("snmpinform", "settings", "trap_ip") or "",
        trap_retries = uci:get("snmpinform", "settings", "trap_retries") or "2",
        trap_interval = uci:get("snmpinform", "settings", "trap_interval") or "30"
    }
    write_log("[AC] get_config_for_ac: retrieved SNMP config")
    return config
end

-- 仅在明确作为 CGI 脚本运行时执行
if arg and arg[0] and arg[0]:match("snmp%.lua") and is_cgi() then
    local function run()
        write_log("SNMP Settings API started")
        route_api()
        write_log("SNMP Settings API finished")
    end
    run()
end

return M