#!/usr/bin/lua

-- Copyright (c) 2024 The Linux Foundation. All rights reserved.
-- Not a Contribution.

-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

-- Lua环境：此脚本在OpenWRT的Lua环境中运行，确保有必要的Lua库，如luci.sys, luci.jsonc和luci.uci
-- UCI：这个脚本使用了uci库来直接操作配置文件。这比直接读写文件更为安全和标准�?

-- 安全性：确保处理用户输入时，进行了必要的验证和清理，以防止注入攻�?
-- 性能：由于脚本每次都需要读取和写入配置文件，对于频繁调用的API，可以考虑优化，例如通过缓存减少IO操作�?
-- 权限：确保脚本以必要的权限运行，修改网络配置文件和重启网络服务需要root权限�?


-- 引入模块
local cjson = require("cjson.safe")
local sys = require("luci.sys")
local uci = require("luci.model.uci").cursor()
-- local config_apply_mode = require("config_apply_mode") -- Use the new module name
local log_file = "/tmp/vlan_config.log" -- 日志文件路径

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end

-- 获取当前应用模式的辅助函数
local function get_current_apply_mode()
    local mode_file = "/etc/config_apply_mode_status"
    local f = io.open(mode_file, "r")
    if f then
        local mode = f:read("*l") -- Read the first line
        f:close()
        mode = mode and mode:gsub("^%s*(.-)%s*$", "%1") -- Trim whitespace
        if mode == "immediate" or mode == "deferred" then
            write_log("Read apply_mode from " .. mode_file .. ": " .. mode)
            return mode
        else
            write_log("Invalid content in " .. mode_file .. ": '" .. (mode or "nil") .. "'. Defaulting to 'immediate'.")
            return "immediate"
        end
    else
        write_log("Could not open " .. mode_file .. ". Defaulting to 'immediate'.")
        return "immediate"
    end
end



-- Helper function to add an item to a UCI list (simulates uci:add_list if not available)
local function add_uci_list_item(package, section_name, option_name, value_to_add)
    local current_list = uci:get_list(package, section_name, option_name)
    local found = false
    for _, v in ipairs(current_list) do
        if v == value_to_add then
            found = true
            break
        end
    end

    if not found then
        table.insert(current_list, value_to_add)
        local success, err = pcall(function() uci:set(package, section_name, option_name, current_list) end)
        if success then
            write_log("Added '" .. value_to_add .. "' to " .. package .. "." .. section_name .. "." .. option_name)
        else
            write_log("Error adding '" .. value_to_add .. "' to " .. package .. "." .. section_name .. "." .. option_name .. ": " .. (err or "unknown error"))
        end
    else
        write_log("Value '" .. value_to_add .. "' already exists in " .. package .. "." .. section_name .. "." .. option_name .. ". Skipping add.")
    end
end

-- Helper function to remove an item from a UCI list (simulates uci:del_list if not available)
local function del_uci_list_item(package, section_name, option_name, value_to_remove)
    local current_list = uci:get_list(package, section_name, option_name)
    local new_list = {}
    local removed = false
    for _, v in ipairs(current_list) do
        if v == value_to_remove and not removed then -- Only remove the first occurrence for now
            removed = true
        else
            table.insert(new_list, v)
        end
    end

    if removed then
        local success, err
        if #new_list == 0 then
            -- If the list becomes empty after removal, delete the option
            success, err = pcall(function() uci:delete(package, section_name, option_name) end)
            if success then
                write_log("Removed option " .. option_name .. " from " .. package .. "." .. section_name .. " as list became empty after removing '" .. value_to_remove .. "'")
            else
                write_log("Error deleting option " .. option_name .. " from " .. package .. "." .. section_name .. ": " .. (err or "unknown error"))
            end
        else
            -- Otherwise, set the updated list
            success, err = pcall(function() uci:set(package, section_name, option_name, new_list) end)
            if success then
                write_log("Removed '" .. value_to_remove .. "' from " .. package .. "." .. section_name .. "." .. option_name .. ". New list: " .. cjson.encode(new_list))
            else
                write_log("Error setting new list for " .. package .. "." .. section_name .. "." .. option_name .. ": " .. (err or "unknown error"))
            end
        end
    else
        write_log("Value '" .. value_to_remove .. "' not found in " .. package .. "." .. section_name .. "." .. option_name .. ". Skipping remove.")
    end
end

-- Helper function to create or get a network.switch_vlan section
local function ensure_switch_vlan(vid)
    local switch_vlan_section_name = nil
    uci:foreach("network", "switch_vlan", function(section)
        if tonumber(section.vlan) == tonumber(vid) then
            switch_vlan_section_name = section[".name"]
            return false -- Stop iteration
        end
    end)

    if not switch_vlan_section_name then
        switch_vlan_section_name = uci:add("network", "switch_vlan")
        uci:set("network", switch_vlan_section_name, "device", "switch1") -- Assuming switch1 is the device name
        uci:set("network", switch_vlan_section_name, "vlan", vid)
        write_log("Created new network.switch_vlan section for VID: " .. vid .. " (UCI name: " .. switch_vlan_section_name .. ")")
    end
    return switch_vlan_section_name
end

-- Helper function to update ports in a network.switch_vlan section.
local function update_switch_vlan_ports(vid, port_index, is_tagged)
    local switch_vlan_section_name = ensure_switch_vlan(vid) 
    local current_ports_str = uci:get("network", switch_vlan_section_name, "ports") or ""
    local current_ports_list = {}
    for p in string.gmatch(current_ports_str, "[^%s]+") do
        table.insert(current_ports_list, p)
    end

    local new_ports_list = {}
    local port_entry_to_add = tostring(port_index) .. (is_tagged and "t" or "")
    local port_entry_to_remove_opposite = tostring(port_index) .. (is_tagged and "" or "t") 

    local added = false
    for _, p in ipairs(current_ports_list) do
        if p == port_entry_to_remove_opposite then
            write_log("Removing opposite port type from switch_vlan " .. vid .. ": " .. p)
        elseif p == port_entry_to_add then
            table.insert(new_ports_list, p)
            added = true
        else
            table.insert(new_ports_list, p)
        end
    end

    if not added then
        table.insert(new_ports_list, port_entry_to_add)
    end

    -- Ensure '0t' (CPU port tagged) comes first, then sort others
    local has_0t = false
    local other_ports = {}
    for _, p in ipairs(new_ports_list) do
        if p == "0t" then
            has_0t = true
        else
            table.insert(other_ports, p)
        end
    end
    table.sort(other_ports, function(a, b) return tonumber(string.match(a, "%d+")) < tonumber(string.match(b, "%d+")) end) -- Sort numerically by port index
    local final_ports_str = has_0t and "0t " .. table.concat(other_ports, " ") or table.concat(other_ports, " ")
    final_ports_str = final_ports_str:gsub("^[\\s]+", "") -- Trim leading spaces

    uci:set("network", switch_vlan_section_name, "ports", final_ports_str)
    write_log("Updated network.switch_vlan " .. vid .. " ports to: '" .. final_ports_str .. "'")
end

-- Helper function to remove a specific port entry (tagged or untagged) from a network.switch_vlan section.
local function remove_switch_vlan_port(vid, port_index, is_tagged)
    local switch_vlan_section_name = uci:get_first("network", "switch_vlan", { vlan = tostring(vid) })
    if not switch_vlan_section_name then
        write_log("Info: network.switch_vlan section for VID " .. vid .. " not found. Nothing to remove for port " .. port_index .. ".")
        return
    end

    local current_ports_str = uci:get("network", switch_vlan_section_name, "ports") or ""
    local current_ports_list = {}
    for p in string.gmatch(current_ports_str, "[^%s]+") do
        table.insert(current_ports_list, p)
    end

    local new_ports_list = {}
    local port_entry_to_remove = tostring(port_index) .. (is_tagged and "t" or "")
    local removed_count = 0
    for _, p in ipairs(current_ports_list) do
        if p == port_entry_to_remove then
            removed_count = removed_count + 1
        else
            table.insert(new_ports_list, p)
        end
    end

    if removed_count > 0 then
        local has_0t = false
        local other_ports = {}
        for _, p in ipairs(new_ports_list) do
            if p == "0t" then
                has_0t = true
            else
                table.insert(other_ports, p)
            end
        end
        table.sort(other_ports, function(a, b) return tonumber(string.match(a, "%d+")) < tonumber(string.match(b, "%d+")) end)
        local final_ports_str = has_0t and "0t " .. table.concat(other_ports, " ") or table.concat(other_ports, " ")
        final_ports_str = final_ports_str:gsub("^[\\s]+", "")

        uci:set("network", switch_vlan_section_name, "ports", final_ports_str)
        write_log("Removed " .. removed_count .. " instances of port " .. port_entry_to_remove .. " from network.switch_vlan " .. vid .. ". New ports: '" .. final_ports_str .. "'")
    else
        write_log("Info: Port " .. port_entry_to_remove .. " not found in network.switch_vlan " .. vid .. ". Nothing to remove.")
    end
end

-- Utility function to check if a value exists in a table (array)
function table.contains(tab, val)
    for _, v in ipairs(tab) do
        if v == val then
            return true
        end
    end
    return false
end

-- Helper to get the radio band from a wifi-device UCI section
local function get_radio_band(radio_uci_section_name)
    local hwmode = uci:get("wireless", radio_uci_section_name, "hwmode")
    if hwmode then
        if string.match(hwmode, "a$") then -- e.g., "11a", "11ac", "11axa"
            return "5G"
        elseif string.match(hwmode, "g$") then -- e.g., "11g", "11n", "11axg"
            return "2.4G"
        elseif string.match(hwmode, "be$") then -- For WiFi 7 (802.11be) in 6GHz
            return "6G"
        end
    end
    return "Unknown Band"
end

-- Helper to get interface mappings (GUI name to internal details)
local function get_interface_mappings()
    local mappings = {}
    local lan_physical_ifname_base = "eth1" 
    local lan_cpu_vlan_ifname_default = "eth1.1"
    -- NOTE: lan_physical_switch_port_index assumes eth1 is connected to switch port 1. 
    -- This might need dynamic discovery if the mapping is not fixed across devices.
    local lan_physical_switch_port_index = "1"

    -- LAN Port (GUI "LAN1" corresponds to physical switch port 1 and CPU's eth1.1)
    mappings["LAN1"] = {
        type = "lan_physical_port", -- Indicates this is a physical switch port
        cpu_vlan_ifname_default = lan_cpu_vlan_ifname_default, -- The default CPU-side VLAN interface used by 'lan' bridge
        physical_switch_port_index = lan_physical_switch_port_index,
        description = "Main LAN Port"
    }

    -- Wireless Interfaces (dynamic discovery and descriptive naming)
    uci:foreach("wireless", "wifi-iface", function(section)
        local uci_section_name = section[".name"]
        local ifname = section.ifname
        local mode = section.mode or "ap" -- Default mode to AP if not specified
        local ssid = section.ssid or "" 
        local device_section_name = section.device -- e.g., "wifi0", "wifi1"
        local disabled = section.disabled or "0" -- Read disabled option

        if not ifname then
            write_log("Warning: wifi-iface section " .. uci_section_name .. " has no ifname. Skipping.")
            return -- Skip this section if no ifname
        end

        if disabled == "1" then -- Filter out disabled interfaces
            write_log("Info: Skipping disabled wifi-iface section: " .. uci_section_name .. " (ifname: " .. ifname .. ")")
            return
        end

        local band_info = "Unknown Band"
        if device_section_name then
            band_info = get_radio_band(device_section_name)
        end

        local final_description = ""
        local ap_designation = ""

        if mode == "ap" then
            local ap_number_match = string.match(uci_section_name, "^wlan(%d+)$")
            local son_match = string.match(ifname, "^son(%d+)$") -- Check if it's a 'son' interface

            if son_match then
                -- For son interfaces, it's a mesh AP. Naming: SSID (Band AP)
                final_description = string.format("%s (%s AP)", ssid, band_info)
            elseif ap_number_match then
                local ap_index = tonumber(ap_number_match)
                -- Determine AP designation (AP1, AP2, etc.) based on index range
                if ap_index >= 0 and ap_index <= 3 then -- wlan0-wlan3 for 2.4G APs
                    ap_designation = "AP" .. (ap_index + 1)
                elseif ap_index >= 10 and ap_index <= 13 then -- wlan10-wlan13 for 5G APs
                    ap_designation = "AP" .. (ap_index - 9)
                elseif ap_index >= 30 and ap_index <= 33 then -- wlan30-wlan33 for 6G APs
                    ap_designation = "AP" .. (ap_index - 29)
                end
                
                if ap_designation ~= "" and band_info ~= "Unknown Band" then
                    -- For regular APs with a clear designation: SSID (Band APX)
                    final_description = string.format("%s (%s %s)", ssid, band_info, ap_designation)
                else
                    -- Fallback for APs without a clear designation (e.g., new type or misconfiguration)
                    if ssid ~= "" then
                        final_description = string.format("%s (%s AP)", ssid, band_info)
                    else
                        final_description = string.format("%s (%s AP)", ifname, band_info) -- Use ifname if SSID is empty
                    end
                end
            end
        elseif mode == "sta" and band_info ~= "Unknown Band" then
            -- For STA interfaces: SSID (Band STA)
            final_description = string.format("%s (%s STA)", ssid, band_info)
        end

        -- Generic fallback if final_description is still empty (e.g., not a recognized AP/STA/son, or Unknown Band)
        if final_description == "" then
            if ssid ~= "" then
                final_description = ssid
                if band_info ~= "Unknown Band" then
                    final_description = final_description .. " (" .. band_info .. " " .. string.upper(mode) .. ")"
                else
                    final_description = final_description .. " (" .. string.upper(mode) .. ")"
                end
            else
                final_description = ifname .. " (" .. string.upper(mode) .. ")"
                if band_info ~= "Unknown Band" then
                    final_description = ifname .. " (" .. band_info .. " " .. string.upper(mode) .. ")"
                end
            end
        end

        mappings[final_description] = {
            type = "wifi",
            uci_section_name = uci_section_name,
            ifname = ifname,
            description = final_description -- This "description" is what will be used as the key in 'mappings'
        }
    end)

    -- Consider adding mappings for explicit eth0, eth1, etc. if they are not part of LAN1 mapping
    -- and need separate VLAN configuration (e.g., dedicated WAN port on eth0 with VLANs)
    -- For now, relying on LAN1 mapping for primary LAN port

    return mappings
end

-- Helper to create or ensure existence of a network.interface bridge and add a member
local function ensure_interface_bridge(bridge_name, proto, type, ifname_to_add)
    local bridge_uci_section_name = nil
    uci:foreach("network", "interface", function(section)
        if section.name == bridge_name then
            bridge_uci_section_name = section[".name"]
            return false -- Stop iteration
        end
    end)

    if not bridge_uci_section_name then
        bridge_uci_section_name = uci:add("network", "interface")
        uci:set("network", bridge_uci_section_name, "name", bridge_name)
        uci:set("network", bridge_uci_section_name, "proto", proto)
        uci:set("network", bridge_uci_section_name, "type", type)
        write_log("Created new network.interface bridge: " .. bridge_name .. " (UCI: " .. bridge_uci_section_name .. ")")
    end

    local current_ifnames = uci:get_list("network", bridge_uci_section_name, "ifname")
    local has_changed = false

    if not table.contains(current_ifnames, ifname_to_add) then
        table.insert(current_ifnames, ifname_to_add)
        has_changed = true
    end

    -- Sort and deduplicate for consistent output, similar to lan bridge members
    local sorted_ifnames = {}
    local temp_seen = {} -- For deduplication during sort preparation
    for _, iface in ipairs(current_ifnames) do
        if not temp_seen[iface] then
            table.insert(sorted_ifnames, iface)
            temp_seen[iface] = true
        end
    end

    table.sort(sorted_ifnames, function(a, b)
        -- Custom sort order for consistency: eth1.X first (numerically), then athX (numerically), then others
        local lan_physical_ifname_base = "eth1" -- Define locally as it's used in this scope
        local a_is_eth_vlan = string.match(a, "^" .. lan_physical_ifname_base .. "%.(%d+)$")
        local b_is_eth_vlan = string.match(b, "^" .. lan_physical_ifname_base .. "%.(%d+)$")
        local a_is_ath = string.match(a, "^ath(%d+)$")
        local b_is_ath = string.match(b, "^ath(%d+)$")

        -- Prioritize eth1.X interfaces
        if a_is_eth_vlan and not b_is_eth_vlan then return true end
        if not a_is_eth_vlan and b_is_eth_vlan then return false end
        
        -- Then athX interfaces
        if a_is_ath and not b_is_ath then return true end
        if not a_is_ath and b_is_ath then return false end

        -- If both are eth1.X or both are athX, sort numerically
        if a_is_eth_vlan and b_is_eth_vlan then
            return tonumber(a_is_eth_vlan) < tonumber(b_is_eth_vlan)
        elseif a_is_ath and b_is_ath then
            return tonumber(a_is_ath) < tonumber(b_is_ath)
        end

        return a < b -- Fallback to lexicographical sort for other cases
    end)

    local final_ifname_str = table.concat(sorted_ifnames, " ")
    uci:set("network", bridge_uci_section_name, "ifname", final_ifname_str)
    write_log("Updated bridge '" .. bridge_name .. "' ifname to: '" .. final_ifname_str .. "'")
    return bridge_uci_section_name
end

-- Helper to create or ensure existence of a network.device 802.1q subinterface (e.g., eth1.100)
local function ensure_8021q_subinterface(vlan_ifname, base_ifname, vid)
    local subiface_uci_section = uci:get_first("network", "device", { name = vlan_ifname })
    if not subiface_uci_section then
        subiface_uci_section = uci:add("network", "device")
        uci:set("network", subiface_uci_section, "name", vlan_ifname)
    end
    uci:set("network", subiface_uci_section, ".type", "8021q")
    uci:set("network", subiface_uci_section, "ifname", base_ifname)
    uci:set("network", subiface_uci_section, "vid", tostring(vid))
    write_log("Ensured 802.1q subinterface: " .. vlan_ifname .. " (UCI: " .. subiface_uci_section .. ")")
    return subiface_uci_section
end

-- 路由到具体逻辑
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 获取 POST 数据长度
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""

    -- 读取 POST 数据
    if POSTLength > 0 then
        POST = io.read(POSTLength)
        write_log("Received POST data: " .. (POST or "nil"))
    else
        write_log("No POST data received or CONTENT_LENGTH is 0")
    end

    -- 确保读取成功
    if not POST or POST == "" then
        local error_message = "Failed to retrieve POST data"
        write_log(error_message)
        io.write(cjson.encode({
            module = "vlan",
            version = "1.0",
            errcode = 1,
            result = { message = error_message }
        }))
        io.flush()
        return
    end

    -- 解析 POST 数据为JSON
    local requestData = cjson.decode(POST)
    if not requestData then
        local error_message = "Invalid JSON input"
        write_log(error_message)
        io.write(cjson.encode({
            module = "vlan",
            version = "1.0",
            errcode = 2,
            result = { message = error_message }
        }))
        io.flush()
        return
    end

    -- 检查请求格式
    if not requestData.version or not requestData.sid or not requestData.module or not requestData.api then
        local error_message = "Invalid request format"
        write_log(error_message)
        io.write(cjson.encode({
            module = "vlan",
            version = "1.0",
            errcode = 3,
            result = { message = error_message }
        }))
        io.flush()
        return
    end

    if requestData.api == "set" then
        write_log("Calling set_vlan with data: " .. cjson.encode(requestData))
        set_vlan(requestData)
    elseif requestData.api == "get" then
        write_log("Calling get_vlan with data: " .. cjson.encode(requestData))
        get_vlan(requestData)
    else
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "vlan",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
    end
end

-- set_vlan function (full replacement to support new JSON format and actual network config)
function set_vlan(data)
    write_log("Debug: Type of uci: " .. type(uci))
    write_log("Debug: Type of uci.add_list: " .. type(uci.add_list)) -- This will now correctly be nil
    write_log("Debug: Type of uci.get_list: " .. type(uci.get_list))
    write_log("Debug: Type of uci.del_list: " .. type(uci.del_list)) -- New debug log
    write_log("Starting set_vlan with updated logic for LAN/WAN config.")
    -- 兼容AC下发旧格式模板
    local param = data.params or {}
    if type(param) == "table" and not param.interfaces and #param > 0 then
        param = { interfaces = param }
        write_log("兼容AC下发旧格式模板，已自动转换为新格式")
    end

    if not param.interfaces then
        write_log("Error: 'interfaces' array is missing in set_vlan parameters.")
        io.write(cjson.encode({ module = "vlan", version = "1.0", errcode = 4, result = { message = "'interfaces' array is missing." } }))
        return
    end

    local interface_mappings = get_interface_mappings()
    local lan_physical_ifname_base = "eth1" 
    local lan_physical_switch_port_index = interface_mappings["LAN1"].physical_switch_port_index

    local management_vlan_id = tonumber(param.management_vlan_id)
    if management_vlan_id and (management_vlan_id < 1 or management_vlan_id > 4094) then
        write_log("Warning: Invalid management_vlan_id: " .. management_vlan_id .. ". Ignoring.")
        management_vlan_id = nil
    end

    -- --- Step 1: Cleanup Existing Dynamic VLAN Configurations ---
    write_log("Clearing existing dynamic VLAN bridges and 802.1q devices before reconfiguring.")
    -- Delete any br-vlanXXX bridges (excluding 'lan' bridge, as its ifname will be updated)
    uci:foreach("network", "interface", function(section)
        local name = section.name or ""
        -- Only delete bridges that were dynamically created (e.g., br-vlanXXX) and are not the 'lan' bridge itself
        if string.match(name, "^br%-vlan(%d+)$") and name ~= "lan" then
            write_log("Deleting old dynamic bridge: " .. name .. " (UCI: " .. section[".name"] .. ")")
            uci:delete("network", section[".name"])
        end
    end)
    -- Delete any eth1.XXX devices created dynamically (excluding eth1.1 and eth1.2)
    uci:foreach("network", "device", function(section)
        local name = section.name or ""
        if string.match(name, "^" .. lan_physical_ifname_base .. "%.(%d+)$") and name ~= "eth1.1" and name ~= "eth1.2" then
            write_log("Deleting old dynamic 802.1q device: " .. name .. " (UCI: " .. section[".name"] .. ")")
            uci:delete("network", section[".name"])
        end
    end)
    -- Delete all existing switch_vlan entries for a clean slate
    uci:foreach("network", "switch_vlan", function(section)
        write_log("Deleting old switch_vlan entry: " .. section[".name"] .. " (VLAN ID: " .. (section.vlan or "N/A") .. ")")
        uci:delete("network", section[".name"])
    end)
    write_log("Cleaned up all existing switch_vlan entries.")


    -- --- Step 2: Configure Management VLAN (Affects 'lan' interface) ---
    local lan_uci_section = nil
    uci:foreach("network", "interface", function(section)
        if section[".name"] == "lan" then
            lan_uci_section = section
            write_log("Debug: Found lan_uci_section in set_vlan via foreach: " .. section[".name"])
            return false -- Stop iteration after finding it
        end
    end)

    if not lan_uci_section then
        write_log("Error: 'lan' interface section not found in set_vlan.")
        io.write(cjson.encode({
            module = "vlan", version = "1.0", errcode = 1, result = { message = "Failed to find 'lan' interface section." }
        }))
        return
    end

    local lan_uci_section_name = lan_uci_section[".name"]
    local current_lan_ifnames_str = uci:get("network", lan_uci_section_name, "ifname") or ""
    write_log("Debug: Original lan bridge ifnames string for set_vlan: '" .. current_lan_ifnames_str .. "'")

    local new_lan_bridge_members = {}
    local mgmt_cpu_vlan_ifname_to_use = nil
    local eth1_1_default_ifname = interface_mappings["LAN1"].cpu_vlan_ifname_default -- "eth1.1"

    if management_vlan_id then
        write_log("Configuring management VLAN with ID: " .. management_vlan_id)
        mgmt_cpu_vlan_ifname_to_use = lan_physical_ifname_base .. "." .. management_vlan_id

        -- 创建管理VLAN的专用接口，保持原有LAN接口不变
        local mgmt_interface_name = "mgmt_vlan" .. management_vlan_id

        -- 删除可能存在的旧管理VLAN接口
        uci:foreach("network", "interface", function(section)
            if section[".name"] and string.match(section[".name"], "^mgmt_vlan%d+$") then
                write_log("Deleting old management VLAN interface: " .. section[".name"])
                uci:delete("network", section[".name"])
            end
        end)

        -- 创建新的管理VLAN接口，使用专用IP地址
        uci:set("network", mgmt_interface_name, "interface")
        uci:set("network", mgmt_interface_name, "proto", "static")
        uci:set("network", mgmt_interface_name, "ipaddr", "192.168." .. management_vlan_id .. ".254")
        uci:set("network", mgmt_interface_name, "netmask", "*************")
        uci:set("network", mgmt_interface_name, "ifname", mgmt_cpu_vlan_ifname_to_use)

        -- 保持原有LAN接口的IP地址不变，确保用户可以继续使用原IP访问
        -- 不修改lan接口的ipaddr，保持*************
        uci:set("network", lan_uci_section_name, "def_ifname", mgmt_cpu_vlan_ifname_to_use)
        write_log("Created dedicated management VLAN interface '" .. mgmt_interface_name .. "' with IP 192.168." .. management_vlan_id .. ".254")
        write_log("Original LAN interface IP (*************) preserved for user access compatibility")

        -- Ensure the 802.1q subinterface for management VLAN exists
        ensure_8021q_subinterface(mgmt_cpu_vlan_ifname_to_use, lan_physical_ifname_base, management_vlan_id)

        -- Add the new management VLAN interface to the list of bridge members
        table.insert(new_lan_bridge_members, mgmt_cpu_vlan_ifname_to_use)
        
        -- Configure the physical switch port (LAN1) as tagged for the management VLAN
        ensure_switch_vlan(management_vlan_id)
        update_switch_vlan_ports(management_vlan_id, lan_physical_switch_port_index, true) -- Tagged for LAN1
        update_switch_vlan_ports(management_vlan_id, "0", true) -- CPU port '0' tagged ('0t') for management VLAN
        write_log("Configured physical LAN port " .. lan_physical_switch_port_index .. " as tagged for management VLAN " .. management_vlan_id .. " in switch_vlan.")
        -- 自动补全ath*接口到管理VLAN bridge成员
        uci:foreach("wireless", "wifi-iface", function(section)
            local ifname = section.ifname
            local disabled = section.disabled or "0"
            if ifname and disabled ~= "1" and string.match(ifname, "^ath%d+$") then
                if not table.contains(new_lan_bridge_members, ifname) then
                    table.insert(new_lan_bridge_members, ifname)
                    write_log("自动将 " .. ifname .. " 加入管理VLAN bridge成员")
                end
            end
        end)
    end

    -- 确保eth1.2接口始终存在（用于LAN2端口）
    local eth1_2_ifname = "eth1.2"
    ensure_8021q_subinterface(eth1_2_ifname, lan_physical_ifname_base, 2)

    -- 确保eth1.2在LAN bridge中（如果不是管理VLAN接口的话）
    if not mgmt_cpu_vlan_ifname_to_use or mgmt_cpu_vlan_ifname_to_use ~= eth1_2_ifname then
        if not table.contains(new_lan_bridge_members, eth1_2_ifname) then
            table.insert(new_lan_bridge_members, eth1_2_ifname)
            write_log("Ensured eth1.2 is included in LAN bridge for LAN2 port functionality")
        end
    end

    if not management_vlan_id then
        write_log("No management VLAN ID provided or invalid. Reverting 'lan' interface to default eth1.1 based bridge.")
        mgmt_cpu_vlan_ifname_to_use = eth1_1_default_ifname

        -- 清理所有管理VLAN接口，但保持LAN接口IP不变
        uci:foreach("network", "interface", function(section)
            if section[".name"] and string.match(section[".name"], "^mgmt_vlan%d+$") then
                write_log("Cleaning up management VLAN interface: " .. section[".name"])
                uci:delete("network", section[".name"])
            end
        end)

        -- 恢复LAN接口的默认ifname，但保持IP地址不变（用户友好）
        uci:set("network", lan_uci_section_name, "def_ifname", eth1_1_default_ifname)
        write_log("Reverted 'lan' interface def_ifname to " .. eth1_1_default_ifname .. " (IP address preserved for user convenience).")

        -- Add the default eth1.1 interface to the list of bridge members
        table.insert(new_lan_bridge_members, mgmt_cpu_vlan_ifname_to_use)

        -- Ensure switch port 1 is untagged for VLAN 1 if management VLAN is removed
        ensure_switch_vlan(1) -- Ensure VLAN 1 entry exists
        update_switch_vlan_ports(1, lan_physical_switch_port_index, false) -- Set as untagged for LAN1
        remove_switch_vlan_port(1, lan_physical_switch_port_index, true) -- Ensure not tagged for VLAN 1
        -- Also ensure CPU port 0 is tagged for VLAN 1
        update_switch_vlan_ports(1, "0", true) -- CPU port '0' tagged ('0t') for VLAN 1
    end

    -- Populate existing_lan_members for efficient lookup
    local existing_lan_members = {}
    for iface in string.gmatch(current_lan_ifnames_str, "[^%s]+") do
        existing_lan_members[iface] = true
    end

    -- Iterate through original interfaces, adding non-management eth1.X and ath* interfaces
    for iface_name, _ in pairs(existing_lan_members) do
        -- If it's an eth1.X interface and it's not the new/default management one, skip it.
        -- This handles cases where user had a different management VLAN set previously, or eth1.2 should be kept.
        local is_eth1_vlan_iface = string.match(iface_name, "^" .. lan_physical_ifname_base .. "%.%d+$")
        if is_eth1_vlan_iface then
            if iface_name ~= mgmt_cpu_vlan_ifname_to_use then
                -- If it's eth1.2 and was in original config, add it back explicitly if not already there
                if iface_name == "eth1.2" and not table.contains(new_lan_bridge_members, "eth1.2") then
                    table.insert(new_lan_bridge_members, "eth1.2")
                end
                -- All other eth1.X are considered old management VLANs and will be excluded.
            end
        else
            -- Add all other non-eth1.X interfaces (ath*, etc.) if not already in the list
            if not table.contains(new_lan_bridge_members, iface_name) then
                table.insert(new_lan_bridge_members, iface_name)
            end
        end
    end

    -- Final cleanup and sorting for consistent output
    local sorted_final_lan_ifnames = {}
    local temp_seen = {} -- For deduplication during sort preparation
    for _, iface in ipairs(new_lan_bridge_members) do
        if not temp_seen[iface] then
            table.insert(sorted_final_lan_ifnames, iface)
            temp_seen[iface] = true
        end
    end

    -- Custom sort order for consistency: eth1.X first (numerically), then athX (numerically), then others
    table.sort(sorted_final_lan_ifnames, function(a, b)
        local a_is_eth_vlan = string.match(a, "^" .. lan_physical_ifname_base .. "%.(%d+)$")
        local b_is_eth_vlan = string.match(b, "^" .. lan_physical_ifname_base .. "%.(%d+)$")
        local a_is_ath = string.match(a, "^ath(%d+)$")
        local b_is_ath = string.match(b, "^ath(%d+)$")

        -- Prioritize eth1.X interfaces
        if a_is_eth_vlan and not b_is_eth_vlan then return true end
        if not a_is_eth_vlan and b_is_eth_vlan then return false end
        
        -- Then athX interfaces
        if a_is_ath and not b_is_ath then return true end
        if not a_is_ath and b_is_ath then return false end

        -- If both are eth1.X or both are athX, sort numerically
        if a_is_eth_vlan and b_is_eth_vlan then
            return tonumber(a_is_eth_vlan) < tonumber(b_is_eth_vlan)
        elseif a_is_ath and b_is_ath then
            return tonumber(a_is_ath) < tonumber(b_is_ath)
        end

        return a < b -- Fallback to lexicographical sort for other cases
    end)

    local final_lan_ifname_str = table.concat(sorted_final_lan_ifnames, " ")
    uci:set("network", lan_uci_section[".name"], "ifname", final_lan_ifname_str)
    write_log("Set 'lan' interface ifname to: " .. final_lan_ifname_str)

    -- --- Step 3: Configure Per-Interface VLANs (PVID and Tagged VLANs) ---
    local configured_interfaces = {} -- Track interfaces that were explicitly configured in this request
    for _, iface_config in ipairs(param.interfaces) do
        local logical_name = iface_config.name
        local pvid = tonumber(iface_config.pvid)
        local tagged_vlans = iface_config.tagged_vlans or {}

        if not logical_name then
            write_log("Warning: Interface name is missing for an interface configuration. Skipping.")
        else
            -- Validate VLAN IDs
            if pvid and (pvid < 0 or pvid > 4094) then
                write_log("Warning: Invalid PVID for " .. logical_name .. ": " .. pvid .. ". Ignoring PVID.")
                pvid = nil
            end
            local valid_tagged_vlans = {}
            for _, vid in ipairs(tagged_vlans) do
                local num_vid = tonumber(vid)
                if num_vid and num_vid >= 1 and num_vid <= 4094 then
                    table.insert(valid_tagged_vlans, num_vid)
                else
                    write_log("Warning: Invalid tagged VLAN ID for " .. logical_name .. ": " .. vid .. ". Ignoring.")
                end
            end
            tagged_vlans = valid_tagged_vlans

            write_log("Processing interface: " .. logical_name .. ", PVID: " .. (pvid or "none") .. ", Tagged: " .. cjson.encode(tagged_vlans))

            local iface_details = interface_mappings[logical_name]
            if not iface_details then
                write_log("Warning: Unknown logical interface name: " .. logical_name .. ". Skipping configuration.")
            else
                local actual_ifname = iface_details.ifname -- For wireless interfaces
                local uci_wifi_iface_section = iface_details.uci_section_name
                local is_lan_physical_gui_entry = (iface_details.type == "lan_physical_port")
                local is_wifi_iface = (iface_details.type == "wifi")

                configured_interfaces[logical_name] = true -- Mark as configured

                -- --- Handle Physical LAN Port (LAN1 GUI entry) VLANs via switch_vlan ---
                if is_lan_physical_gui_entry then
                    -- Configure PVID on switch_vlan for physical port 1
                    if pvid then
                        ensure_switch_vlan(pvid)
                        update_switch_vlan_ports(pvid, lan_physical_switch_port_index, false) -- Set as untagged
                        -- Ensure it's not tagged for this VID
                        remove_switch_vlan_port(pvid, lan_physical_switch_port_index, true)
                        write_log("Set switch port " .. lan_physical_switch_port_index .. " as untagged for VID " .. pvid .. " (LAN1 PVID).")
                    else
                        -- If no PVID, remove untagged from this port for any VLAN
                        uci:foreach("network", "switch_vlan", function(section)
                            local vid_on_switch = tonumber(section.vlan)
                            if vid_on_switch then
                                remove_switch_vlan_port(vid_on_switch, lan_physical_switch_port_index, false)
                            end
                        end)
                        write_log("Removed any untagged VLAN from switch port " .. lan_physical_switch_port_index .. " (LAN1 PVID removed).")
                    end

                    -- Configure Tagged VLANs on switch_vlan for physical port 1
                    for _, vid in ipairs(tagged_vlans) do
                        ensure_switch_vlan(vid)
                        update_switch_vlan_ports(vid, lan_physical_switch_port_index, true) -- Set as tagged
                        -- Ensure it's not untagged for this VID
                        remove_switch_vlan_port(vid, lan_physical_switch_port_index, false)
                        write_log("Set switch port " .. lan_physical_switch_port_index .. " as tagged for VID " .. vid .. " (LAN1 Tagged).")
                    end
                elseif is_wifi_iface then
                    -- Handle wireless interface network assignment and vlan_tag option
                    write_log("Configuring wireless interface: " .. actual_ifname .. " (UCI section: " .. uci_wifi_iface_section .. ")")
                    
                    if pvid or #tagged_vlans > 0 then
                        -- If wireless interface has any VLAN config, ensure vlan_tag is set to 1
                        uci:set("wireless", uci_wifi_iface_section, "vlan_tag", "1")
                        write_log("Set wireless." .. uci_wifi_iface_section .. ".vlan_tag=1")
                        
                        -- If it has specific VLANs, ensure it's not tied to 'lan' or 'lan0' directly
                        -- It will be part of the specific VLAN bridge
                        uci:delete("wireless", uci_wifi_iface_section, "network")
                        write_log("Deleted wireless." .. uci_wifi_iface_section .. ".network as specific VLANs are configured.")
                    else
                        -- If no specific VLANs, but management VLAN is active, assign to 'lan0'
                        if management_vlan_id then
                            uci:set("wireless", uci_wifi_iface_section, "network", "lan0")
                            write_log("Set wireless." .. uci_wifi_iface_section .. ".network=lan0 due to management VLAN.")
                            uci:set("wireless", uci_wifi_iface_section, "vlan_tag", "1") -- Still enable vlan_tag
                        else
                            -- Otherwise, set to default 'lan' if it wasn't specified with VLANs
                            uci:set("wireless", uci_wifi_iface_section, "network", "lan")
                            write_log("Set wireless." .. uci_wifi_iface_section .. ".network=lan as no specific VLANs or management VLAN.")
                            uci:delete("wireless", uci_wifi_iface_section, "vlan_tag") -- Disable vlan_tag
                        end
                    end

                    -- For PVID on wireless interfaces, set the 'network' option to the VLAN bridge.
                    -- Note: wireless PVID is typically handled by setting 'network' to the VLAN bridge name,
                    -- not via switch_vlan ports.
                    if pvid then
                        local vlan_bridge_name = "vlan" .. pvid
                        uci:set("wireless", uci_wifi_iface_section, "network", vlan_bridge_name)
                        write_log("Set wireless." .. uci_wifi_iface_section .. ".network=" .. vlan_bridge_name .. " for PVID.")
                        -- Ensure the wireless interface is added to the bridge's ifname list as well
                        ensure_interface_bridge(vlan_bridge_name, "none", "bridge", actual_ifname) -- Add the wireless interface itself to the bridge
                        write_log("Added wireless interface '" .. actual_ifname .. "' to bridge '" .. vlan_bridge_name .. "' for PVID.")
                    end

                    -- For tagged_vlans on wireless interfaces, these are usually handled by the
                    -- wireless driver itself or by the bridge. We mainly ensure the 802.1q subinterface
                    -- is created and part of the correct bridge.
                    for _, vid in ipairs(tagged_vlans) do
                        local vlan_device_name = actual_ifname .. "." .. vid
                        local vlan_bridge_name = "vlan" .. vid
                        ensure_8021q_subinterface(vlan_device_name, actual_ifname, vid)
                        ensure_interface_bridge(vlan_bridge_name, "none", "bridge", vlan_device_name)
                        write_log("Ensured wireless 802.1q subinterface " .. vlan_device_name .. " and bridge " .. vlan_bridge_name .. " for tagged VLAN " .. vid .. ".")
                    end

                end
            end
        end
    end -- End for loop over interfaces in request

    -- --- Step 4: Cleanup Unconfigured Wireless Interfaces ---
    -- Revert wireless interfaces not explicitly configured to 'lan' bridge and remove from other dynamic VLAN bridges
    for logical_name, details in pairs(interface_mappings) do
        if details.type == "wifi" and not configured_interfaces[logical_name] then
            local uci_wifi_iface_section = details.uci_section_name
            local actual_ifname = details.ifname
            if uci_wifi_iface_section and actual_ifname then
                local current_network = uci:get("wireless", uci_wifi_iface_section, "network")
                if current_network and string.match(current_network, "^br%-vlan(%d+)$") then
                    uci:set("wireless", uci_wifi_iface_section, "network", "lan")
                    write_log("Cleaned up wireless interface " .. logical_name .. " by reverting network to 'lan'.")
                end
                -- Also remove from any br-vlanXXX bridges it might still be a member of
                uci:foreach("network", "interface", function(section)
                    if section.type == "bridge" and string.match(section.name or "", "^br%-vlan(%d+)$") then
                        write_log("Debug (cleanup unconfigured): Processing bridge: " .. (section.name or "nil_name") .. " for iface: " .. (actual_ifname or "nil"))
                        -- Use our custom del_uci_list_item function
                        del_uci_list_item("network", section[".name"], "bridge_ports", actual_ifname)
                        write_log("Removed " .. actual_ifname .. " from bridge " .. (section.name or "N/A") .. " (cleanup for unconfigured wireless).")
                    end
                end)
            end
        end
    end

    -- --- Step 5: Commit and Restart ---
    uci:commit("network")
    write_log("Network UCI configuration committed.")
    uci:commit("wireless")
    write_log("Wireless UCI configuration committed.")

    -- Send response to Web interface immediately
    io.write(cjson.encode({
        module = "vlan", version = "1.0", errcode = 0, sid = data.sid, result = { message = "VLAN configuration applied successfully. Services are restarting..." }
    }))
    write_log("Response sent to Web interface.")

    local apply_mode = get_current_apply_mode()
    write_log("Current apply mode: " .. apply_mode)

    if apply_mode == "immediate" then
        write_log("Applying configuration immediately: restarting services.")
        -- Define the path for the temporary restart script
        local restart_script_path = "/tmp/vlan_restart.sh"

        -- Define the content of the restart script
        local restart_script_content = [[#!/bin/sh
sleep 2
/etc/init.d/network restart
wifi
rm -f "$0"
]]

        -- Escape newlines and single quotes for echo -e
        local escaped_content = string.gsub(restart_script_content, "'", "'\''")
        escaped_content = string.gsub(escaped_content, "\n", "\\n") -- Escape backslashes for lua string

        -- Create the temporary restart script using /bin/echo -e
        write_log("Creating temporary restart script: " .. restart_script_path .. " using /bin/echo -e.")
        local create_script_command = "/bin/echo -e '" .. escaped_content .. "' > " .. restart_script_path
        local status_create, reason_create = sys.call(create_script_command)

        if status_create ~= 0 then
            write_log("Failed to create temporary restart script using /bin/echo -e. Status: " .. status_create .. ", Reason: " .. (reason_create or "N/A"))
            return
        end

        -- Make the script executable
        write_log("Making script executable: " .. restart_script_path)
        local chmod_command = "/bin/chmod +x " .. restart_script_path
        local status_chmod, reason_chmod = sys.call(chmod_command)

        if status_chmod ~= 0 then
            write_log("Failed to make script executable. Status: " .. status_chmod .. ", Reason: " .. (reason_chmod or "N/A"))
            return
        end

        -- Finally, execute the script in the background
        write_log("Scheduling background restart: " .. restart_script_path .. " &")
        local execute_script_command = restart_script_path .. " &"
        local status_execute, reason_execute = sys.call(execute_script_command)

        if status_execute ~= 0 then
            write_log("Failed to schedule background service restarts. Status: " .. status_execute .. ", Reason: " .. (reason_execute or "N/A"))
        else
            write_log("Background service restarts scheduled successfully.")
        end
    else
        write_log("Apply mode is deferred. Services will not be restarted automatically.")
        -- Update the message to reflect deferred application
        io.write(cjson.encode({
            module = "vlan", version = "1.0", errcode = 0, sid = data.sid, result = { message = "VLAN configuration saved. Apply deferred." }
        }))
        io.flush()
    end

end

-- get_vlan function (updated to support new JSON format and actual network config)
function get_vlan(data)
    write_log("DEBUG: get_vlan function entered.")
    write_log("Starting get_vlan (updated logic).")
    local result = {
        management_vlan_id = nil,
        interfaces = {}
    }

    local interface_mappings = get_interface_mappings()
    local lan_physical_ifname_base = "eth1"
    local lan_physical_switch_port_index = interface_mappings["LAN1"].physical_switch_port_index

    -- --- Infer Management VLAN ID ---
    -- Check if 'lan' interface's ifname points to an eth1.VLANID
    local lan_uci_section = nil
    uci:foreach("network", "interface", function(section)
        write_log("Debug: Foreach processing section.type: " .. (section[".type"] or "nil_type") .. ", section['.name']: '" .. (section[".name"] or "nil_name_internal") .. "', section.name: '" .. (section.name or "nil_name_option") .. "'")
        if section[".name"] == "lan" then
            lan_uci_section = section
            write_log("Debug: Found lan_uci_section via foreach (using .name): " .. section[".name"])
            return false -- Stop iteration after finding it
        end
    end)

    write_log("Debug: lan_uci_section result: " .. (lan_uci_section and "found" or "nil"))
    if lan_uci_section then
        local lan_ifname_list_str = uci:get("network", lan_uci_section[".name"], "ifname")
        write_log("Debug: lan_ifname_list_str = '" .. (lan_ifname_list_str or "nil") .. "'")
        if lan_ifname_list_str then
            -- Split the ifname string into individual interfaces and check for eth1.VLANID
            for iface_entry in string.gmatch(lan_ifname_list_str, "[^%s]+") do
                write_log("Debug: Processing iface_entry = '" .. iface_entry .. "'")
                local mgmt_vid_str = string.match(iface_entry, "^" .. lan_physical_ifname_base .. "%.(%d+)$")
                write_log("Debug: string.match result for " .. iface_entry .. " = '" .. (mgmt_vid_str or "nil") .. "'")
                local mgmt_vid = tonumber(mgmt_vid_str)
                write_log("Debug: tonumber result for " .. iface_entry .. " = " .. (mgmt_vid or "nil"))
                if mgmt_vid then
                    result.management_vlan_id = mgmt_vid
                    write_log("Inferred management VLAN ID: " .. mgmt_vid .. " from 'lan' interface ifname: " .. iface_entry)
                    break -- Found it, no need to check further
                end
            end
        else
            write_log("Debug: lan_ifname_list_str is nil or empty.")
        end
    else
        write_log("Debug: lan_uci_section for 'lan' interface not found.")
    end
    write_log("Determined Management VLAN ID: " .. (result.management_vlan_id or "None"))

    -- --- Populate vlan_config_from_switch ---
    local vlan_config_from_switch = {} -- { vid = { tagged = {port_idx, ...}, untagged = {port_idx, ...} } }
    uci:foreach("network", "switch_vlan", function(section)
        local vid = tonumber(section.vlan)
        local ports_str = section.ports or ""
        if vid then
             vlan_config_from_switch[vid] = { tagged = {}, untagged = {} }
             for port_entry in string.gmatch(ports_str, "[^%s]+") do
                 if string.match(port_entry, "t$") then -- Tagged port (e.g., '1t', '0t')
                     table.insert(vlan_config_from_switch[vid].tagged, string.sub(port_entry, 1, -2))
                 else -- Untagged port (eg., '1')
                     table.insert(vlan_config_from_switch[vid].untagged, port_entry)
                 end
             end
        end
    end)
    write_log("VLAN config from switch_vlan: " .. cjson.encode(vlan_config_from_switch))

    -- --- Infer Per-Interface VLANs ---
    for logical_name, iface_details in pairs(interface_mappings) do
        local current_pvid = nil
        local current_tagged_vlans = {}
        local iface_config_found = false
        local is_lan_physical_gui_entry = (iface_details.type == "lan_physical_port")
        local is_wifi_iface = (iface_details.type == "wifi")
        local actual_ifname = iface_details.ifname -- For wireless interfaces
        local uci_wifi_iface_section = iface_details.uci_section_name

        write_log("Inferring VLAN for logical interface: " .. logical_name)

        -- --- Infer for Physical LAN Port (LAN1 GUI entry) ---
        if is_lan_physical_gui_entry then
            -- Infer PVID from switch_vlan for physical port 1
            for vid, config in pairs(vlan_config_from_switch) do
                if config and config.untagged then
                    for _, port_idx in ipairs(config.untagged) do
                        if port_idx == lan_physical_switch_port_index then
                            current_pvid = vid
                            write_log("Inferred PVID " .. current_pvid .. " for " .. logical_name .. " from switch_vlan (untagged).")
                            break
                        end
                    end
                end
                if current_pvid then break end
            end
            
            -- Infer Tagged VLANs from switch_vlan for physical port 1
            for vid, config in pairs(vlan_config_from_switch) do
                if config and config.tagged then
                    for _, port_idx in ipairs(config.tagged) do
                        if port_idx == lan_physical_switch_port_index then
                            if vid ~= current_pvid then -- Exclude PVID from tagged list
                                table.insert(current_tagged_vlans, vid)
                            end
                            break
                        end
                    end
                end
            end
            table.sort(current_tagged_vlans)
            iface_config_found = true
        end

        -- --- Infer for Wireless and Mesh Interfaces ---
        if is_wifi_iface and uci_wifi_iface_section and actual_ifname then
            local network_option = uci:get("wireless", uci_wifi_iface_section, "network")
            if network_option then
                local pvid_from_network = tonumber(string.match(network_option, "^br%-vlan(%d+)$"))
                if pvid_from_network then
                    current_pvid = pvid_from_network
                    write_log("Inferred PVID " .. current_pvid .. " for " .. logical_name .. " from wireless.network option.")
                end
            end

            -- Infer tagged VLANs from bridge memberships (br-vlanXXX)
            uci:foreach("network", "interface", function(section)
                local bridge_name = section.name or ""
                if section.type == "bridge" and string.match(bridge_name, "^br%-vlan(%d+)$") then
                    local vid = tonumber(string.match(bridge_name, "^br%-vlan(%d+)$"))
                    if vid then
                        -- Check if the actual wireless interface is a member of this bridge
                        local bridge_ports = uci:get_list("network", section[".name"], "bridge_ports")
                        for _, port_member in ipairs(bridge_ports) do
                            if port_member == actual_ifname then
                                if vid ~= current_pvid then -- Exclude PVID from tagged list
                                    table.insert(current_tagged_vlans, vid)
                                end
                                break
                            end
                        end
                    end
                end
            end)
            table.sort(current_tagged_vlans)
            iface_config_found = true
        end

        if iface_config_found then
            table.insert(result.interfaces, {
                name = logical_name,
                pvid = current_pvid,
                tagged_vlans = current_tagged_vlans
            })
        else
            write_log("No VLAN configuration found for " .. logical_name .. ". Skipping addition to result.")
        end
    end

    write_log("Final VLAN configuration retrieved: " .. cjson.encode(result))
    io.write(cjson.encode({
        module = "vlan", version = "1.0", errcode = 0, sid = data.sid, result = result
    }))
end

-- 检查是否在CGI环境中运行
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- AC 调用接口
local M = {}

function M.set_config_from_ac(payload)
    write_log("[AC] set_config_from_ac called: " .. cjson.encode(payload))

    -- 调用现有的set_vlan函数
    local data = { params = payload }
    set_vlan(data)

    write_log("[AC] VLAN configuration applied")
    return true, "VLAN configuration applied by AC"
end

function M.get_config_for_ac()
    local config = {
        vlans = get_vlan(),
        interface_mappings = get_interface_mappings()
    }
    write_log("[AC] get_config_for_ac: retrieved VLAN config")
    return config
end

-- 仅在明确作为 CGI 脚本运行时执行
if arg and arg[0] and arg[0]:match("vlan%.lua") and is_cgi() then
    local function run()
        write_log("VLAN API started")
        route_api()
        write_log("VLAN API finished")
    end
    run()
end

return M


