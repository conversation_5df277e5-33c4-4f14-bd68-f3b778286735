#!/usr/bin/lua

-- Copyright (c) 2013 The Linux Foundation. All rights reserved.
-- Not a Contribution.

-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

-- Lua环境：此脚本在OpenWRT的Lua环境中运行，确保有必要的Lua库，如luci.jsonc和luci.sys
-- UCI：这个脚本使用了uci库来直接操作配置文件。这比直接读写文件更为安全和标准。

-- Base64编码：在获取PPPoE配置时，用户名和密码会进行base64编码以满足API格式的要求。

-- 错误处理：脚本中包含了基本的错误处理，可以根据实际需求进行扩展。

-- DNS处理：脚本假设DNS服务器是用空格分隔的，如果有不同的分隔符需要修改处理逻辑。
-- 重启网络：脚本在修改配置后重启了网络服务，这可能导致用户短暂断网。根据实际需求，可能需要考虑更细粒度的服务重启或者使用uci命令来动态应用配置。
-- IPv6：脚本中没有处理wan6接口的配置，因为不需要4G/5G接口，但如需要保留IPv6的配置，可以在modify_network_config函数中添加相关处理。

-- 兼容性：脚本中使用了luci.util.base64encode和luci.util.base64decode来处理base64编码。如果你的环境中没有这些函数，你需要自己实现或导入相应的库。
-- 安全性：确保处理用户输入时，进行了必要的验证和清理，以防止注入攻击
-- 性能：由于脚本每次都需要读取和写入配置文件，对于频繁调用的API，可以考虑优化，例如通过缓存减少IO操作。
-- 权限：确保脚本以必要的权限运行，修改网络配置文件和重启网络服务需要root权限。

local cjson = require("cjson.safe")
local sys = require("luci.sys")
local io = require("io")
local os = require("os")
-- local config_apply_mode = require("config_apply_mode") -- Use the new module

local log_file = "/tmp/qos_management.log"          -- 日志文件路径
local qos_rules_data = "/etc/config/qos_rules.json" -- 持久化 QoS 规则文件
local qos_enabled_file = "/etc/config/qos_enabled.flag" -- 持久化 QoS 启用状态文件
local qos_interface = "eth0"                        -- QoS 控制接口

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end

-- 获取当前应用模式的辅助函数
local function get_current_apply_mode()
    local mode_file = "/etc/config_apply_mode_status"
    local f = io.open(mode_file, "r")
    if f then
        local mode = f:read("*l") -- Read the first line
        f:close()
        mode = mode and mode:gsub("^%s*(.-)%s*$", "%1") -- Trim whitespace
        if mode == "immediate" or mode == "deferred" then
            write_log("Read apply_mode from " .. mode_file .. ": " .. mode)
            return mode
        else
            write_log("Invalid content in " .. mode_file .. ": '" .. (mode or "nil") .. "'. Defaulting to 'immediate'.")
            return "immediate"
        end
    else
        write_log("Could not open " .. mode_file .. ". Defaulting to 'immediate'.")
        return "immediate"
    end
end

-- 读取请求数据
local function read_request_data()
    local request_method = sys.getenv("REQUEST_METHOD")
    write_log("read_request_data - REQUEST_METHOD: " .. (request_method or "nil"))
    if request_method == "POST" then
        local request_body = io.read("*all")
        if request_body and request_body ~= "" then
            write_log("read_request_data - Request Body: " .. request_body)
            local decoded_json = cjson.decode(request_body)
            if decoded_json then
                requestData = decoded_json
                write_log("read_request_data - Decoded JSON Data: " .. cjson.encode(requestData))
            else
                write_log("read_request_data - Failed to decode JSON")
            end
        else
            write_log("read_request_data - No request body or empty")
        end
    else
        write_log("read_request_data - Not a POST request")
    end
end

-- 复制配置文件到临时文件
local function lock_and_copy_config()
    local temp_file = "/tmp/qos_rules_temp.json"
    sys.call("cp " .. qos_rules_data .. " " .. temp_file .. " 2>/dev/null || touch " .. temp_file)
    write_log("Copied config to temp file: " .. (sys.exec("ls -l " .. temp_file) or "unknown"))
    return temp_file
end

-- 保存 QoS 规则（TXT 格式）
local function save_qos_rules(rules)
    write_log("Saving QoS rules to: " .. qos_rules_data)
    local temp_file = "/tmp/qos_rules_temp.txt"
    local file, err = io.open(temp_file, "w")
    if not file then
        write_log("Failed to write temp file " .. temp_file .. ": " .. (err or "unknown error"))
        return false
    end

    -- 用 map 去重
    local rule_map = {}
    for _, rule in ipairs(rules) do
        rule_map[rule.num] = rule
    end

    for _, rule in ipairs(rules) do
        file:write("rule " .. rule.num .. "\n")
        file:write("\tenabled " .. (rule.enabled and "1" or "0") .. "\n")
        file:write("\tstart_ip " .. (rule.start_ip or "") .. "\n")
        file:write("\tend_ip " .. (rule.end_ip or "") .. "\n")
        file:write("\tqos_mode " .. (rule.qos_mode or "IP-based speed limit") .. "\n")
        file:write("\tlimit_rate " .. (rule.limit_rate or "0") .. "\n")
        file:write("\tmax_limit_rate " .. (rule.max_limit_rate or "0") .. "\n")
    end
    file:flush()
    file:close()
    write_log("Successfully wrote to temp file: " .. temp_file)

    local cp_result = sys.call("cp " .. temp_file .. " " .. qos_rules_data)
    sys.call("rm " .. temp_file)
    sys.call("sync")
    if cp_result == 0 then
        write_log("Successfully updated " .. qos_rules_data)
        return true
    else
        write_log("Failed to copy temp file to " .. qos_rules_data .. ", cp result: " .. cp_result)
        return false
    end
end

-- 加载 QoS 规则（TXT 格式）
-- 加载 QoS 规则（TXT 格式）
-- 加载 QoS 规则（TXT 格式）
local function load_qos_rules()
    write_log("Loading QoS rules from: " .. qos_rules_data)
    local temp_file = lock_and_copy_config()
    local rules = {}
    local success, file = pcall(io.open, temp_file, "r")
    if not success or not file then
        write_log("Warning: Failed to read " .. temp_file .. " - " .. (file or "unknown error") .. ", assuming empty rules")
        sys.call("rm " .. temp_file)
        return {}
    end

    local current_rule = nil
    for line in file:lines() do
        write_log("Parsing line: " .. line)
        if line:match("^rule%s+(.+)$") then
            if current_rule then
                table.insert(rules, current_rule)
            end
            current_rule = { num = line:match("^rule%s+(.+)$") }
            write_log("New rule started: num=" .. current_rule.num)
        elseif current_rule then
            local key, value = line:match("^%s*([%w_]+)%s+(.+)$")  -- 支持下划线
            if key then
                if key == "enabled" then
                    current_rule.enabled = (value == "1")
                else
                    current_rule[key] = value
                end
                write_log("Parsed key=" .. key .. ", value=" .. (value or "nil"))
            else
                write_log("Failed to parse line: " .. line)
            end
        end
    end
    if current_rule then
        table.insert(rules, current_rule)
    end
    file:close()
    sys.call("rm " .. temp_file)
    write_log("Loaded " .. #rules .. " rules")
    for i, rule in ipairs(rules) do
        write_log("Rule " .. i .. ": " .. cjson.encode(rule))
    end
    return rules
end

-- 生成规则编号
local function generate_rule_num()
    local rules = load_qos_rules()
    local max_num = 0
    for _, rule in ipairs(rules) do
        local num = tonumber(rule.num) or 0
        if num > max_num then
            max_num = num
        end
    end
    return tostring(max_num + 1)
end

-- 应用 QoS 规则（根据每条规则的 enabled 下发）
local function apply_qos_rules(rules)
    write_log("Applying QoS rules...")

    -- 下行接口固定为 br-lan，WAN 接口为 wan
    local DN_DEV = "br-lan"
    local WAN_DEV = "eth1.2" -- 根据实际 WAN 接口名

    -- 检测所有 wifi 接口
    local wifi_interfaces = {"wifi0", "wifi1"} -- 可扩展
    local up_interfaces = {}
    for _, wifi_dev in ipairs(wifi_interfaces) do
        local check_cmd = string.format("ifconfig %s >/dev/null 2>&1", wifi_dev)
        local status = os.execute(check_cmd)
        if status == 0 then
            table.insert(up_interfaces, { dev = wifi_dev, direction = "up" })
            write_log("Found wifi interface: " .. wifi_dev)
        else
            write_log("Wifi interface not found: " .. wifi_dev)
        end
    end

    if #up_interfaces == 0 then
        write_log("No wifi interfaces found, QoS rules will only apply to br-lan")
        up_interfaces = {{ dev = "wifi0", direction = "up" }} -- 回退默认值
    end

    -- 添加接口：WiFi (up), br-lan (down), wan (up/down)
    local interfaces = up_interfaces
    table.insert(interfaces, { dev = DN_DEV, direction = "down" })
    table.insert(interfaces, { dev = WAN_DEV, direction = "up" })   -- 出 WAN 流量
    table.insert(interfaces, { dev = WAN_DEV, direction = "down" }) -- 入 WAN 流量
    write_log("Using interfaces: " .. cjson.encode(interfaces))

    -- 禁用硬件加速
    local apply_mode = get_current_apply_mode()
    write_log("Current apply mode: " .. apply_mode)

    if apply_mode == "immediate" then
        write_log("Disabling hardware flow offloading and restarting firewall immediately...")
        sys.exec("uci set firewall.@globals[0].flow_offloading=0")
        sys.exec("uci set firewall.@globals[0].flow_offloading_hw=0")
        sys.exec("uci commit firewall")
        sys.exec("/etc/init.d/firewall restart >/dev/null 2>&1")
        write_log("Hardware flow offloading disabled and firewall restarted.")

        -- 清理旧规则
        for _, iface in ipairs(interfaces) do
            write_log("Clearing tc qdisc on " .. iface.dev)
            sys.exec("tc qdisc del dev " .. iface.dev .. " root 2>/dev/null")
            sys.exec("tc filter del dev " .. iface.dev .. " parent 1: 2>/dev/null")
            sys.exec("tc class del dev " .. iface.dev .. " parent 1: 2>/dev/null")
        end
        write_log("Clearing iptables mangle chain")
        sys.exec("iptables -t mangle -D FORWARD -j QOS_IP 2>/dev/null")
        sys.exec("iptables -t mangle -D POSTROUTING -j QOS_IP 2>/dev/null")
        sys.exec("iptables -t mangle -D OUTPUT -j QOS_IP 2>/dev/null")
        sys.exec("iptables -t mangle -F QOS_IP 2>/dev/null")
        sys.exec("iptables -t mangle -X QOS_IP 2>/dev/null")

        -- 创建 iptables 链
        sys.exec("iptables -t mangle -N QOS_IP 2>/dev/null")
        sys.exec("iptables -t mangle -A PREROUTING -j QOS_IP 2>/dev/null")  -- 添加 PREROUTING 链
        sys.exec("iptables -t mangle -A FORWARD -j QOS_IP 2>/dev/null")
        sys.exec("iptables -t mangle -A POSTROUTING -j QOS_IP 2>/dev/null")
        sys.exec("iptables -t mangle -A OUTPUT -j QOS_IP 2>/dev/null")
        write_log("Created iptables QOS_IP chain")

        local success = true

        -- 初始化 tc 规则
        for _, iface in ipairs(interfaces) do
            local check_cmd = string.format("ifconfig %s >/dev/null 2>&1", iface.dev)
            local status = os.execute(check_cmd)
            if status ~= 0 then
                write_log("Warning: Interface " .. iface.dev .. " does not exist, skipping")
            else
                -- 添加根 qdisc
                local root_cmd = string.format("tc qdisc add dev %s root handle 1: htb default 999", iface.dev)
                write_log("Executing root qdisc: [" .. root_cmd .. "]")
                local root_result = sys.exec(root_cmd .. " 2>&1")
                if root_result:match("error") then
                    write_log("Failed to execute root qdisc: " .. root_cmd .. " - Error: " .. root_result)
                end

                -- 添加根 class
            local root_class_cmd = string.format("tc class add dev %s parent 1: classid 1:1 htb rate 1000Mbit burst 15k quantum 60000", iface.dev)
                write_log("Executing root class: [" .. root_class_cmd .. "]")
                local root_class_result = sys.exec(root_class_cmd .. " 2>&1")
                if root_class_result:match("error") then
                    write_log("Failed to execute root class: " .. root_class_cmd .. " - Error: " .. root_class_result)
                end

                -- 添加默认类
                local default_class_cmd = string.format("tc class add dev %s parent 1:1 classid 1:999 htb rate 1000Mbit ceil 1000Mbit burst 15k quantum 60000", iface.dev)
                write_log("Executing default class: [" .. default_class_cmd .. "]")
                local default_class_result = sys.exec(default_class_cmd .. " 2>&1")
                if default_class_result:match("error") then
                    write_log("Failed to execute default class: " .. default_class_cmd .. " - Error: " .. default_class_result)
                end

                -- 为默认类添加 SFQ 队列
                local default_sfq_cmd = string.format("tc qdisc add dev %s parent 1:999 handle 999: sfq perturb 10", iface.dev)
                write_log("Executing default sfq: [" .. default_sfq_cmd .. "]")
                local default_sfq_result = sys.exec(default_sfq_cmd .. " 2>&1")
                if default_sfq_result:match("error") then
                    write_log("Failed to execute default sfq: " .. default_sfq_cmd .. " - Error: " .. default_sfq_result)
                end
            end
        end

        -- 应用每条规则
        for i, rule in ipairs(rules) do
            if rule.enabled then
                write_log("Applying rule: " .. rule.num)
                local start_ip = rule.start_ip or "*************"
                local end_ip = rule.end_ip or "*************"
                local limit_rate_mbps = tonumber(rule.limit_rate or 10000) / 1000 -- 默认 10Mbps
                local max_rate_mbps = tonumber(rule.max_limit_rate or 20000) / 1000 -- 默认 20Mbps
                local mark = tonumber(rule.num)
                local class_id = mark + 100 -- 使用 num 作为 classid
                local handle_id = mark + 1000 -- 避免与根 handle 冲突

                for _, iface in ipairs(interfaces) do
                    local prio = (iface.direction == "up") and 1 or 2
                    local rate = (limit_rate_mbps > 0) and limit_rate_mbps or 1
                    local ceil = (max_rate_mbps > 0 and max_rate_mbps > rate) and max_rate_mbps or rate

                    local check_cmd = string.format("ifconfig %s >/dev/null 2>&1", iface.dev)
                    local status = os.execute(check_cmd)
                    if status ~= 0 then
                        write_log("Skipping tc setup for missing interface: " .. iface.dev)
                        success = false
                    else
                        -- 创建子分类
                        local class_cmd = string.format("tc class add dev %s parent 1:1 classid 1:%d htb rate %.2fMbit ceil %.2fMbit burst 15k prio %d quantum 1514",
                            iface.dev, class_id, rate, ceil, prio)
                        write_log("Executing tc class: [" .. class_cmd .. "]")
                        local class_result = sys.exec(class_cmd .. " 2>&1")
                        if class_result:match("error") then
                            write_log("Failed to execute tc class: " .. class_cmd .. " - Error: " .. class_result)
                            success = false
                        end

                        -- 创建过滤器
                        local filter_cmd = string.format("tc filter add dev %s protocol ip parent 1: prio %d handle %d fw flowid 1:%d",
                            iface.dev, prio, mark, class_id)
                        write_log("Executing tc filter: [" .. filter_cmd .. "]")
                        local filter_result = sys.exec(filter_cmd .. " 2>&1")
                        if filter_result:match("error") then
                            write_log("Failed to execute tc filter: " .. filter_cmd .. " - Error: " .. filter_result)
                            success = false
                        end

                        -- 创建 SFQ 队列
                        local sfq_cmd = string.format("tc qdisc add dev %s parent 1:%d handle %d: sfq perturb 10",
                            iface.dev, class_id, handle_id)
                        write_log("Executing tc sfq: [" .. sfq_cmd .. "]")
                        local sfq_result = sys.exec(sfq_cmd .. " 2>&1")
                        if sfq_result:match("error") then
                            write_log("Failed to execute tc sfq: " .. sfq_cmd .. " - Error: " .. sfq_result)
                            success = false
                        end
                    end
                end

                -- 设置 iptables 标记
                if start_ip == end_ip then
                    sys.exec(string.format("iptables -t mangle -A QOS_IP -s %s/32 -j MARK --set-mark %d 2>/dev/null", start_ip, mark))
                    sys.exec(string.format("iptables -t mangle -A QOS_IP -d %s/32 -j MARK --set-mark %d 2>/dev/null", start_ip, mark))
                else
                    sys.exec(string.format("iptables -t mangle -A QOS_IP -m iprange --src-range %s-%s -j MARK --set-mark %d 2>/dev/null", start_ip, end_ip, mark))
                    sys.exec(string.format("iptables -t mangle -A QOS_IP -m iprange --dst-range %s-%s -j MARK --set-mark %d 2>/dev/null", start_ip, end_ip, mark))
                end
                write_log("Set iptables mark for IP range " .. start_ip .. "-" .. end_ip .. " with mark " .. mark)
            else
                write_log("Skipping disabled rule: " .. rule.num)
            end
        end
    else
        write_log("Apply mode is deferred. QoS rules will not be applied automatically.")
    end

    if success then
        write_log("QoS rules applied successfully")
    else
        write_log("QoS rules applied with errors")
    end
    return success
end

-- 设置 QoS 启用状态
local function set_qos_enabled(enabled)
    if enabled then
        local file = io.open(qos_enabled_file, "w")
        if file then
            file:close()
        else
            write_log("Error: Failed to create QoS enabled flag")
            return false
        end
    else
        os.remove(qos_enabled_file)
    end
    return true
end

-- API: 获取 QoS 规则
-- API: 获取 QoS 规则
local function api_get_qos_rules(data)
    write_log("Starting api_get_qos_rules")
    local rules = load_qos_rules()
    local response_rules = {}
    for _, rule in ipairs(rules) do
        local response_rule = {
            num = rule.num,
            enabled = rule.enabled,
            qos_mode = rule.qos_mode or "IP-based speed limit",
            start_ip = rule.start_ip,
            end_ip = rule.end_ip,
            limit_rate = rule.limit_rate,
            max_limit_rate = rule.max_limit_rate
        }
        table.insert(response_rules, response_rule)
        write_log("Adding rule to response: " .. cjson.encode(response_rule))
    end
    local response = { errcode = 0, result = response_rules }
    write_log("Returning response: " .. cjson.encode(response))
    return response
end

-- API: 设置 QoS 规则
local function api_set_qos_rule(data)
    local param = data.param or {}
    local num = param.num
    local start_ip = param.start_ip
    local end_ip = param.end_ip
    local limit_rate = param.limit_rate
    local max_limit_rate = param.max_limit_rate
    local enabled = param.enabled

    write_log("Starting api_set_qos_rule")
    local rules = load_qos_rules()
    write_log("Loaded " .. #rules .. " rules")

    local rule_index = nil
    if num then
        for i, rule in ipairs(rules) do
            if rule.num == num then
                rule_index = i
                break
            end
        end
    end

    if not num or not rule_index then
        num = generate_rule_num()
        write_log("Generated new rule num: " .. num)
    end

    local new_rule = {
        num = num,
        enabled = enabled,
        start_ip = start_ip,
        end_ip = end_ip,
        qos_mode = "IP-based speed limit",
        limit_rate = limit_rate,
        max_limit_rate = max_limit_rate
    }
    write_log("New rule created: " .. cjson.encode(new_rule))

    if rule_index then
        rules[rule_index] = new_rule
        write_log("Updated rule at index: " .. rule_index)
    else
        table.insert(rules, new_rule)
        write_log("Added new rule")
    end

    local save_success = save_qos_rules(rules)
    write_log("Save QoS rules result: " .. tostring(save_success))
    if not save_success then
        write_log("Failed to save QoS rules")
        return { errcode = 8, message = "Failed to set QoS rule - save failed" }
    end

    local apply_success = apply_qos_rules(rules)
    write_log("Apply QoS rules result: " .. tostring(apply_success))

    local apply_mode = get_current_apply_mode()
    local message_suffix = ""
    if apply_mode == "deferred" then
        message_suffix = ". Apply deferred."
    end

    local response = { errcode = 0, result = { num = num, message = "QoS rule " .. (rule_index and "updated" or "added") .. message_suffix } }
    write_log("Returning response: " .. cjson.encode(response))
    return response
end

-- API: 删除 QoS 规则
local function api_delete_qos_rule(data)
    local param = data.param or {}
    local nums = param.nums or {}
    
    write_log("Starting api_delete_qos_rule with nums: " .. cjson.encode(nums))
    local rules = load_qos_rules()
    local updated_rules = {}
    local deleted_count = 0

    for _, rule in ipairs(rules) do
        local should_delete = false
        for _, num in ipairs(nums) do
            if rule.num == num then
                should_delete = true
                deleted_count = deleted_count + 1
                write_log("Marking rule " .. num .. " for deletion")
                break
            end
        end
        if not should_delete then
            table.insert(updated_rules, rule)
        end
    end

    if deleted_count == 0 then
        write_log("No rules found to delete")
        return { errcode = 6, message = "No rules found to delete" }
    end

    local save_success = save_qos_rules(updated_rules)
    write_log("Save QoS rules result: " .. tostring(save_success))
    if not save_success then
        write_log("Failed to save QoS rules")
        return { errcode = 8, message = "Failed to delete QoS rules - save failed" }
    end

    local apply_success = apply_qos_rules(updated_rules)
    write_log("Apply QoS rules result: " .. tostring(apply_success))
    
    local apply_mode = get_current_apply_mode()
    local message_suffix = ""
    if apply_mode == "deferred" then
        message_suffix = ". Apply deferred."
    end

    local response = { errcode = 0, result = { message = deleted_count .. " QoS rule(s) deleted" .. message_suffix } }
    write_log("Returning response: " .. cjson.encode(response))
    return response
end


-- API: 编辑 QoS 规则
local function api_edit_qos_rule(data)
    local param = data.param or {}
    local num = param.num
    local enabled = param.enabled
    local start_ip = param.start_ip
    local end_ip = param.end_ip
    local limit_rate = param.limit_rate
    local max_limit_rate = param.max_limit_rate

    write_log("Starting api_edit_qos_rule")
    if not num then
        write_log("Error: Rule number (num) is required")
        return { errcode = 5, message = "Rule number (num) is required" }
    end

    local rules = load_qos_rules()
    local rule_index = nil
    for i, rule in ipairs(rules) do
        if rule.num == num then
            rule_index = i
            break
        end
    end

    if not rule_index then
        write_log("Error: Rule " .. num .. " not found")
        return { errcode = 6, message = "Rule " .. num .. " not found" }
    end

    local existing_rule = rules[rule_index]
    local was_enabled = existing_rule.enabled

    if enabled ~= nil then existing_rule.enabled = enabled end
    if start_ip then existing_rule.start_ip = start_ip end
    if end_ip then existing_rule.end_ip = end_ip end
    if limit_rate then existing_rule.limit_rate = limit_rate end
    if max_limit_rate then existing_rule.max_limit_rate = max_limit_rate end

    local save_success = save_qos_rules(rules)
    write_log("Save QoS rules result: " .. tostring(save_success))
    if not save_success then
        write_log("Failed to save QoS rules")
        return { errcode = 8, message = "Failed to edit QoS rule - save failed" }
    end

    local apply_success = apply_qos_rules(rules)
    write_log("Apply QoS rules result: " .. tostring(apply_success))
    
    local apply_mode = get_current_apply_mode()
    local message_suffix = ""
    if apply_mode == "deferred" then
        message_suffix = ". Apply deferred."
    end

    if was_enabled and not enabled then
        write_log("Rule " .. num .. " disabled, limit disabled but rule preserved")
    elseif not was_enabled and enabled then
        write_log("Rule " .. num .. " enabled, limit applied")
    end

    local response = { errcode = 0, result = { num = num, message = "QoS rule " .. num .. " updated" .. message_suffix } }
    write_log("Returning response: " .. cjson.encode(response))
    return response
end


-- API: 查询 QoS 配置和状态
local function api_query_qos_status(data)
    write_log("Starting api_query_qos_status")
    local result = {
        qos_rules = {},
        tc_config = {},
        nft_rules = {}
    }

    -- 1. 获取QoS规则配置
    local rules = load_qos_rules()
    for _, rule in ipairs(rules) do
        table.insert(result.qos_rules, {
            num = rule.num,
            enabled = rule.enabled,
            qos_mode = rule.qos_mode or "IP-based speed limit",
            start_ip = rule.start_ip,
            end_ip = rule.end_ip,
            limit_rate = rule.limit_rate,
            max_limit_rate = rule.max_limit_rate
        })
    end

    -- 2. 获取tc配置
    local interfaces = {"br-lan", "eth1"}
    for _, iface in ipairs(interfaces) do
        local tc_info = {
            interface = iface,
            qdisc = sys.exec("tc qdisc show dev " .. iface),
            class = sys.exec("tc class show dev " .. iface),
            filter = sys.exec("tc filter show dev " .. iface)
        }
        table.insert(result.tc_config, tc_info)
    end

    -- 3. 获取nft规则
    result.nft_rules = sys.exec("nft list table inet qos")

    local response = { errcode = 0, result = result }
    write_log("Returning response: " .. cjson.encode(response))
    return response
end

-- API: 设置 QoS 启用状态
local function api_set_qos_enabled(data)
    local param = data.param or {}
    local enabled = param.enabled

    write_log("Starting api_set_qos_enabled")
    local apply_mode = get_current_apply_mode()
    write_log("Current apply mode: " .. apply_mode)

    if enabled then
        local file = io.open(qos_enabled_file, "w")
        if file then
            file:close()
            if apply_mode == "immediate" then
                sys.exec("uci set firewall.@globals[0].qos_enabled='1'")
                sys.exec("uci commit firewall")
                sys.exec("/etc/init.d/firewall restart") -- Restart firewall if QoS is enabled immediately
                response_message = "QoS enabled and firewall restarting."
            else
                response_message = "QoS enabled. Apply deferred."
            end
        else
            write_log("Error: Failed to create QoS enabled flag")
            response_message = "Error: Failed to enable QoS."
            return { errcode = 1, message = response_message }
        end
    else
        os.remove(qos_enabled_file)
        if apply_mode == "immediate" then
            sys.exec("uci set firewall.@globals[0].qos_enabled='0'")
            sys.exec("uci commit firewall")
            sys.exec("/etc/init.d/firewall restart") -- Restart firewall if QoS is disabled immediately
            response_message = "QoS disabled and firewall restarting."
        else
            response_message = "QoS disabled. Apply deferred."
        end
    end
    write_log("QoS status updated: " .. tostring(enabled))
    return { errcode = 0, message = response_message }
end

-- API 路由
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 读取请求数据
    read_request_data()

    if not requestData then
        local error_response = { errcode = 2, message = "Failed to read request data" }
        write_log("Writing error response: " .. cjson.encode(error_response))
        io.write(cjson.encode(error_response))
        return
    end

    local api = requestData.api
    write_log("Routing API: " .. (api or "nil"))
    local response
    if api == "get_qos_rules" then
        response = api_get_qos_rules(requestData)
    elseif api == "set_qos_rule" then
        response = api_set_qos_rule(requestData)
    elseif api == "delete_qos_rule" then
        response = api_delete_qos_rule(requestData)
    elseif api == "edit_qos_rule" then
        response = api_edit_qos_rule(requestData)
    elseif api == "query_qos_status" then
        response = api_query_qos_status(requestData)
    elseif api == "set_qos_enabled" then
        response = api_set_qos_enabled(requestData)
    else
        response = { errcode = 4, message = "Unknown API: " .. (api or "nil") }
    end
    write_log("Writing response: " .. cjson.encode(response))
    io.write(cjson.encode(response))
    io.flush()  -- 确保输出立即发送
end

-- 检查是否在CGI环境中运行
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- AC 调用接口
local M = {}

function M.set_config_from_ac(payload)
    write_log("[AC] set_config_from_ac called: " .. cjson.encode(payload))

    -- 调用现有的set_qos函数
    local data = { param = payload }
    set_qos(data)

    write_log("[AC] QoS configuration applied")
    return true, "QoS configuration applied by AC"
end

function M.get_config_for_ac()
    local config = {
        enabled = is_qos_enabled(),
        rules = read_qos_rules()
    }
    write_log("[AC] get_config_for_ac: retrieved QoS config")
    return config
end

-- 仅在明确作为 CGI 脚本运行时执行
if arg and arg[0] and arg[0]:match("qos_management%.lua") and is_cgi() then
    local function run()
        write_log("QoS Management API started")
        read_request_data()
        route_api()
        write_log("QoS Management API finished")
    end
    run()
end

return M