#!/bin/sh

echo "=== 测试VLAN管理IP地址保持功能 ==="

# 测试1: 设置管理VLAN为10，检查IP地址配置
echo "1. 设置管理VLAN为10:"
echo '{
    "module": "vlan",
    "api": "set",
    "param": {
        "management_vlan_id": 10,
        "interfaces": [
            {
                "name": "LAN1",
                "pvid": 10,
                "tagged_vlans": []
            }
        ]
    }
}' | lua vlan.lua

echo ""
echo "检查网络接口配置:"
uci show network | grep -E "(lan|mgmt_vlan)"
echo ""

# 测试2: 检查是否可以通过原IP访问
echo "2. 检查原IP地址是否保持:"
ip addr show br-lan | grep "*************" && echo "✓ 原IP地址保持" || echo "✗ 原IP地址丢失"
echo ""

# 测试3: 检查管理VLAN专用IP是否创建
echo "3. 检查管理VLAN专用IP:"
ip addr show | grep "**************" && echo "✓ 管理VLAN IP创建成功" || echo "✗ 管理VLAN IP未创建"
echo ""

# 测试4: 检查eth1.2是否存在
echo "4. 检查eth1.2接口:"
ip link show eth1.2 && echo "✓ eth1.2接口存在" || echo "✗ eth1.2接口不存在"
echo ""

# 测试5: 清除管理VLAN，检查IP保持
echo "5. 清除管理VLAN:"
echo '{
    "module": "vlan",
    "api": "set", 
    "param": {
        "interfaces": [
            {
                "name": "LAN1",
                "pvid": 1,
                "tagged_vlans": []
            }
        ]
    }
}' | lua vlan.lua

echo ""
echo "清除后检查网络接口配置:"
uci show network | grep -E "(lan|mgmt_vlan)"
echo ""

# 测试6: 验证原IP是否仍然保持
echo "6. 验证原IP地址是否仍然保持:"
ip addr show br-lan | grep "*************" && echo "✓ 原IP地址保持" || echo "✗ 原IP地址丢失"

echo ""
echo "=== 测试完成 ==="
