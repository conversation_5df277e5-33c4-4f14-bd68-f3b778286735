#!/usr/bin/lua

local cjson = require("cjson.safe")  -- 使用 safe 版本，避免抛出错误

-- 日志写入函数
local function write_log(message)
    local file = io.open("/tmp/status.log", "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] ") .. message .. "\n")
        file:close()
    else
        print("Failed to open log file")
    end
end



-- 调用获取状态信息的函数
function get_cpu_mem_info()

    local cpu_info = { usage = 0 }

    local mem_info = { total = 0, used = 0, free = 0, usage = 0 }

    -- 读取 CPU 信息
    local file = io.popen("top -bn1 | grep 'CPU:'")
    local cpu_line = file:read("*all")
    file:close()
    
    if cpu_line then
        local user, sys, idle = cpu_line:match("(%d+)%% usr%s+(%d+)%% sys%s+%d+%% nic%s+(%d+)%% idle")
        if user and sys and idle then
            cpu_info.usage = tonumber(user) + tonumber(sys)
        end
    end

    -- 读取内存信息
    file = io.popen("top -bn1 | grep 'Mem:'")
    local mem_line = file:read("*all")
    file:close()

    if mem_line then
        local used, free, total = mem_line:match("Mem:%s+(%d+)K used,%s+(%d+)K free")
        if used and free then
            mem_info.used = tonumber(used)
            mem_info.free = tonumber(free)
            mem_info.total = mem_info.used + mem_info.free
            mem_info.usage = math.floor((mem_info.used / mem_info.total) * 100)
        end
    end

    return cpu_info, mem_info
end

-- 读取文件内容
local function read_file(path)
    local file = io.open(path, "r")
    if not file then return nil end
    local content = file:read("*all")
    file:close()
    return content
end

function get_device_info()
    local device_info = { devicetype = "IAP3500-E11", devicename = "wireless device", mac = "-", mode = "ap" }

    -- 获取设备别名
    local file = io.popen("cat /etc/config/system | grep 'option alias'")
    local devicename = file:read("*l")
    file:close()
    if devicename then
        device_info.devicename = devicename:match("option alias '(.-)'") or "Unknown"
    end

    -- 获取 MAC 地址
    file = io.popen("cat /sys/class/net/br-lan/address")
    local mac = file:read("*l")
    file:close()
    if mac then
        device_info.mac = mac
    end

    -- 获取设备运行模式
    local uci = require("luci.model.uci").cursor() -- 引入 UCI
    file = io.popen("cat /etc/config/system | grep 'option mode'")
    local mode = file:read("*l")
    file:close()
    if mode then
        device_info.mode = mode:match("option mode '(.-)'") or "Unknown"
        -- 如果是桥接模式，检查 bridge_mode 或 STA 配置
        if device_info.mode == "bridge" then
            local system_config = io.popen("cat /etc/config/system"):read("*all")
            local bridge_mode = system_config:match("option bridge_mode '(%w+)'")
            if bridge_mode then
                device_info.mode = bridge_mode
            else
                -- 回退到检查 STA 配置
                local sta_wds = uci:get("wireless", "sta0", "wds") or
                                uci:get("wireless", "sta1", "wds") or
                                uci:get("wireless", "sta3", "wds")
                device_info.mode = (sta_wds == "1") and "wds" or "universal"
            end
        end
    end

    -- 获取固件版本信息
    file = io.popen("cat /etc/os-release | grep 'BUILD_ID='")
    local build_id_info = file:read("*l")
    file:close()

    local build_id = "-"
    if build_id_info then
        build_id = build_id_info:match("BUILD_ID=\"(.-)\"") or "-"
    end

    -- 直接从 /etc/openwrt_build 获取完整的固件版本
    local firmware_version = read_file("/etc/openwrt_build") or "-"
    device_info.firmware_version = firmware_version:gsub("%s+", "")  -- 去除空白字符

    write_log("get_device_info: mode=" .. device_info.mode)
    return device_info
end


-- 获取运行时间
local function get_system_runtime()
    -- 使用 io.popen 执行命令并读取输出
    local file = io.popen("cat /proc/uptime")
    local uptime = file:read("*l")  -- 读取一行
    file:close()

    local total_seconds = uptime and tonumber(uptime:match("(%d+).%d+")) or 0
    local hours = math.floor(total_seconds / 3600)
    local minutes = math.floor((total_seconds % 3600) / 60)
    local seconds = total_seconds % 60

    return string.format("%d hours, %d minutes, %d seconds", hours, minutes, seconds)
end

-- 获取系统时间
local function get_system_time()
    return os.date("%Y-%m-%d %H:%M:%S")
end

-- 获取请求的功能
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 获取 POST 数据长度
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""

    -- 读取 POST 数据
    if POSTLength > 0 then
        POST = io.read(POSTLength)
        write_log("Received POST data: " .. (POST or "nil"))
    else
        write_log("No POST data received or CONTENT_LENGTH is 0")
    end

    -- 确保读取成功
    if not POST or POST == "" then
        local error_message = "Failed to retrieve POST data"
        write_log(error_message)
        io.write(cjson.encode({
            module = "status",
            version = "1.0",
            errcode = 1,
            result = { message = error_message }
        }))
        return
    end

    -- 解析 POST 数据为 JSON
    local requestData = cjson.decode(POST)
    if not requestData then
        local error_message = "Invalid JSON input"
        write_log(error_message)
        io.write(cjson.encode({
            module = "status",
            version = "1.0",
            errcode = 2,
            result = { message = error_message }
        }))
        return
    end

    if requestData.api == "get" then
        -- 获取设备信息
        local cpu_info, mem_info = get_cpu_mem_info()
        local device_info = get_device_info()
        local system_runtime = get_system_runtime()
        local system_time = get_system_time()  -- 获取系统时间

        -- 组装返回结果
        local result = {
            system_source = {
                cpu_usage = cpu_info.usage,
                mem_total = mem_info.total,
                mem_used = mem_info.used,
                mem_free = mem_info.free,
                mem_usage = mem_info.usage
            },
            device_info = device_info,
            system_runtime = system_runtime,
            system_time = system_time  -- 添加系统时间
        }

        -- 返回 JSON 数据
        io.write(cjson.encode({
            module = "status",
            version = "1.0",
            api = "get",
            errcode = 0,
            sid = requestData.sid,
            result = result
        }))
    else
        -- 未知 API 错误
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "status",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
    end
end

-- 入口函数
local function run()
    write_log("Status API started")
    route_api()
    write_log("Status API finished")
end

run()
