#!/usr/bin/lua

--[[
WiFi7 Mesh Status Monitor Module
基于WiFi6 son_topo实现，为WiFi7设备提供mesh状态监控功能
参考: package/son_topo/src/son_topo.c
作者: WiFi7开发团队
日期: 2025-07-07
]]

local uci = require("uci")
local json = require("json")

-- 引入mesh相关模块
local mesh_config = require("mesh_config")
local mesh_service = require("mesh_service")

local mesh_monitor = {}

-- 检测是否为CGI环境
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- 日志函数
local function log_message(level, message)
    if is_cgi() then
        os.execute(string.format("logger -t mesh_monitor '[%s] %s'", level, message))
    else
        print(string.format("[%s] %s", level, message))
    end
end

-- 执行系统命令并返回结果
local function execute_command(cmd)
    local handle = io.popen(cmd .. " 2>&1")
    local result = handle:read("*a")
    local success = handle:close()
    return success, result
end

-- 获取系统运行时间
local function get_uptime()
    local uptime_file = io.open("/proc/uptime", "r")
    if uptime_file then
        local uptime_str = uptime_file:read("*line")
        uptime_file:close()
        local uptime = tonumber(string.match(uptime_str, "([%d%.]+)"))
        return uptime or 0
    end
    return 0
end

-- 获取内存使用情况
local function get_memory_info()
    local meminfo = {}
    local meminfo_file = io.open("/proc/meminfo", "r")

    if meminfo_file then
        for line in meminfo_file:lines() do
            local key, value = string.match(line, "([%w_]+):%s*(%d+)")
            if key and value then
                meminfo[key] = tonumber(value)
            end
        end
        meminfo_file:close()
    end

    return {
        total = meminfo.MemTotal or 0,
        free = meminfo.MemFree or 0,
        available = meminfo.MemAvailable or meminfo.MemFree or 0,
        used = (meminfo.MemTotal or 0) - (meminfo.MemFree or 0)
    }
end

-- 获取CPU使用率
local function get_cpu_usage()
    local cpu_info = {}

    -- 读取/proc/stat获取CPU信息
    local stat_file = io.open("/proc/stat", "r")
    if stat_file then
        local cpu_line = stat_file:read("*line")
        stat_file:close()

        if cpu_line then
            local user, nice, system, idle = string.match(cpu_line, "cpu%s+(%d+)%s+(%d+)%s+(%d+)%s+(%d+)")
            if user and nice and system and idle then
                local total = tonumber(user) + tonumber(nice) + tonumber(system) + tonumber(idle)
                local used = tonumber(user) + tonumber(nice) + tonumber(system)
                cpu_info.usage = total > 0 and math.floor((used / total) * 100) or 0
            end
        end
    end

    return cpu_info.usage or 0
end

-- 获取无线接口统计信息
function mesh_monitor.get_wireless_stats()
    local stats = {}
    local interfaces = {"wlan0", "wlan8", "radio0", "radio1"}

    for _, iface in ipairs(interfaces) do
        local iface_stats = {
            tx_packets = 0,
            rx_packets = 0,
            tx_bytes = 0,
            rx_bytes = 0,
            tx_errors = 0,
            rx_errors = 0,
            signal_level = -100,
            noise_level = -100,
            channel = 0,
            frequency = 0
        }

        -- 从/proc/net/dev获取网络统计
        local dev_file = io.open("/proc/net/dev", "r")
        if dev_file then
            for line in dev_file:lines() do
                if string.find(line, iface .. ":") then
                    local rx_bytes, rx_packets, rx_errs, _, _, _, _, _, tx_bytes, tx_packets, tx_errs =
                        string.match(line, "%s*" .. iface .. ":%s*(%d+)%s+(%d+)%s+(%d+)%s+%d+%s+%d+%s+%d+%s+%d+%s+%d+%s+(%d+)%s+(%d+)%s+(%d+)")

                    if rx_bytes then
                        iface_stats.rx_bytes = tonumber(rx_bytes) or 0
                        iface_stats.rx_packets = tonumber(rx_packets) or 0
                        iface_stats.rx_errors = tonumber(rx_errs) or 0
                        iface_stats.tx_bytes = tonumber(tx_bytes) or 0
                        iface_stats.tx_packets = tonumber(tx_packets) or 0
                        iface_stats.tx_errors = tonumber(tx_errs) or 0
                    end
                    break
                end
            end
            dev_file:close()
        end

        -- 获取无线信号信息
        if string.find(iface, "wlan") then
            local iwconfig_cmd = string.format("iwconfig %s 2>/dev/null", iface)
            local success, iwconfig_result = execute_command(iwconfig_cmd)

            if success and iwconfig_result then
                local frequency = string.match(iwconfig_result, "Frequency:([%d%.]+)")
                local channel = string.match(iwconfig_result, "Channel (%d+)")
                local signal = string.match(iwconfig_result, "Signal level=([-%d]+)")
                local noise = string.match(iwconfig_result, "Noise level=([-%d]+)")

                iface_stats.frequency = tonumber(frequency) or 0
                iface_stats.channel = tonumber(channel) or 0
                iface_stats.signal_level = tonumber(signal) or -100
                iface_stats.noise_level = tonumber(noise) or -100
            end
        end

        stats[iface] = iface_stats
    end

    return stats
end

-- 获取mesh网络拓扑
function mesh_monitor.get_mesh_topology()
    local topology = {
        nodes = {},
        links = {},
        total_nodes = 0,
        cap_node = nil
    }

    -- 读取mesh拓扑文件
    local topo_files = {"/tmp/mac_mesh", "/tmp/mesh_topology", "/var/run/mesh_topo"}

    for _, topo_file in ipairs(topo_files) do
        local file = io.open(topo_file, "r")
        if file then
            local content = file:read("*a")
            file:close()

            if content and #content > 0 then
                -- 解析拓扑信息
                for line in content:gmatch("[^\r\n]+") do
                    local mac, role, signal, parent = string.match(line, "([%w:]+)%s+(%w+)%s+([-%d]+)%s*([%w:]*)")
                    if mac and role then
                        local node = {
                            mac = mac,
                            role = role,
                            signal = tonumber(signal) or -100,
                            parent = parent and #parent > 0 and parent or nil
                        }

                        table.insert(topology.nodes, node)

                        if role == "CAP" or role == "cap" then
                            topology.cap_node = node
                        end

                        -- 如果有父节点，创建链接
                        if node.parent then
                            table.insert(topology.links, {
                                from = node.parent,
                                to = mac,
                                signal = node.signal
                            })
                        end
                    end
                end
                break
            end
        end
    end

    topology.total_nodes = #topology.nodes
    return topology
end

-- 获取系统状态
function mesh_monitor.get_system_status()
    local status = {
        uptime = get_uptime(),
        memory = get_memory_info(),
        cpu_usage = get_cpu_usage(),
        load_average = {},
        temperature = 0
    }

    -- 获取系统负载
    local loadavg_file = io.open("/proc/loadavg", "r")
    if loadavg_file then
        local loadavg_str = loadavg_file:read("*line")
        loadavg_file:close()

        if loadavg_str then
            local load1, load5, load15 = string.match(loadavg_str, "([%d%.]+)%s+([%d%.]+)%s+([%d%.]+)")
            status.load_average = {
                one_minute = tonumber(load1) or 0,
                five_minutes = tonumber(load5) or 0,
                fifteen_minutes = tonumber(load15) or 0
            }
        end
    end

    -- 获取CPU温度（如果可用）
    local temp_files = {"/sys/class/thermal/thermal_zone0/temp", "/sys/class/hwmon/hwmon0/temp1_input"}
    for _, temp_file in ipairs(temp_files) do
        local file = io.open(temp_file, "r")
        if file then
            local temp_str = file:read("*line")
            file:close()

            if temp_str then
                local temp = tonumber(temp_str)
                if temp then
                    -- 温度通常以毫度为单位，转换为摄氏度
                    status.temperature = temp > 1000 and math.floor(temp / 1000) or temp
                    break
                end
            end
        end
    end

    return status
end

-- 获取mesh服务状态
function mesh_monitor.get_mesh_services_status()
    local services = {"wsplcd", "hyd", "repacd", "son_topo"}
    local status = {}

    for _, service in ipairs(services) do
        local cmd = string.format("ps | grep -v grep | grep %s", service)
        local success, result = execute_command(cmd)

        status[service] = {
            running = success and result and #result > 0,
            pid = nil,
            memory = 0,
            cpu = 0
        }

        if status[service].running and result then
            -- 提取PID
            local pid = string.match(result, "^%s*(%d+)")
            if pid then
                status[service].pid = tonumber(pid)

                -- 获取进程内存和CPU使用情况
                local proc_stat_file = io.open("/proc/" .. pid .. "/stat", "r")
                if proc_stat_file then
                    local stat_line = proc_stat_file:read("*line")
                    proc_stat_file:close()

                    if stat_line then
                        local fields = {}
                        for field in stat_line:gmatch("%S+") do
                            table.insert(fields, field)
                        end

                        -- 字段23是虚拟内存大小（以字节为单位）
                        if fields[23] then
                            status[service].memory = math.floor(tonumber(fields[23]) / 1024) -- 转换为KB
                        end
                    end
                end
            end
        end
    end

    return status
end

-- 获取完整的mesh状态报告
function mesh_monitor.get_full_status_report()
    local report = {
        timestamp = os.time(),
        system = mesh_monitor.get_system_status(),
        mesh_config = mesh_config.get_status(),
        mesh_services = mesh_monitor.get_mesh_services_status(),
        wireless_stats = mesh_monitor.get_wireless_stats(),
        mesh_topology = mesh_monitor.get_mesh_topology(),
        health_score = 0
    }

    -- 计算健康评分
    report.health_score = mesh_monitor.calculate_health_score(report)

    return report
end

-- 计算mesh网络健康评分
function mesh_monitor.calculate_health_score(report)
    local score = 100

    -- 检查系统资源使用情况
    if report.system.cpu_usage > 80 then
        score = score - 20
    elseif report.system.cpu_usage > 60 then
        score = score - 10
    end

    if report.system.memory.used / report.system.memory.total > 0.9 then
        score = score - 20
    elseif report.system.memory.used / report.system.memory.total > 0.8 then
        score = score - 10
    end

    -- 检查mesh服务状态
    local services_running = 0
    local total_services = 0
    for service, status in pairs(report.mesh_services) do
        total_services = total_services + 1
        if status.running then
            services_running = services_running + 1
        end
    end

    if services_running < total_services then
        score = score - (total_services - services_running) * 15
    end

    -- 检查无线信号质量
    local poor_signal_count = 0
    for iface, stats in pairs(report.wireless_stats) do
        if string.find(iface, "wlan") and stats.signal_level < -70 then
            poor_signal_count = poor_signal_count + 1
        end
    end

    if poor_signal_count > 0 then
        score = score - poor_signal_count * 10
    end

    -- 检查mesh拓扑
    if report.mesh_topology.total_nodes == 0 then
        score = score - 30
    elseif not report.mesh_topology.cap_node then
        score = score - 20
    end

    return math.max(0, score)
end

-- 生成性能统计
function mesh_monitor.get_performance_stats(duration)
    duration = duration or 60 -- 默认60秒

    local stats = {
        start_time = os.time(),
        duration = duration,
        samples = {},
        averages = {}
    }

    -- 收集多个样本
    local sample_interval = math.max(1, math.floor(duration / 10)) -- 最多10个样本
    local sample_count = math.floor(duration / sample_interval)

    for i = 1, sample_count do
        local sample = {
            timestamp = os.time(),
            cpu_usage = get_cpu_usage(),
            memory = get_memory_info(),
            wireless = mesh_monitor.get_wireless_stats()
        }

        table.insert(stats.samples, sample)

        if i < sample_count then
            os.execute("sleep " .. sample_interval)
        end
    end

    -- 计算平均值
    if #stats.samples > 0 then
        local total_cpu = 0
        local total_memory_used = 0

        for _, sample in ipairs(stats.samples) do
            total_cpu = total_cpu + sample.cpu_usage
            total_memory_used = total_memory_used + sample.memory.used
        end

        stats.averages = {
            cpu_usage = math.floor(total_cpu / #stats.samples),
            memory_used = math.floor(total_memory_used / #stats.samples)
        }
    end

    return stats
end

-- CGI接口处理
function mesh_monitor.handle_cgi()
    if not is_cgi() then
        return
    end

    -- 设置HTTP头
    print("Content-Type: application/json")
    print("Cache-Control: no-cache")
    print("")

    local method = os.getenv("REQUEST_METHOD")
    local query_string = os.getenv("QUERY_STRING") or ""
    local response = {success = false, message = "Unknown error"}

    if method == "GET" then
        if string.find(query_string, "action=status") then
            -- 获取完整状态报告
            local report = mesh_monitor.get_full_status_report()
            response = {success = true, data = report}

        elseif string.find(query_string, "action=system") then
            -- 获取系统状态
            local system_status = mesh_monitor.get_system_status()
            response = {success = true, data = system_status}

        elseif string.find(query_string, "action=services") then
            -- 获取mesh服务状态
            local services_status = mesh_monitor.get_mesh_services_status()
            response = {success = true, data = services_status}

        elseif string.find(query_string, "action=wireless") then
            -- 获取无线统计
            local wireless_stats = mesh_monitor.get_wireless_stats()
            response = {success = true, data = wireless_stats}

        elseif string.find(query_string, "action=topology") then
            -- 获取mesh拓扑
            local topology = mesh_monitor.get_mesh_topology()
            response = {success = true, data = topology}

        elseif string.find(query_string, "action=performance") then
            -- 获取性能统计
            local duration = tonumber(string.match(query_string, "duration=(%d+)")) or 60
            local perf_stats = mesh_monitor.get_performance_stats(duration)
            response = {success = true, data = perf_stats}

        else
            response = {success = false, message = "Invalid action"}
        end

    elseif method == "POST" then
        -- 处理POST请求（如果需要）
        local content_length = tonumber(os.getenv("CONTENT_LENGTH")) or 0
        if content_length > 0 then
            local post_data = io.read(content_length)
            local success, data = pcall(json.decode, post_data)

            if success and data then
                -- 这里可以添加需要POST处理的功能
                response = {success = false, message = "POST operations not implemented"}
            else
                response = {success = false, message = "Invalid JSON data"}
            end
        else
            response = {success = false, message = "No data provided"}
        end
    end

    print(json.encode(response))
end

-- 如果作为CGI运行，处理请求
if is_cgi() then
    mesh_monitor.handle_cgi()
end

return mesh_monitor