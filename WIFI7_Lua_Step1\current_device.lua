#!/usr/bin/lua

-- Copyright (c) 2013 The Linux Foundation. All rights reserved.
-- Not a Contribution.

-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

-- Lua环境：此脚本在OpenWRT的Lua环境中运行，确保有必要的Lua库，如luci.jsonc和luci.sys
-- UCI：这个脚本使用了uci库来直接操作配置文件。这比直接读写文件更为安全和标准。

-- vap_ifname：显示虚拟接口名称，即客户端连接的AP接口名称。
-- wifi-iface：显示WiFi接口的UCI配置名。
--网络：显示该WiFi接口关联的网络。
--客户端MAC地址：从iwinfo获取的关联客户端的MAC地址。
--信道：从无线设备配置中获取当前信道。
--tx_rate 和 rx_rate：客户端的传输速率和接收速率，以Mbit/s为单位。
--信号 和 噪声：客户端的信号和噪声强度。
--IPv4地址 和 IPv6地址：通过luci.sys.net的mac2ip和mac2ip6函数尝试获取客户端的IP地址。

--将此脚本命名为client_info.lua并放在LuCI的controller目录下，如/usr/lib/lua/luci/controller/.
--通过访问API端点（例如http://router_ip/cgi-bin/luci/api/client_info）来获取客户端信息。
--确保在执行时有足够的权限读取系统信息。
--注意：此脚本假设客户端已经通过DHCP获取了IP地址。如果客户端未获取IP地址或者使用静态IP，IP地址可能无法正确获取。

-- 引入模块
-- current_device.lua (v27 - 获取当前连接设备信息，精简数据，添加设备类型)
local cjson = require("cjson.safe")
local sys = require("luci.sys")
local io = require("io")
local os = require("os")

local log_file = "/tmp/current_device.log"

-- 日志写入函数 (与 v26 相同)
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] ") .. message .. "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end

-- 修剪字符串函数 (与 v26 相同)
local function trim(str)
    return str:match("^%s*(.-)%s*$")
end

-- 解析关联时长字符串 (HH:MM:SS) (与 v26 相同)
local function parse_assoc_time(assoc_time_str)
    local online_duration_str = "N/A"
    if assoc_time_str then
        local hours, minutes, seconds = assoc_time_str:match("(%d+):(%d+):(%d+)")
        if hours and minutes and seconds then
            local duration_seconds = tonumber(hours) * 3600 + tonumber(minutes) * 60 + tonumber(seconds)
            local days = math.floor(duration_seconds / (24 * 3600))
            local display_hours = math.floor((duration_seconds % (24 * 3600)) / 3600)
            local display_minutes = math.floor((duration_seconds % 3600) / 60)
            local display_seconds = duration_seconds % 60

            if days > 0 then
                online_duration_str = string.format("%dd %dh %dm %ds", days, display_hours, display_minutes, display_seconds)
            elseif display_hours > 0 then
                online_duration_str = string.format("%dh %dm %ds", display_hours, display_minutes, display_seconds)
            elseif display_minutes > 0 then
                online_duration_str = string.format("%dm %ds", display_minutes, seconds)
            else
                online_duration_str = string.format("%ds", display_seconds)
            end
            write_log("    Parsed Assoc Time: " .. assoc_time_str .. ", Duration (seconds): " .. duration_seconds .. ", Formatted Duration: " .. online_duration_str)
        else
            write_log("    Failed to parse Assoc Time string: " .. assoc_time_str)
        end
    else
        write_log("    Assoc Time string is nil or empty.")
    end
    return online_duration_str
end

-- 从 DHCP 租约文件中获取设备名和 IP 地址 (与 v26 相同)
local function get_device_info_from_dhcp(mac)
    local device_name = "-"
    local ip_address = "-"
    local leases_output = sys.exec("cat /tmp/dhcp.leases")
    if leases_output then
        for lease_line in leases_output:gmatch("[^\r\n]+") do
            local _, lease_mac, ip, device_name_with_mac = lease_line:match("^(%d+)%s+([%w:]+)%s+([^%s]+)%s+(.+)")
            if lease_mac and lease_mac:upper() == mac:upper() then
                ip_address = ip
                if device_name_with_mac then
                    local parts = device_name_with_mac:split(" ")
                    device_name = parts[1] or "-"
                else
                    device_name = "-"
                end
                return device_name, ip_address
            end
        end
    end
    return device_name, ip_address
end


-- *** 新增函数：从 MAC 过滤配置文件中读取所有 MAC 地址 ***
-- 返回一个集合 (table)，键是 MAC 地址 (大写)，值是 true
local function get_filtered_macs_from_config()
    local filtered_mac_set = {}
    local mac_filter_file = "/etc/mac_filter_config.txt" -- 使用与 mac_access_control.lua 相同的路径
    local file, err = io.open(mac_filter_file, "r")
    if file then
        write_log("Reading MAC filter config for exclusion: " .. mac_filter_file)
        local line_num = 0
        for line in file:lines() do
            line_num = line_num + 1
            -- 跳过第一行 (filter_mode 行)
            if line_num > 1 then
                local mac = line:match("^%s*(%x%x:%x%x:%x%x:%x%x:%x%x:%x%x)%s*$") -- 直接匹配 MAC 格式并去除前后空格
                if mac then
                    filtered_mac_set[mac:upper()] = true -- 存储大写 MAC
                    -- write_log("  Found MAC in config: " .. mac:upper()) -- Optional debug log
                -- else -- Optional: Log invalid lines after line 1 if needed
                --    write_log("  Skipping non-MAC line in config (line " .. line_num .. "): " .. line)
                end
            end
        end
        file:close()
        write_log("Finished reading MAC filter config. Found " .. table.maxn(filtered_mac_set or {}) .. " MACs for filtering.") -- Crude way to count keys
    else
        write_log("WARNING: Could not open MAC filter config file (" .. mac_filter_file .. "): " .. tostring(err) .. ". No devices will be filtered.")
    end
    return filtered_mac_set
end

-- 获取当前连接的无线设备信息 (v27 - 精简数据, 添加设备类型)
-- *** 修改后的函数：获取当前连接的无线设备信息 ***
-- (已移除对 get_blacklisted_macs 和 table.contains 的依赖)
local function get_current_devices()
    local users = {}
    -- 获取配置文件中所有 MAC 地址的集合，用于过滤
    local filtered_mac_set = get_filtered_macs_from_config() -- <<<< 修改点 1: 调用新函数

    local interfaces = {
        "ath0", "ath1", "ath2", "ath3",
        "ath10", "ath11", "ath12", "ath13",
        "ath30", "ath31", "ath32", "ath33"
    }
    local iface_type_map = { -- 假设的 ifname 到设备类型映射，请根据实际情况调整
        ath0 = "2.4G RF1", ath1 = "2.4G RF2", ath2 = "2.4G RF3", ath3 = "2.4G RF4",
        ath10 = "5G RF1", ath11 = "5G RF2", ath12 = "5G RF3", ath13 = "5G RF4",
        ath30 = "6G RF1", ath31 = "6G RF2", ath32 = "6G RF3", ath33 = "6G RF4"
    }

    for _, ifname in ipairs(interfaces) do
        local command = "wlanconfig " .. ifname .. " list sta"
        local output = sys.exec(command)

        if not output or output == "" then
            write_log("No client data found for interface: " .. ifname .. ", command output: " .. (output or "empty"))
        else
            write_log("wlanconfig command output for " .. ifname .. ":\n" .. output)
            local lines = {}
            for line in output:gmatch("[^\r\n]+") do
                table.insert(lines, line)
            end

            -- wlanconfig output format might vary slightly, adjust field indices if needed
            -- Assuming header is line 1, data starts line 2
            -- Assuming MAC is field 1, RSSI field 6, Assoc time field 20
            local header_line = lines[1] or ""
            -- TODO: Potentially parse header to dynamically find column indices if format changes

            for i = 2, #lines do
                local line = lines[i]
                -- Basic check if line starts like a MAC address
                if line:match("^%x%x:%x%x:%x%x:%x%x:%x%x:%x%x") then
                    local fields = {}
                    -- Split line by whitespace, handling potential multiple spaces
                    for field in line:gmatch("%S+") do
                        table.insert(fields, field)
                    end

                    -- Ensure enough fields exist before accessing them
                    if #fields >= 20 then -- Check for at least 20 fields to safely access index 20
                        local mac = fields[1]
                        local rssi = fields[6]
                        local assoc_time_str = fields[20]

                        -- >>>> 修改点 2: 使用 filtered_mac_set 进行检查 <<<<
                        if not filtered_mac_set[mac:upper()] then
                            -- MAC 不在配置文件中，处理该设备信息
                            write_log("  Processing device: " .. mac)

                            -- 清理 RSSI 字符串 (保持不变)
                            local sanitized_rssi = rssi:gsub("[^%d%-%.]", "")
                            local rssi_val = tonumber(sanitized_rssi) or 0

                            local device_name, ip = get_device_info_from_dhcp(mac)
                            local online_time = parse_assoc_time(assoc_time_str)
                            local device_type = iface_type_map[ifname] or "Unknown" -- 根据 ifname 获取设备类型

                            local user_info = {
                                device_type = device_type, -- 设备类型
                                device_name = device_name,
                                ip = ip,
                                mac = mac,
                                signal = rssi_val,        -- 信号强度 (dBm)
                                signal_unit = "dBm",
                                online_time = online_time -- 在线时长
                            }
                            table.insert(users, user_info)
                            write_log("    User info added: " .. cjson.encode(user_info))
                        else
                            -- >>>> 修改点 3: 更新跳过日志 <<<<
                            write_log("  MAC " .. mac .. " found in MAC filter config file, skipping.")
                        end
                    else
                        write_log("  Skipping line due to insufficient fields (" .. #fields .. "): " .. line)
                    end
                else
                     write_log("  Skipping line that doesn't start like a MAC: " .. line)
                end
            end
        end
    end
    return users
end



-- 路由 API 请求
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 获取 POST 数据
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""
    if POSTLength > 0 then
        POST = io.read(POSTLength)
    end

    -- 解析 JSON 请求
    local requestData = cjson.decode(POST)
    if not requestData then
        io.write(cjson.encode({ errcode = 2, message = "Invalid JSON input" }))
        return
    end

    if requestData.api == "get" then
        write_log("API request: get current devices")
        local result = get_current_devices()
        io.write(cjson.encode({
            module = "current_device",
            version = "1.0",
            api = "get",
            errcode = 0,
            sid = requestData.sid,
            result = { users = result }
        }))
    else
        io.write(cjson.encode({ errcode = 4, message = "Unknown API: " .. requestData.api }))
    end
end


-- 检查是否在CGI环境中运行
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- AC 调用接口
local M = {}

function M.set_config_from_ac(payload)
    write_log("[AC] set_config_from_ac called: " .. cjson.encode(payload))
    -- 当前设备列表是只读的，不支持设置
    write_log("[AC] Current device list is read-only")
    return false, "Current device list is read-only"
end

function M.get_config_for_ac()
    local devices = get_current_devices()
    write_log("[AC] get_config_for_ac: retrieved current devices")
    return { users = devices }
end

-- 仅在明确作为 CGI 脚本运行时执行
if arg and arg[0] and arg[0]:match("current_device%.lua") and is_cgi() then
    local function run()
        write_log("Current Device API started")
        route_api()
        write_log("Current Device API finished")
    end
    run()
end

return M