#!/usr/bin/lua

-- Copyright (c) 2024 The Linux Foundation. All rights reserved.
-- Not a Contribution.

-- Copyright 2024 xiayan <<EMAIL>>
-- Licensed to the public under the Apache License 2.0.

-- Lua环境：此脚本在OpenWRT的Lua环境中运行，确保有必要的Lua库，如luci.sys, luci.jsonc和luci.uci

-- UCI：这个脚本使用了uci库来直接操作配置文件。这比直接读写文件更为安全和标准。

-- 网桥模式功能概述：
-- 将无线信号转换为同步信号设备和一个无线接入点信号。
-- WAN口、LAN口和无线信号同一个VLAN。
-- DHCP 服务器默认关闭。
-- 支持两种连接方式：WDS桥接和万能桥接。
-- 配置包括：连接方式、内网设置、连接设置、2.4G/5G/6G无线设置。
-- 配置文件修改：
-- /etc/config/system：设置mode为bridge。
-- /etc/config/网络：
-- 将 WAN 和 LAN 桥接到同一个桥接接口（例如br-lan）。
-- 内网设置为静态IP或DHCP客户端。
-- 取消DHCP服务器（修改/etc/config/dhcp）。
-- /etc/config/无线：
-- 配置STA接口（sta0、sta1、sta3）用于连接上级无线网络（WDS 或万能桥接）。
-- 配置AP接口（wlan0、wlan10、wlan30）提供无线接入点。
-- 根据连接方式设置wds参数。

-- 说明
-- /etc/config/系统：
-- option mode 'bridge'：通过uci:set("system", "nhx", "mode", "bridge")设置。
-- /etc/config/网络：
-- WAN 和 LAN 桥接到br-lan（eth1.1 eth1.2）。
-- 支持静态IP或DHCP客户端。
-- 删除独立的wan和wan6接口。
-- /etc/config/无线：
-- STA接口（sta0、sta1、sta3）：根据实际选择一个启用，配置为mode 'sta'，设置SSID、加密等。
-- WDS：当connection_type为wds时，设置option wds '1'，点对点模式下设置bssid。
-- 万能桥接：option wds '0'，无bssid。
-- AP接口（wlan0、wlan10、wlan30）：配置为mode 'ap'，提供无线接入点。
-- /etc/config/dhcp：
-- 禁用 LAN 的 DHCP 服务器：optionignore '1'。

--设置连接设置：
--WDS模式：将wwanX接口桥接br-lan，确保STA参与桥接。
--万能桥接模式：wwanX为DHCP，并调用防火墙配置函数。
--删除cipher设置，因为当前配置只支持encryption。
--获取连接设置：
--新增status字段，返回STA连接状态。
--修改启用STA的检测逻辑。
--路由api：
--新增get_sta_status API，支持Web查询STA状态。
--新增函数：
--configure_universal_bridge_firewall：为万能桥连接配置NAT。
--get_sta_status和get_sta_status_api：检查并返回STA连接状态。

--增加了以下配置清理和恢复机制：
--路由模式配置清理：
--删除 WAN 协议配置（proto）
--删除 WAN IP 地址配置（ipaddr）
--删除 WAN 子网掩码配置（netmask）
--删除 WAN 网关配置（gateway）
--删除 WAN DNS 配置（dns）
--删除 WAN6 协议配置
--AP 模式配置清理：
--删除所有非标准的 def_ifname 配置
--确保 LAN 和 WAN 接口配置正确
--DHCP 服务器配置清理：
--禁用 DHCP 服务器（ignore = 1）
--删除 DHCP 地址池配置（start, limit）
--删除 DHCP 租约时间配置（leasetime）
--删除强制 DHCP 服务器运行配置（force）
--删除 IPv6 路由通告配置（ra）
--防火墙配置清理：
--删除 WAN 区域配置
--删除 WAN 到 LAN 的转发规则
--删除 LAN 到 WAN 的转发规则
--删除 NAT 规则
--无线配置清理：
--删除可能存在的旧 STA 接口配置（sta0, sta1, sta3）
--确保无线配置符合网桥模式要求
--配置提交和重启：
--在清理配置后立即提交更改
--使用异步方式重启网络和无线服务
--添加详细的日志记录
--这些改进确保了：
--从路由模式切换到网桥模式时，所有路由相关的配置都被正确清理
--从 AP 模式切换到网桥模式时，所有 AP 相关的配置都被正确清理
--网桥模式所需的标准配置都被正确设置
--避免了不同模式之间的配置冲突
--提供了更详细的日志记录，方便追踪配置变更
--建议在测试时特别关注以下几点：
--从路由模式切换到网桥模式时，确认所有路由相关的配置都被正确清理
--从 AP 模式切换到网桥模式时，确认所有 AP 相关的配置都被正确清理
--验证 WAN 和 LAN 接口是否能正确桥接
--检查防火墙规则是否正确应用
--确认 DHCP 服务器是否被正确禁用
--验证无线配置是否正确应用

-- 引入模块
local cjson = require("cjson.safe")
local sys = require("luci.sys")
local uci = require("luci.model.uci").cursor()
-- local config_apply_mode = require("config_apply_mode")
local log_file = "/tmp/bridge_mode.log" -- 日志文件路径

-- 日志写入函数
local function write_log(message)
    local file = io.open(log_file, "a")
    if file then
        file:write(os.date("[%Y-%m-%d %H:%M:%S] "), message, "\n")
        file:close()
    else
        print("Failed to open log file: " .. log_file)
    end
end

-- 获取当前应用模式的辅助函数
local function get_current_apply_mode()
    local mode_file = "/etc/config_apply_mode_status"
    local f = io.open(mode_file, "r")
    if f then
        local mode = f:read("*l") -- Read the first line
        f:close()
        mode = mode and mode:gsub("^%s*(.-)%s*$", "%1") -- Trim whitespace
        if mode == "immediate" or mode == "deferred" then
            write_log("Read apply_mode from " .. mode_file .. ": " .. mode)
            return mode
        else
            write_log("Invalid content in " .. mode_file .. ": '" .. (mode or "nil") .. "'. Defaulting to 'immediate'.")
            return "immediate"
        end
    else
        write_log("Could not open " .. mode_file .. ". Defaulting to 'immediate'.")
        return "immediate"
    end
end



-- 验证 IP 地址、子网掩码、网关和 DNS
local function is_valid_ip(ip)
    return ip and ip:match("^%d+%.%d+%.%d+%.%d+$") ~= nil
end

local function is_valid_netmask(netmask)
    return netmask and netmask:match("^%d+%.%d+%.%d+%.%d+$") ~= nil
end

local function is_valid_gateway(gateway)
    return gateway == "" or (gateway and is_valid_ip(gateway))
end

local function is_valid_dns(dns)
    return dns == "" or (dns and is_valid_ip(dns))
end

-- 验证 RSSI 阈值
local function is_valid_rssi(rssi)
    local r = tonumber(rssi)
    return r and r >= -100 and r <= 0 -- RSSI 通常在 -100 到 0 dBm 之间
end

-- 路由到具体逻辑
function route_api()
    -- 设置 HTTP 响应头
    io.write("Content-type: application/json\nPragma: no-cache\n\n")

    -- 获取 POST 数据
    local POSTLength = tonumber(os.getenv("CONTENT_LENGTH")) or 0
    local POST = ""
    if POSTLength > 0 then
        POST = io.read(POSTLength)
        write_log("Received POST data: " .. (POST or "nil"))
    else
        write_log("No POST data received or CONTENT_LENGTH is 0")
    end

    if not POST or POST == "" then
        local error_message = "Failed to retrieve POST data"
        write_log(error_message)
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            errcode = 1,
            result = { message = error_message }
        }))
        return
    end

    -- 解析 POST 数据为 JSON
    local requestData = cjson.decode(POST)
    if not requestData then
        local error_message = "Invalid JSON input"
        write_log(error_message)
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            errcode = 2,
            result = { message = error_message }
        }))
        return
    end

    write_log("Parsed request data: " .. cjson.encode(requestData))

    -- 检查请求格式
    if not requestData.version or not requestData.sid or not requestData.module or not requestData.api then
        local error_message = "Invalid request format"
        write_log(error_message)
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            errcode = 3,
            result = { message = error_message }
        }))
        return
    end

    if requestData.api == "set" then
        write_log("Calling set_all_settings with data: " .. cjson.encode(requestData))
        set_all_settings(requestData)
    elseif requestData.api == "get" then
        write_log("Calling get_all_settings with data: " .. cjson.encode(requestData))
        get_all_settings(requestData)
    elseif requestData.api == "scan_ssid" then
        write_log("Calling scan_ssid with data: " .. cjson.encode(requestData))
        scan_ssid(requestData)
    elseif requestData.api == "get_sta_status" then
        write_log("Calling get_sta_status with data: " .. cjson.encode(requestData))
        get_sta_status_api(requestData)
    else
        local error_message = "Unknown API: " .. requestData.api
        write_log(error_message)
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            errcode = 4,
            result = { message = error_message }
        }))
    end
end

function configure_universal_bridge_firewall(wwan_iface)
    -- 配置防火墙区域和转发规则
    uci:delete("firewall", "wwan_zone")
    uci:section("firewall", "zone", "wwan_zone", {
        name = "wwan",
        input = "ACCEPT",
        output = "ACCEPT",
        forward = "ACCEPT",
        network = wwan_iface
    })

    uci:delete("firewall", "wwan_to_lan")
    uci:section("firewall", "forwarding", "wwan_to_lan", {
        src = "wwan",
        dest = "lan"
    })

    uci:delete("firewall", "masquerade_rule")
    uci:section("firewall", "rule", "masquerade_rule", {
        name = "Masquerade",
        src = "wwan",
        dest = "lan",
        target = "MASQUERADE"
    })

    uci:commit("firewall")
    write_log("Configured firewall for universal bridge mode on " .. wwan_iface)
end

-- 简化防火墙配置（桥接模式通用）
function configure_bridge_firewall()
    -- 删除 wwan 相关配置
    uci:delete("firewall", "wwan_zone")
    uci:delete("firewall", "wwan_to_lan")
    uci:delete("firewall", "masquerade_rule")
    
    -- 删除 wan 相关配置（路由模式残留）
    uci:delete("firewall", "@forwarding[0]") -- 删除 lan -> wan 转发
    uci:foreach("firewall", "zone", function(s)
        if s.name == "wan" then
            uci:delete("firewall", s[".name"])
            write_log("Deleted wan zone: " .. s[".name"])
        end
        if s.name == "lan" then
            uci:delete("firewall", s[".name"])
            write_log("Deleted duplicate lan zone: " .. s[".name"])
        end
    end)
    
    -- 删除 wan 相关的规则
    uci:foreach("firewall", "rule", function(s)
        if s.src == "wan" then
            uci:delete("firewall", s[".name"])
            write_log("Deleted wan-related rule: " .. s.name)
        end
    end)
    
    -- 配置 lan 区域
    uci:section("firewall", "zone", "lan_zone", {
        name = "lan",
        input = "ACCEPT",
        output = "ACCEPT",
        forward = "ACCEPT",
        network = "lan"
    })
    
    uci:commit("firewall")
    write_log("Configured firewall for bridge mode")
end

function get_sta_status(wwan_iface)
    local ifname = uci:get("network", wwan_iface, "ifname") or "ath5"
    local status_output = sys.exec("iwinfo " .. ifname .. " info")
    if status_output and status_output:match("ESSID:") then
        local essid = status_output:match("ESSID: \"(.+)\"") or "unknown"
        local signal = status_output:match("Signal: (%-%d+) dBm") or "unknown"
        local ap = status_output:match("Access Point: ([%w:]+)") or "unknown"
        return {
            connected = true,
            ssid = essid,
            signal = signal,
            ap = ap
        }
    else
        return {
            connected = false,
            ssid = "N/A",
            signal = "N/A",
            ap = "N/A"
        }
    end
end

function get_sta_status_api(data)
    local param = data.param or {}
    local band = param.band or "2.4G"
    local wwan_iface
    if band == "2.4G" then
        wwan_iface = "wwan0"
    elseif band == "5G" then
        wwan_iface = "wwan1"
    elseif band == "6G" then
        wwan_iface = "wwan3"
    else
        local error_message = "Invalid band: " .. band
        write_log(error_message)
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            api = "get_sta_status",
            errcode = 10,
            sid = data.sid,
            result = { message = error_message }
        }))
        return
    end

    local status = get_sta_status(wwan_iface)
    write_log("STA status for " .. band .. ": " .. cjson.encode(status))
    io.write(cjson.encode({
        module = "bridge_mode",
        version = "1.0",
        api = "get_sta_status",
        errcode = 0,
        sid = data.sid,
        result = {
            band = band,
            status = status
        }
    }))
end

-- 自动接口管理函数
function manage_interfaces_for_mode(mode)
    local connection_type = (requestData.param or {}).connection_type or "wds"
    if mode == "bridge" then
        -- 删除 wwan 接口
        uci:delete("network", "wwan0")
        uci:delete("network", "wwan1")
        uci:delete("network", "wwan3")

        -- 拼接 br-lan 的 ifname
        local ifnames = {"eth1.1", "eth1.2"}
        local active_sta = nil
        local active_vaps = {}
        local sta_count = 0
        
        -- 查找活跃 STA
        uci:foreach("wireless", "wifi-iface", function(s)
            if s.mode == "sta" and s.disabled ~= "1" and s.ifname then
                active_sta = s.ifname
                sta_count = sta_count + 1
                write_log("Found active STA: " .. s.ifname .. " (SSID: " .. (s.ssid or "none") .. ")")
            end
        end)

        if sta_count > 1 then
            write_log("Warning: Multiple active STAs detected, using " .. (active_sta or "none"))
        elseif sta_count == 0 then
            write_log("Warning: No active STA detected")
        end

        -- 查找活跃 VAP（仅主 SSID）
        uci:foreach("wireless", "wifi-iface", function(s)
            if s.mode == "ap" and s.disabled ~= "1" and s.ifname and
               (s.ssid:match("^3onedata%-%d+%-") or s.ssid:match("^3onedata%-%d%-%w+%-")) then
                table.insert(active_vaps, s.ifname)
                write_log("Found active VAP: " .. s.ifname .. " with SSID " .. s.ssid)
            end
        end)

        -- 添加 STA 和 VAP
        if active_sta then
            table.insert(ifnames, active_sta)
        else
            write_log("No active STA added to br-lan")
        end
        for _, vap in ipairs(active_vaps) do
            table.insert(ifnames, vap)
        end

        -- 设置 br-lan
        uci:set("network", "lan", "ifname", table.concat(ifnames, " "))
        uci:set("network", "lan", "proto", "dhcp")
        uci:set("network", "lan", "type", "bridge")
        uci:set("network", "lan", "stp", "1") -- 启用 STP
        uci:set("dhcp", "lan", "ignore", "1")
        uci:commit("network")
        uci:commit("dhcp")
        write_log("Configured br-lan ifname: " .. table.concat(ifnames, " "))

        -- 强制添加接口到 br-lan，添加延迟确保接口就绪

         for _, ifname in ipairs(ifnames) do
             sys.exec("sleep 1; brctl addif br-lan " .. ifname .. " >/dev/null 2>&1")
             local result = sys.exec("brctl show | grep " .. ifname)
             if result == "" then
                 write_log("Failed to add interface " .. ifname .. " to br-lan in manage_interfaces_for_mode")
             else
                 write_log("Successfully added interface " .. ifname .. " to br-lan in manage_interfaces_for_mode")
             end
         end
    else
        write_log("manage_interfaces_for_mode: called with unsupported mode '" .. tostring(mode) .. "'")
    end
end

-- 新增：生成后台脚本（简化 wifi 初始化）
local function generate_background_script(sta_ifname, connection_type)
    write_log("generate_background_script: Called with sta_ifname='" .. tostring(sta_ifname) .. "', connection_type='" .. tostring(connection_type) .. "'") -- 添加日志
    local script_path = "/tmp/apply_bridge.sh"
    local script_content = [[
#!/bin/sh
logger -t bridge_mode "Starting background bridge configuration for $1 (connection_type: ]] .. connection_type .. [[)"

# 禁用所有旧 STA 接口，防止冲突
for old_if in ath5 ath15 ath35; do
    if [ "$old_if" != "$1" ]; then
        uci set wireless.@wifi-iface[$old_if].disabled=1
        logger -t bridge_mode "Disabled old STA interface: $old_if"
    fi
done
uci commit wireless
    -- 强制添加接口到 br-lan，添加延迟确保接口就绪
    -- Comment out or remove this block, rely on generate_background_script and network restart
     for _, ifname in ipairs(ifnames) do
         sys.exec("sleep 1; brctl addif br-lan " .. ifname .. " >/dev/null 2>&1")
         local result = sys.exec("brctl show | grep " .. ifname)
         if result == "" then
             write_log("Failed to add interface " .. ifname .. " to br-lan in manage_interfaces_for_mode")
         else
             write_log("Successfully added interface " .. ifname .. " to br-lan in manage_interfaces_for_mode")
         end
     end
else
    write_log("manage_interfaces_for_mode: called with unsupported mode '" .. tostring(mode) .. "'")
end
# 检查参数是否存在
if [ -z "$1" ]; then
    logger -t bridge_mode "Error: Interface parameter \$1 is missing"
    exit 1
fi

# 检查接口是否存在
iw dev "$1" info >/dev/null 2>&1
if [ $? -ne 0 ]; then
    logger -t bridge_mode "Error: Interface $1 does not exist"
    # 可选：尝试其他接口
    for iface in ath5 ath15 ath35; do
        if [ "$iface" != "$1" ]; then
            iw dev "$iface" info >/dev/null 2>&1
            if [ $? -eq 0 ]; then
                logger -t bridge_mode "Switching to available interface $iface"
                set -- "$iface"
                break
            fi
        fi
    done
    if [ "$1" = "$iface" ]; then
        logger -t bridge_mode "Found available interface $1"
    else
        logger -t bridge_mode "No available interfaces found, exiting"
        exit 1
    fi
fi

# 第一次尝试：重启 WiFi

sleep 120  # 等待 2 分钟确保 WiFi 稳定
logger -t bridge_mode "WiFi restart completed, waited 120 seconds"

# 检查 $1 是否桥接成功
iw dev "$1" link | grep -q "Connected to"
if [ $? -ne 0 ]; then
    logger -t bridge_mode "$1 not associated after first attempt, relying on monitor"
else
    logger -t bridge_mode "$1 associated with AP after first attempt"
fi

# 确保 $1 加入 br-lan
brctl addif br-lan $1
if [ $? -eq 0 ]; then
    logger -t bridge_mode "Added $1 to br-lan"
else
    logger -t bridge_mode "Failed to add $1 to br-lan"
fi

# 重启网络服务（仅一次）
/etc/init.d/network restart
sleep 5
logger -t bridge_mode "Network service restarted"

# 重启防火墙服务
/etc/init.d/firewall restart
logger -t bridge_mode "Firewall service restarted"

# 验证 $1 桥接
brctl show | grep -q "$1"
if [ $? -eq 0 ]; then
    logger -t bridge_mode "$1 successfully added to br-lan"
else
    logger -t bridge_mode "$1 not found in br-lan"
fi

# 在万能桥接模式下杀掉 iface_mgr 进程
if [ "]] .. connection_type .. [[" = "universal" ]; then
    logger -t bridge_mode "Detected universal bridge mode, attempting to kill iface_mgr"
    IFACE_MGR_PID=$(ps | grep '[i]face-mgr' | awk '{print $1}')
    if [ -n "$IFACE_MGR_PID" ]; then
        kill -9 $IFACE_MGR_PID
        logger -t bridge_mode "Successfully killed iface_mgr process (PID: $IFACE_MGR_PID)"
    else
        logger -t bridge_mode "iface_mgr process not found"
    fi
else
    logger -t bridge_mode "Not in universal bridge mode (connection_type: ]] .. connection_type .. [[), skipping iface_mgr termination"
fi

logger -t bridge_mode "Bridge configuration completed, IP handling delegated to monitor"
]]

    local file = io.open(script_path, "w")
    if file then
        file:write(script_content)
        file:close()
        os.execute("chmod +x " .. script_path)
        write_log("Generated background script: " .. script_path)
    else
        write_log("Failed to generate background script")
        return false
    end
    return true
end

-- 设置所有配置
function set_all_settings(data)
    local param = data.param or {}
    local mode = param.mode or "bridge"
    local connection_type = param.connection_type or "wds" -- 默认 WDS 桥接
    write_log("set_all_settings: Determined connection_type = '" .. tostring(connection_type) .. "'") -- 添加日志
    -- 自动接口管理
    manage_interfaces_for_mode(mode)

    -- 设置系统模式为 bridge
    uci:set("system", "nhx", "mode", "bridge")
    write_log("Setting system mode to bridge")
    uci:set("system", "nhx", "bridge_mode", connection_type) -- 新增设置 bridge_mode
    write_log("Setting system mode to bridge, bridge_mode to " .. connection_type)
    -- 清理其他模式的配置
    write_log("Cleaning up configurations from other modes")
    
    -- 1. 清理路由模式配置
    write_log("Cleaning up router mode configurations")
    uci:delete("network", "wan", "ipaddr")  -- 删除 WAN IP 配置
    uci:delete("network", "wan", "netmask")  -- 删除 WAN 子网掩码
    uci:delete("network", "wan", "gateway")  -- 删除 WAN 网关
    uci:delete("network", "wan", "dns")  -- 删除 WAN DNS
    uci:set("network", "wan", "proto", "none")  -- 确保 WAN 禁用
    uci:set("network", "wan6", "proto", "none")  -- 确保 WAN6 禁用
    
    -- 2. 清理 AP 模式配置
    write_log("Cleaning up AP mode configurations")
    
    -- 3. 清理 VLAN 配置（网桥模式下所有端口属于 VLAN 1）
    write_log("Cleaning up VLAN configurations")
    uci:foreach("network", "switch_vlan", function(s)
        uci:delete("network", s[".name"])
        write_log("Deleted VLAN: " .. s[".name"])
    end)
    uci:set("network", "switch_vlan1", "switch_vlan")
    uci:set("network", "switch_vlan1", "device", "switch1")
    uci:set("network", "switch_vlan1", "vlan", "1")
    uci:set("network", "switch_vlan1", "ports", "0t 1 2 3 4")
    
    -- 4. 清理 DHCP 服务器配置
    write_log("Cleaning up DHCP server configurations")
    uci:set("dhcp", "lan", "ignore", "1")
    uci:set("dhcp", "lan", "start", "100")
    uci:set("dhcp", "lan", "limit", "150")
    uci:set("dhcp", "lan", "leasetime", "12h")
    uci:delete("dhcp", "lan", "dhcp_option")  -- 删除路由模式的残留配置
    uci:set("dhcp", "wan", "ignore", "1")  -- 确保 WAN DHCP 禁用
    
    -- 5. 清理防火墙配置
    write_log("Cleaning up firewall configurations")
    configure_bridge_firewall()
    
    -- 6. 清理无线配置
    write_log("Cleaning up wireless configurations")
    -- 只禁用，不删除sta接口
    uci:set("wireless", "sta0", "disabled", "1")
    uci:set("wireless", "sta1", "disabled", "1")
    uci:set("wireless", "sta3", "disabled", "1")
    -- 禁用 son1 和 son2，避免 ath14 和 ath34 加入 br-lan
    uci:set("wireless", "son1", "disabled", "1")
    uci:set("wireless", "son2", "disabled", "1")
    
    -- 7. 禁用 DDNS 服务（不删除配置）
    write_log("Disabling DDNS service")
    sys.exec("/etc/init.d/ddns stop")  -- 停止 DDNS 服务
    
    -- 8. 提交清理的配置
    write_log("Committing cleanup changes")
    uci:commit("network")
    uci:commit("dhcp")
    uci:commit("firewall")
    uci:commit("wireless")
    uci:commit("system")

    -- 设置连接方式和连接设置
    local connection = param.connection or {}
    set_connection_settings(connection_type, connection)

    -- 设置内网配置
    local lan_param = param.lan or {}
    set_lan_settings(lan_param)

    -- 设置 WiFi (2.4G, 5G, 6G)
    local wifi_param = param.wifi or {}
    set_wifi(wifi_param)

    -- 只同步同radio下的STA和AP信道
    uci:foreach("wireless", "wifi-iface", function(s)
        if s.mode == "sta" and s.disabled ~= "1" then
            local sta_channel = uci:get("wireless", s.device, "channel")
            -- 只同步同device下的AP
            uci:foreach("wireless", "wifi-iface", function(ap)
                if ap.device == s.device and ap.mode == "ap" then
                    uci:set("wireless", ap[".name"], "channel", sta_channel)
                    write_log("Set AP " .. ap[".name"] .. " channel to " .. tostring(sta_channel))
                end
            end)
            -- 只同步该device的device配置
            uci:set("wireless", s.device, "channel", sta_channel)
            write_log("Set device " .. s.device .. " channel to " .. tostring(sta_channel))
        end
    end)

    -- 提交所有更改
    write_log("Committing all configuration changes")
    uci:commit("network")
    uci:commit("wireless")
    uci:commit("system")
    -- 在合适的时间重新桥接RSSI指示灯服务
    local ret = os.execute("/etc/init.d/rssileds restart")

    -- 先返回干净的 JSON 响应给 Web
    write_log("All settings updated successfully")
    io.write(cjson.encode({
        module = "bridge_mode",
        version = "1.0",
        api = "set",
        errcode = 0,
        sid = data.sid,
        result = { message = "All settings updated successfully" }
    }))
    io.flush() -- 确保输出缓冲被刷新

    -- 强制更新桥接接口，去除重复接口
    local current_ifname = uci:get("network", "lan", "ifname") or ""
    write_log("Final br-lan ifname: " .. current_ifname)
    local ifname_set = {}
    for ifname in current_ifname:gmatch("%S+") do
        if not ifname_set[ifname] then
            ifname_set[ifname] = true
            local result = sys.exec("brctl addif br-lan " .. ifname .. " 2>&1")
            if result == "" then
                write_log("Successfully added interface " .. ifname .. " to br-lan")
            else
                write_log("Failed to add interface " .. ifname .. " to br-lan: " .. result)
            end
        else
            write_log("Skipping duplicate interface " .. ifname)
        end
    end

    -- 杀掉旧的 bridge_monitor 进程
    sys.exec("killall bridge_monitor 2>/dev/null")
    write_log("Killed existing bridge_monitor process")

    -- 异步执行桥接配置
    local bridge_ifname
    local band = connection.band or "2.4G"
    if band == "2.4G" then
        bridge_ifname = "ath5"
    elseif band == "5G" then
        bridge_ifname = "ath15"
    elseif band == "6G" then
        bridge_ifname = "ath35"
    else
        write_log("Invalid band: " .. band .. ", using default ath5")
        bridge_ifname = "ath5"
    end

    if not generate_background_script(bridge_ifname, connection_type) then
        write_log("Failed to generate background script")
        return
    end

    local restart_ok = sys.call("/tmp/apply_bridge.sh " .. bridge_ifname .. " &")
    if restart_ok ~= 0 then
        write_log("Failed to start async bridge configuration, return code: " .. restart_ok)
    else
        write_log("Async bridge configuration initiated")
    end

    -- 增加短暂延迟，确保 UCI 配置写入
    sys.call("sleep 1")
    write_log("Starting bridge_monitor")
    -- 启动新的 bridge_monitor 进程
    local monitor_ok = sys.call("/www/cgi-bin/3onedata/bridge_monitor &")
    if monitor_ok == 0 then
        write_log("Started bridge_monitor successfully")
    else
        write_log("Failed to start bridge_monitor, return code: " .. monitor_ok)
    end

    write_log("All settings updated successfully")

    -- UCI 提交
    uci:commit("network")
    uci:commit("dhcp")
    uci:commit("system")
    uci:commit("wireless")
    write_log("UCI commit successful for bridge mode")

    local apply_mode = get_current_apply_mode()
    write_log("Current apply mode: " .. apply_mode)

    if apply_mode == "immediate" then
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            api = "set",
            errcode = 0,
            sid = data.sid,
            result = { message = "桥接模式设置已更新，正在重启服务..." }
        }))
        io.flush()
        -- 异步重启服务
        local restart_cmd = [[
            /bin/sh -c "
                echo 'Starting bridge mode restart sequence' >> /tmp/bridge_mode_restart.log;
                /etc/init.d/network stop;
                sleep 2;
                sync;
                /etc/init.d/network start;
                sleep 3;
                wifi;
                sleep 2;
                echo 'Bridge mode restart sequence completed' >> /tmp/bridge_mode_restart.log;
            " &
        ]]
        os.execute(restart_cmd)
    else
        write_log("Apply mode is deferred. Services will not be restarted automatically.")
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            api = "set",
            errcode = 0,
            sid = data.sid,
            result = { message = "桥接模式设置已保存，延迟生效" }
        }))
        io.flush()
    end
end

-- 获取所有配置
function get_all_settings(data)
    local result = {
        module = "bridge_mode",
        version = "1.0",
        api = "get",
        errcode = 0,
        sid = data.sid,
        result = {
            connection_type = get_connection_type(),
            connection = get_connection_settings(),
            lan = get_lan_settings(),
            wifi = get_wifi(),
            mode = "bridge" -- 固定返回 bridge
        }
    }

    write_log("All settings retrieved successfully")
    io.write(cjson.encode(result))
end

-- 设置连接方式和连接设置
function set_connection_settings(connection_type, connection)
    local band = connection.band or "2.4G"
    local sta_iface, wifi_device, wwan_iface, sta_ifname
    if band == "2.4G" then
        sta_iface = "sta0"
        wifi_device = "wifi0"
        wwan_iface = "wwan0"
        sta_ifname = "ath5"
        local ret = os.execute("uci set system.rssid_vif_sta0.dev='ath5' && uci commit system")
    elseif band == "5G" then
        sta_iface = "sta1"
        wifi_device = "wifi1"
        wwan_iface = "wwan1"
        sta_ifname = "ath15"
        local ret = os.execute("uci set system.rssid_vif_sta0.dev='ath15' && uci commit system")
    elseif band == "6G" then
        sta_iface = "sta3"
        wifi_device = "wifi2"
        wwan_iface = "wwan3"
        sta_ifname = "ath35"
        local ret = os.execute("uci set system.rssid_vif_sta0.dev='ath35' && uci commit system")
    else
        local error_message = "Invalid band: " .. band
        write_log(error_message)
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            api = "set",
            errcode = 5,
            result = { message = error_message }
        }))
        return
    end

    -- 禁用所有 STA 接口，确保只启用目标 STA
    for _, s in ipairs({"sta0", "sta1", "sta3"}) do
        if s ~= sta_iface then
            uci:set("wireless", s, "disabled", "1")
            write_log("Disabled STA interface: " .. s)
        end
    end

    -- Encryption 字段映射
    local encryption_map = {
        ["WPA2 PSK (CCMP)"] = "psk2",
        ["WPA2 PSK (TKIP, CCMP)"] = "psk2",
        ["WPA PSK (TKIP)"] = "psk",
        ["mixed WPA/WPA2 PSK (CCMP)"] = "psk-mixed",
        ["mixed WPA/WPA2 PSK (TKIP, CCMP)"] = "psk-mixed",
        ["WPA3 SAE"] = "sae",
        ["WPA2/WPA3 SAE"] = "sae-mixed",
        ["none"] = "none"
    }
    local enc = connection.encryption or "none"
    local mapped_enc = encryption_map[enc] or enc
    write_log("Mapping encryption: input=" .. tostring(enc) .. ", mapped=" .. tostring(mapped_enc))

    -- 设置 STA 接口
    uci:set("wireless", sta_iface, "mode", "sta")
    uci:set("wireless", sta_iface, "ssid", connection.ssid or "")
    uci:set("wireless", sta_iface, "encryption", mapped_enc)
    -- 设置 sae 选项
    if mapped_enc == "sae" or mapped_enc == "sae-mixed" then
        uci:set("wireless", sta_iface, "sae", "1")
    else
        uci:set("wireless", sta_iface, "sae", "0")
    end
    if mapped_enc ~= "none" then
        uci:set("wireless", sta_iface, "key", connection.key or "")
    end
    uci:set("wireless", sta_iface, "disabled", "0")
    uci:set("wireless", sta_iface, "ifname", sta_ifname)

    -- 清理不受支持的配置
    uci:delete("wireless", sta_iface, "vap_ind")
    uci:delete("wireless", sta_iface, "blockdfschan")

    -- 设置连接方式
    if connection_type == "wds" then
        uci:set("wireless", sta_iface, "network", "lan")
        uci:set("wireless", sta_iface, "wds", "1")
        uci:set("wireless", sta_iface, "extap", "0")
        if connection.mode == "p2p" and connection.peer_mac then
            uci:set("wireless", sta_iface, "bssid", connection.peer_mac)
        else
            uci:delete("wireless", sta_iface, "bssid")
        end
        if connection.rssi_threshold and is_valid_rssi(connection.rssi_threshold) then
            uci:set("wireless", sta_iface, "rssi_threshold", connection.rssi_threshold)
        else
            uci:delete("wireless", sta_iface, "rssi_threshold")
        end
        -- WDS 模式下桥接到 lan
        uci:set("network", wwan_iface, "ifname", sta_ifname)
        uci:set("network", wwan_iface, "proto", "none")
        uci:set("network", wwan_iface, "disabled", "0")
        -- 更新 lan 的 ifname，添加 STA 接口
        local lan_ifname = uci:get("network", "lan", "ifname") or "eth1.1 eth1.2"
        lan_ifname = lan_ifname:gsub("ath[0-3][0-5]", ""):gsub("%s+", " ")
        uci:set("network", "lan", "ifname", lan_ifname .. " " .. sta_ifname)
        write_log("Updated br-lan ifname: " .. lan_ifname .. " " .. sta_ifname)
    elseif connection_type == "universal" then
        uci:set("wireless", sta_iface, "network", "lan") -- 与 WDS 模式一致，桥接到 lan
        uci:set("wireless", sta_iface, "wds", "0")
        uci:set("wireless", sta_iface, "extap", "1") -- 启用 extap 模式以支持万能桥接
        if connection.mode == "p2p" and connection.peer_mac then
            uci:set("wireless", sta_iface, "bssid", connection.peer_mac)
        else
            uci:delete("wireless", sta_iface, "bssid")
        end
        if connection.rssi_threshold and is_valid_rssi(connection.rssi_threshold) then
            uci:set("wireless", sta_iface, "rssi_threshold", connection.rssi_threshold)
        else
            uci:delete("wireless", sta_iface, "rssi_threshold")
        end
        -- 移除独立的 wwan 接口，直接桥接到 br-lan
        uci:set("network", wwan_iface, "ifname", sta_ifname)
        uci:set("network", wwan_iface, "proto", "none") -- 与 WDS 模式一致，禁用 wwan 的 DHCP
        uci:set("network", wwan_iface, "disabled", "0")
    else
        local error_message = "Invalid connection type: " .. connection_type
        write_log(error_message)
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            api = "set",
            errcode = 6,
            result = { message = error_message }
        }))
        return
    end

    -- 动态选择信道
    local best_channel = "auto"
    local best_signal = -100
    local scan_interface = (band == "2.4G" and "ath0") or (band == "5G" and "ath10") or "ath30"
    local scan_result = sys.exec("iwinfo " .. scan_interface .. " scan")
    for line in scan_result:gmatch("[^\n]+") do
        if line:match("ESSID: \"" .. connection.ssid .. "\"") then
            local sig = tonumber(line:match("Signal: (%-%d+) dBm")) or -100
            local ch = line:match("Channel: (%d+)") or "auto"
            if sig > best_signal then
                best_signal = sig
                best_channel = ch
            end
        end
    end
    if best_channel == "auto" then
        -- 如果扫描失败，根据频段设置默认信道
        if band == "2.4G" then
            best_channel = "1"  -- 默认 2.4G 信道
        elseif band == "5G" then
            best_channel = "36"  -- 默认 5G 信道
        elseif band == "6G" then
            best_channel = "1"  -- 默认 6G 信道（假设值，可根据实际调整）
        end
    end
    write_log("Best channel for SSID " .. connection.ssid .. ": " .. best_channel .. ", signal: " .. best_signal .. " dBm")
    uci:set("wireless", wifi_device, "channel", best_channel)

    -- 提交配置并重启服务
    uci:commit("wireless")
    uci:commit("network")
    os.execute("wifi down")
    os.execute("sleep 2")
    os.execute("wifi up")
    os.execute("sleep 5")
    write_log("Forced wifi service restarted for band " .. band)
end

-- 获取连接设置
function get_connection_settings()
    -- 判断当前模式
    local mode = uci:get("system", "nhx", "mode") or "ap"
    local ssid = ""
    local sta_iface, band, wwan_iface

    -- 动态检测实际连接的 STA 接口
    local band_map = {
        ["2.4G"] = {sta = "sta0", ifname = "ath5", wwan = "wwan0"},
        ["5G"]   = {sta = "sta1", ifname = "ath15", wwan = "wwan1"},
        ["6G"]   = {sta = "sta3", ifname = "ath35", wwan = "wwan3"}
    }
    for b, i in pairs(band_map) do
        local status_output = sys.exec("iwinfo " .. i.ifname .. " info")
        if status_output and status_output:match("ESSID:") then
            sta_iface = i.sta
            band = b
            wwan_iface = i.wwan
            ssid = uci:get("wireless", i.sta, "ssid") or ""
            write_log("get_connection_settings: found connected STA interface, band=" .. band .. ", ifname=" .. i.ifname)
            break
        end
    end

    -- 回退到 UCI 配置
    if not band then
        if uci:get("wireless", "sta0", "disabled") == "0" then
            sta_iface = "sta0"
            band = "2.4G"
            wwan_iface = "wwan0"
            ssid = uci:get("wireless", "sta0", "ssid") or ""
        elseif uci:get("wireless", "sta1", "disabled") == "0" then
            sta_iface = "sta1"
            band = "5G"
            wwan_iface = "wwan1"
            ssid = uci:get("wireless", "sta1", "ssid") or ""
        elseif uci:get("wireless", "sta3", "disabled") == "0" then
            sta_iface = "sta3"
            band = "6G"
            wwan_iface = "wwan3"
            ssid = uci:get("wireless", "sta3", "ssid") or ""
        else
            sta_iface = "sta0"
            band = "2.4G"
            wwan_iface = "wwan0"
            ssid = uci:get("wireless", "sta0", "ssid") or ""
            write_log("get_connection_settings: no connected STA, defaulting to band=" .. band)
        end
    end

    local wds = uci:get("wireless", sta_iface, "wds") or "0"
    local mode_type = uci:get("wireless", sta_iface, "bssid") and "p2p" or "roaming"

    return {
        band = band,
        ssid = mode == "bridge" and ssid or "", -- 只在bridge模式下返回
        encryption = uci:get("wireless", sta_iface, "encryption") or "none",
        key = uci:get("wireless", sta_iface, "key") or "",
        mode = mode_type,
        peer_mac = uci:get("wireless", sta_iface, "bssid") or "",
        rssi_threshold = uci:get("wireless", sta_iface, "rssi_threshold") or "-70",
        status = get_sta_status(wwan_iface)
    }
end

function get_connection_type()
    local sta_iface = "sta0"
    if uci:get("wireless", "sta1", "disabled") == "0" then
        sta_iface = "sta1"
    elseif uci:get("wireless", "sta3", "disabled") == "0" then
        sta_iface = "sta3"
    end
    -- 默认 universal
    if uci:get("wireless", sta_iface, "wds") == "1" then
        return "wds"
    else
        return "universal"
    end
end

-- 设置内网配置
function set_lan_settings(data)
    local param = data or {}
    write_log("Setting LAN settings: " .. cjson.encode(param))

    -- 将 WAN 和 LAN 桥接到 br-lan
    uci:set("network", "lan", "type", "bridge")
    uci:set("network", "wan", "proto", "none") -- 禁用 WAN 的独立协议
    uci:set("network", "wan6", "proto", "none") -- 禁用 WAN6 的独立协议

    -- 检查 param.ip_allocation 是否存在且有效
    if param.ip_allocation == "static" then
        if not is_valid_ip(param.ip) or not is_valid_netmask(param.netmask) or
           not is_valid_gateway(param.gateway) or not is_valid_dns(param.dns) then
            local error_message = "Invalid IP, netmask, gateway or DNS for static allocation"
            write_log(error_message)
            io.write(cjson.encode({
                module = "bridge_mode",
                version = "1.0",
                api = "set",
                errcode = 7,
                result = { message = error_message }
            }))
            return
        end
        uci:set("network", "lan", "proto", "static")
        uci:set("network", "lan", "ipaddr", param.ip or "*************")
        local netmask = (param.netmask and param.netmask ~= "") and param.netmask or "*************"
        uci:set("network", "lan", "netmask", netmask)
        if param.gateway and param.gateway ~= "" then
            uci:set("network", "lan", "gateway", param.gateway)
        else
            uci:delete("network", "lan", "gateway")
        end
        if param.dns and param.dns ~= "" then
            uci:set("network", "lan", "dns", param.dns)
        else
            uci:delete("network", "lan", "dns")
        end
    elseif param.ip_allocation == "dhcp" then
        -- 显式指定 DHCP 时，切换到 DHCP 模式
        uci:set("network", "lan", "proto", "dhcp")
        uci:delete("network", "lan", "ipaddr")
        uci:delete("network", "lan", "netmask")
        uci:delete("network", "lan", "gateway")
        uci:delete("network", "lan", "dns")
    else
        -- 如果未提供 ip_allocation，保留默认静态 IP
        uci:set("network", "lan", "proto", "static")
        uci:set("network", "lan", "ipaddr", "*************")
        local netmask = (param.netmask and param.netmask ~= "") and param.netmask or "*************"
        uci:set("network", "lan", "netmask", netmask)
        if param.gateway and param.gateway ~= "" then
            uci:set("network", "lan", "gateway", param.gateway)
        else
            uci:delete("network", "lan", "gateway")
        end
        if param.dns and param.dns ~= "" then
            uci:set("network", "lan", "dns", param.dns)
        else
            uci:delete("network", "lan", "dns")
        end
    end

    -- 恢复 def_ifname
    uci:set("network", "lan", "def_ifname", "eth1.1")
    uci:set("network", "wan", "def_ifname", "eth1.2")
    uci:set("network", "wan6", "def_ifname", "eth1.2")

    -- 禁用设备 DHCP 服务（桥接模式下由上级路由器分配）
    uci:set("dhcp", "lan", "ignore", "1")
    uci:set("dhcp", "lan", "start", "100")
    uci:set("dhcp", "lan", "limit", "150")
    uci:set("dhcp", "lan", "leasetime", "12h")

    -- 合并 VLAN，所有 LAN 口统一到 eth1.1
    uci:foreach("network", "switch_vlan", function(s)
        uci:delete("network", s[".name"])
        write_log("Deleted VLAN: " .. s[".name"])
    end)
    uci:set("network", "switch_vlan1", "switch_vlan")
    uci:set("network", "switch_vlan1", "device", "switch1")
    uci:set("network", "switch_vlan1", "vlan", "1")
    uci:set("network", "switch_vlan1", "ports", "0t 1 2 3 4")

    -- 提交更改
    uci:commit("network")
    uci:commit("dhcp")
end

-- 获取内网配置
function get_lan_settings()
    local proto = uci:get("network", "lan", "proto") or "dhcp"
    local ip = uci:get("network", "lan", "ipaddr") or ""
    local netmask = uci:get("network", "lan", "netmask") or ""

    -- 如果 UCI 获取的 IP 或 netmask 为空，尝试通过 ifconfig 获取
    if ip == "" or netmask == "" then
        write_log("UCI IP or netmask is empty, trying to get from ifconfig br-lan")
        local ifconfig_output = io.popen("ifconfig br-lan 2>/dev/null"):read("*a") or ""
        if ifconfig_output ~= "" then
            -- 提取 inet addr 和 Mask
            local ifconfig_ip = ifconfig_output:match("inet addr:([%d%.]+)")
            local ifconfig_mask = ifconfig_output:match("Mask:([%d%.]+)")
            if ifconfig_ip and ifconfig_ip ~= "" then
                ip = ifconfig_ip
                write_log("Got IP from ifconfig: " .. ip)
            end
            if ifconfig_mask and ifconfig_mask ~= "" then
                netmask = ifconfig_mask
                write_log("Got netmask from ifconfig: " .. netmask)
            end
        end
    end

    -- 如果仍然获取不到 IP 和 netmask，使用默认值
    if ip == "" then
        ip = "*************"
        write_log("Using default IP: " .. ip)
    end
    if netmask == "" then
        netmask = "*************"
        write_log("Using default netmask: " .. netmask)
    end

    return {
        ip_allocation = proto,
        ip = ip,
        netmask = netmask,
        gateway = proto == "static" and (uci:get("network", "lan", "gateway") or "") or "-",
        dns = proto == "static" and (uci:get("network", "lan", "dns") or "") or ""
    }
end

-- 设置 WiFi
function set_wifi(wifi_param)
    local bands = {
        {device = "wifi0", iface = "wlan0", band = "2.4G", ifname = "ath0"},
        {device = "wifi1", iface = "wlan10", band = "5G", ifname = "ath10"}
        -- {device = "wifi2", iface = "wlan30", band = "6G", ifname = "ath30"}
        -- 暂时不使用6G
    }
    local encryption_map = {
        ["WPA2 PSK (CCMP)"] = "psk2",
        ["WPA2 PSK (TKIP, CCMP)"] = "psk2",
        ["WPA PSK (TKIP)"] = "psk",
        ["mixed WPA/WPA2 PSK (CCMP)"] = "psk-mixed",
        ["mixed WPA/WPA2 PSK (TKIP, CCMP)"] = "psk-mixed",
        ["WPA3 SAE"] = "sae",
        ["WPA2/WPA3 SAE"] = "sae-mixed",
        ["none"] = "none",
        ["wpa3"] = "sae",
        ["wpa2/wpa3"] = "sae-mixed"
    }
    -- 动态提取后缀
    local ssid_suffix = uci:get("wireless", "wlan0", "ssid") or "3onedata-0-016d"
    ssid_suffix = ssid_suffix:match(".-(%w+)$") or "016d"
    write_log("Using SSID suffix: " .. ssid_suffix)
    
    for _, band in ipairs(bands) do
        local dev = band.device
        local iface = band.iface
        local ifname = band.ifname
        local param = wifi_param[iface]
        
        if param then
            uci:set("wireless", dev, "channel", param.channel or "auto")
            uci:set("wireless", dev, "txpower", param.txpower or "auto")
            uci:set("wireless", dev, "country", param.country or "CN")
            uci:set("wireless", dev, "htmode", param.bandwidth or "auto")
            uci:set("wireless", dev, "disabled", "0")

            uci:set("wireless", iface, "device", dev)
            uci:set("wireless", iface, "mode", "ap")
            uci:set("wireless", iface, "network", "lan")
            local ssid = param.ssid or ("3onedata-0-" .. band.band .. "-" .. ssid_suffix)
            uci:set("wireless", iface, "ssid", ssid)
            local enc = param.encryption or "none"
            local mapped_enc = encryption_map[enc] or enc
            uci:set("wireless", iface, "encryption", mapped_enc)
            -- 设置 sae 选项
            if mapped_enc == "sae" or mapped_enc == "sae-mixed" then
                uci:set("wireless", iface, "sae", "1")
            else
                uci:set("wireless", iface, "sae", "0")
            end
            if mapped_enc ~= "none" then
                uci:set("wireless", iface, "key", param.key or "")
                uci:set("wireless", iface, "cipher", param.cipher or "aes")
                if mapped_enc == "sae" or mapped_enc == "sae-mixed" then
                    uci:set("wireless", iface, "sae", "1")
                    uci:set("wireless", iface, "ieee80211w", "2")
                else
                    uci:delete("wireless", iface, "sae")
                    uci:delete("wireless", iface, "ieee80211w")
                end
            else
                uci:delete("wireless", iface, "key")
                uci:delete("wireless", iface, "cipher")
                uci:delete("wireless", iface, "sae")
                uci:delete("wireless", iface, "ieee80211w")
            end
            uci:set("wireless", iface, "ifname", ifname)
            -- 匹配 3onedata-<数字>- 或 3onedata-<数字>-<band>-
            if ssid:match("^3onedata%-%d+%-") or ssid:match("^3onedata%-%d%-%w+%-") then
                uci:set("wireless", iface, "disabled", "0")
                write_log("Enabled VAP " .. iface .. " with SSID " .. ssid)
            else
                uci:set("wireless", iface, "disabled", "1")
                write_log("Disabled VAP " .. iface .. " with SSID " .. ssid .. " (not matching 3onedata-%d- or 3onedata-%d-%w+-)")
            end
            uci:delete("wireless", iface, "blockdfschan")
            uci:delete("wireless", iface, "vap_ind")
        else
            uci:set("wireless", iface, "disabled", "1")
            write_log("Disabled VAP " .. iface .. " (not specified in wifi_param)")
        end
    end
    uci:commit("wireless")
end

-- 获取 WiFi
function get_wifi()
    local result = {}
    local bands = {
        {device = "wifi0", iface = "wlan0", band = "2.4G"},
        {device = "wifi1", iface = "wlan10", band = "5G"},
        {device = "wifi2", iface = "wlan30", band = "6G"}
    }
    for _, band in ipairs(bands) do
        local dev = band.device
        local iface = band.iface
        result[iface] = {
            band = band.band,
            ssid = uci:get("wireless", iface, "ssid") or "",
            encryption = uci:get("wireless", iface, "encryption") or "none",
            cipher = uci:get("wireless", iface, "cipher") or "aes",
            key = uci:get("wireless", iface, "key") or "",
            channel = uci:get("wireless", dev, "channel") or "auto",
            txpower = uci:get("wireless", dev, "txpower") or "auto",
            country = uci:get("wireless", dev, "country") or "CN",
            bandwidth = uci:get("wireless", dev, "htmode") or "auto"
        }
    end
    return result
end

-- 扫描 SSID（新 API）
function scan_ssid(data)
    local param = data.param or {}
    local band = param.band or "2.4G"
    local interface

    if band == "2.4G" then
        interface = "ath0"
    elseif band == "5G" then
        interface = "ath10"
    elseif band == "6G" then
        interface = "ath30"
    else
        local error_message = "Invalid band for scanning: " .. band
        write_log(error_message)
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            api = "scan_ssid",
            errcode = 8,
            sid = data.sid,
            result = { message = error_message }
        }))
        return
    end

    local scan_result = sys.exec("iwinfo " .. interface .. " scan")
    if not scan_result or scan_result == "" then
        local error_message = "Failed to scan SSIDs on " .. band
        write_log(error_message)
        io.write(cjson.encode({
            module = "bridge_mode",
            version = "1.0",
            api = "scan_ssid",
            errcode = 9,
            sid = data.sid,
            result = { message = error_message }
        }))
        return
    end

    local ssid_list = {}
    local current_ap = nil

    for line in scan_result:gmatch("[^\n]+") do
        local address = line:match("Cell %d+ %- Address: ([%w:]+)")
        local essid = line:match("ESSID: \"(.+)\"")
        local signal = line:match("Signal: (%-%d+) dBm")
        local encryption = line:match("Encryption: (.+)")
        local channel = line:match("Channel: (%d+)")  -- 提取信道
    
        if address then
            if current_ap then
                table.insert(ssid_list, current_ap)
            end
            current_ap = { address = address }
        elseif essid and current_ap then
            current_ap.ssid = essid
        elseif signal and current_ap then
            current_ap.signal = signal
        elseif encryption and current_ap then
            current_ap.encryption = encryption
        elseif channel and current_ap then
            current_ap.channel = channel
        end
    end
    if current_ap then
        table.insert(ssid_list, current_ap)
    end

    write_log("SSID scan completed for " .. band .. ": " .. cjson.encode(ssid_list))
    io.write(cjson.encode({
        module = "bridge_mode",
        version = "1.0",
        api = "scan_ssid",
        errcode = 0,
        sid = data.sid,
        result = { band = band, ssids = ssid_list }
    }))
end

-- 检查是否在CGI环境中运行
local function is_cgi()
    return os.getenv("REQUEST_METHOD") ~= nil
end

-- AC 调用接口
local M = {}

function M.set_config_from_ac(payload)
    write_log("[AC] set_config_from_ac called: " .. cjson.encode(payload))

    -- 设置系统模式为Bridge
    uci:set("system", "nhx", "mode", "bridge")

    -- 处理连接设置
    if payload.connection then
        set_connection_settings(payload.connection)
    end

    -- 处理内网设置
    if payload.lan then
        set_lan_settings(payload.lan)
    end

    -- 处理WiFi设置
    if payload.wifi then
        -- 处理2.4G/5G/6G WiFi配置
        write_log("[AC] WiFi configuration in Bridge mode: " .. cjson.encode(payload.wifi))
    end

    uci:save("system")
    uci:commit("system")
    uci:save("network")
    uci:commit("network")
    uci:save("wireless")
    uci:commit("wireless")

    write_log("[AC] Bridge mode configuration applied")
    return true, "Bridge mode configuration applied by AC"
end

function M.get_config_for_ac()
    local config = {
        mode = "bridge",
        connection = get_connection_settings(),
        lan = get_lan_settings(),
        wifi = {
            wifi_2_4g = get_wifi_2_4g_settings(),
            wifi_5g = get_wifi_5g_settings(),
            wifi_6g = get_wifi_6g_settings()
        }
    }
    write_log("[AC] get_config_for_ac: retrieved Bridge mode config")
    return config
end

-- 仅在明确作为 CGI 脚本运行时执行
if arg and arg[0] and arg[0]:match("bridge_mode%.lua") and is_cgi() then
    local function run()
        write_log("Bridge Mode Settings API started")
        route_api()
        write_log("Bridge Mode Settings API finished")
    end
    run()
end

return M