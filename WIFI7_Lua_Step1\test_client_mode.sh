#!/bin/bash

# 测试client_mode.lua的GET请求功能

echo "=== 测试 client_mode.lua GET 请求 ==="

# 设置环境变量模拟CGI环境
export REQUEST_METHOD="GET"
export QUERY_STRING="action=get_status"
export CONTENT_TYPE="application/json"

echo "1. 测试 get_status API (GET 请求)"
echo "REQUEST_METHOD: $REQUEST_METHOD"
echo "QUERY_STRING: $QUERY_STRING"
echo ""

# 执行脚本
echo "执行结果:"
lua WIFI7_Lua_Step1/client_mode.lua

echo ""
echo "=== 测试完成 ==="

# 检查日志
echo ""
echo "=== 检查日志文件 ==="
if [ -f "/tmp/client_mode.log" ]; then
    echo "最近的日志内容:"
    tail -10 /tmp/client_mode.log
else
    echo "日志文件不存在"
fi
